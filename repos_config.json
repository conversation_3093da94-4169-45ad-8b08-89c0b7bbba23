{"papjia_driver": {"rm_ros2": {"url": "****************:papjia/papjia_mobile_manipulator/rm_ros2.git", "branch": "master"}, "OrbbecSDK_ROS2": {"url": "****************:papjia/papjia_driver/OrbbecSDK_ROS2.git", "branch": "main"}}, "papjia_core": {"papjia_bt": {"url": "****************:papjia/papjia_pickplace/papjia_bt.git", "branch": "jazzy"}, "papjia_config_manage": {"url": "****************:papjia/papjia_pickplace/papjia_config_manage.git", "branch": "hand_demo"}, "papjia_control_msgs": {"url": "****************:papjia/papjia_driver/papjia_control_msgs.git", "branch": "master"}, "papjia_msgs": {"url": "****************:papjia/papjia_pickplace/papjia_msgs.git", "branch": "master"}, "papjia_mtc": {"url": "****************:papjia/papjia_pickplace/papjia_mtc.git", "branch": "jazzy"}, "papjia_skill": {"url": "****************:papjia/papjia_pickplace/papjia_skill.git", "branch": "master"}, "papjia_vision": {"url": "****************:papjia/papjia_pickplace/papjia_vision.git", "branch": "ydsb_demo"}, "papjia_stage_manager": {"url": "****************:papjia/papjia_mobile_manipulator/papjia_stage_manager.git", "branch": "hand_demo"}, "papjia_type_trans": {"url": "****************:papjia/papjia_pickplace/papjia_type_trans.git", "branch": "master"}, "papjia_navigation": {"url": "****************:papjia/papjia_mobile_manipulator/papjia_navigation.git", "branch": "ability-dev"}, "papjia_joy_control": {"url": "****************:papjia/papjia_mobile_manipulator/papjia_joy_control.git", "branch": "master"}, "papjia_car_driver": {"url": "****************:papjia/papjia_mobile_manipulator/papjia_car_driver.git", "branch": "master"}}}