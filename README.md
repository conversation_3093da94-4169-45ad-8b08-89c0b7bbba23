# Docker 容器管理系统

这是一个基于 Python 的 Docker 容器管理系统，专门设计用于管理 ROS2 项目的多个 Docker 容器和程序。该系统提供了简单的命令行界面来启动、停止和管理容器及其中运行的程序。

## 功能特点

- 集中管理多个 Docker 容器和程序
- 支持 Docker Compose 服务管理
- 自动化的容器启动顺序控制
- 灵活的命令模板系统
- 终端窗口管理（支持 GNOME Terminal 和 XTerm）
- 容器状态监控
- 自动启动配置

## 安装要求

- Python 3.6+
- Docker
- Docker Compose
- GNOME Terminal（推荐）或 XTerm
- Python 依赖项：
  ```bash
  pip install docker pyyaml click rich
  ```

## 配置文件详解

系统使用 `docker_config.yaml` 进行配置。以下是每个配置部分的详细说明：

### 1. 路径配置 (paths)
```yaml
paths:
  workspace: "/path/to/workspace"  # 项目工作空间的根目录
  docker_compose: "/path/to/docker/compose/dir"  # docker-compose.yml 所在目录
```
- `workspace`: 指定项目的工作空间根目录，用于容器内的路径映射
- `docker_compose`: 指定 docker-compose.yml 文件所在的目录，所有 docker compose 命令都会在这个目录下执行

### 2. 命令模板 (command_templates)
```yaml
command_templates:
  compose:
    # 基础命令模板，用于执行 docker compose 命令
    base: "{command}"
    # 在容器中执行命令的模板（不带交互）
    exec: "docker compose exec {service} {command}"
    # 在容器中执行命令的模板（带交互终端）
    exec_it: "docker compose exec -it {service} {command}"
  container:
    # 容器内命令的基础模板，设置环境变量并执行命令
    base: "bash -ic 'export ROS_DOMAIN_ID=0 && source /workspace/install/setup.bash && {command}'"
```

命令模板在系统中的使用场景：

1. `compose.base` 使用场景：
   - 执行 compose_commands 中定义的命令时使用
   - 例如：执行 `docker compose up` 命令时
   ```yaml
   compose_commands:
     up:
       command: "docker compose up"  # 使用 compose.base 模板
   ```

2. `compose.exec` 使用场景：
   - 在容器中执行非交互式命令时使用
   - 例如：在容器中运行后台服务或执行脚本
   ```yaml
   containers:
     app1:
       programs:
         service:
           command: "run_service.sh"  # 使用 compose.exec 模板
           terminal: false  # 非交互式
   ```

3. `compose.exec_it` 使用场景：
   - 在容器中执行需要交互的命令时使用
   - 例如：打开终端或运行交互式程序
   ```yaml
   containers:
     app1:
       programs:
         shell:
           command: "bash"  # 使用 compose.exec_it 模板
           terminal: true  # 交互式
   ```

4. `container.base` 使用场景：
   - 在容器内执行命令时使用，为命令设置必要的环境
   - 会在执行命令前：
     - 设置 ROS_DOMAIN_ID
     - 加载 ROS2 环境
     - 设置其他必要的环境变量
   - 例如：运行 ROS2 节点或其他需要 ROS2 环境的程序
   ```yaml
   containers:
     app1:
       programs:
         node:
           command: "ros2 run my_package my_node"  # 命令会被 container.base 模板包装
   ```

命令模板的组合使用：

1. 交互式终端中的命令：
   ```
   最终命令 = compose.exec_it + container.base + 用户命令
   ```
   例如，对于配置：
   ```yaml
   programs:
     shell:
       command: "bash"
   ```
   生成的实际命令类似：
   ```bash
   docker compose exec -it service bash -ic 'export ROS_DOMAIN_ID=0 && source /workspace/install/setup.bash && bash'
   ```

2. 非交互式命令：
   ```
   最终命令 = compose.exec + container.base + 用户命令
   ```
   例如，对于配置：
   ```yaml
   programs:
     app:
       command: "run_app.py"
       terminal: false
   ```
   生成的实际命令类似：
   ```bash
   docker compose exec service bash -ic 'export ROS_DOMAIN_ID=0 && source /workspace/install/setup.bash && run_app.py'
   ```

3. Compose 命令：
   ```
   最终命令 = compose.base + 用户命令
   ```
   例如，对于配置：
   ```yaml
   compose_commands:
     up:
       command: "docker compose up"
   ```
   生成的实际命令就是：
   ```bash
   docker compose up
   ```

通过这种模板系统，可以：
1. 确保所有命令在正确的环境中执行
2. 统一管理命令的执行方式
3. 方便地修改或扩展命令的执行环境
4. 保持配置文件的简洁性

### 3. 终端配置 (terminal)
```yaml
terminal:
  preferred: "gnome-terminal"  # 首选终端模拟器
  fallback: "xterm"  # 备选终端模拟器
```
- `preferred`: 指定首选的终端模拟器，系统会优先使用这个终端
- `fallback`: 当首选终端不可用时，使用这个备选终端
- 支持的终端：
  - GNOME Terminal: 推荐，提供更好的用户体验
  - XTerm: 作为备选，确保基本功能可用

### 4. Docker Compose 命令 (compose_commands)
```yaml
compose_commands:
  up:  # 命令名称，用于 start/stop 命令
    command: "docker compose up"  # 实际执行的命令
    auto_start: true  # 是否在 start-all 时自动启动
    terminal: true  # 是否在新终端窗口中运行
    terminal_title: "Docker Compose - Up"  # 终端窗口标题
```
- `command`: 指定要执行的 docker compose 命令
- `auto_start`: 设置为 true 时，该命令会在执行 start-all 时自动运行
- `terminal`: 设置为 true 时，命令会在新的终端窗口中运行
- `terminal_title`: 指定新终端窗口的标题

### 5. 容器配置 (containers)
```yaml
containers:
  hand_demo:  # 服务名称，用于docker compose命令
    container_name: "hand_demo_container"  # 运行时容器名称
    programs:  # 容器内可执行的程序列表
      main:  # 程序配置名称
        command: "run_app"  # 要执行的命令
        auto_start: true  # 是否自动启动
        terminal: true  # 是否在新终端窗口中运行
        terminal_title: "Hand Demo - Main"  # 终端窗口标题
```

容器配置说明：
- 键名（如 `hand_demo`）：作为服务名称，用于 docker compose 命令
- `container_name`：指定运行时的容器名称，必须唯一
- `programs`：定义容器内可执行的程序列表
  - 程序配置：
    - `command`：在容器内要执行的命令
    - `auto_start`：设置为 true 时，程序会在容器启动后自动运行
    - `terminal`：设置为 true 时，程序会在新的终端窗口中运行
    - `terminal_title`：指定新终端窗口的标题

### 配置注意事项

1. 路径配置：
   - 确保路径存在且有正确的访问权限
   - 使用绝对路径以避免问题

2. 命令模板：
   - 可以根据需要自定义环境变量和源文件
   - 确保命令模板中的路径与容器内的实际路径一致

3. 终端配置：
   - 确保配置的终端程序已安装
   - 可以根据个人偏好设置首选终端

4. Compose 命令：
   - 命令名称用于 start/stop 操作，应该具有描述性
   - 可以添加多个 compose 命令以满足不同需求

5. 服务名称和容器名称：
   - 服务名称（键名）必须与 docker-compose.yml 中的服务名称一致
   - 容器名称（container_name）用于运行时标识容器，必须在整个系统中唯一

## 使用方法

### 基本命令

1. 启动所有自动启动的服务：
```bash
./docker_manager.py start-all
```

2. 启动特定服务中的程序：
```bash
./docker_manager.py start service_name program_name
```
例如：
```bash
./docker_manager.py start hand_demo simulation  # 启动手部演示服务中的仿真程序
```

3. 停止特定服务中的程序：
```bash
./docker_manager.py stop service_name program_name
```
例如：
```bash
./docker_manager.py stop hand_demo simulation  # 停止手部演示服务中的仿真程序
```

4. 查看所有服务和程序状态：
```bash
./docker_manager.py status
```

5. 停止所有服务：
```bash
./docker_manager.py stop-all
```
这会关闭所有终端窗口并停止所有 Docker Compose 服务。

### 启动顺序

系统会按照以下顺序启动服务：

1. 启动 Docker Compose 服务（如果配置为自动启动）
2. 等待 Docker Compose 服务就绪
   - 实时显示每个服务的启动状态
   - 当服务进入运行状态时显示就绪提示
3. 启动配置为自动启动的程序
   - 在新的终端窗口中启动需要终端的程序
   - 在后台启动不需要终端的程序

### 停止操作

系统执行停止操作时会按照以下顺序：

1. 关闭容器程序的终端窗口
   - 关闭所有运行中的程序终端
   - 保留 docker compose up 的终端

2. 执行 `docker compose down`
   - 停止所有 Docker Compose 服务
   - 清理相关的容器和网络资源

3. 关闭 compose up 的终端窗口
   - 等待 compose down 完成
   - 关闭最后的终端窗口

这种停止顺序确保了：
- 程序能够正常退出
- Docker 服务能够正常停止
- 终端窗口能够正确关闭

输出示例：
```
正在关闭容器程序终端...
正在停止docker compose...
成功停止所有服务
正在关闭compose终端...
```

## 配置示例

```yaml
# 完整的配置示例
paths:
  workspace: "/home/<USER>/workspace"
  docker_compose: "/home/<USER>/workspace/docker"

command_templates:
  compose:
    base: "{command}"
    exec: "docker compose exec {service} {command}"
    exec_it: "docker compose exec -it {service} {command}"
  container:
    base: "bash -ic 'export ROS_DOMAIN_ID=0 && source /workspace/install/setup.bash && {command}'"

terminal:
  preferred: "gnome-terminal"
  fallback: "xterm"

compose_commands:
  up:
    command: "docker compose up"
    auto_start: true
    terminal: true
    terminal_title: "Docker Compose - Up"

containers:
  app1:
    container_name: app1_container
    programs:
      main:
        command: "run_app"
        auto_start: true
        terminal: true
        terminal_title: "App1 - Main"
      shell:
        command: "bash"
        auto_start: false
        terminal: true
        terminal_title: "App1 - Shell"
```