#!/usr/bin/env python3
"""
Docker 容器管理系统
用于管理 ROS2 项目的多个 Docker 容器和程序的命令行工具。

主要功能：
1. 容器生命周期管理
   - 启动和停止 Docker Compose 服务
   - 监控容器状态
   - 自动化服务依赖管理

2. 终端管理
   - 在新终端窗口中启动程序
   - 支持 GNOME Terminal 和 XTerm
   - 可靠的进程跟踪和终止

3. 配置管理
   - 基于 YAML 的配置文件
   - 灵活的命令模板系统
   - 支持自动启动配置

使用方法：
1. start-all: 启动所有服务和自动启动的程序
2. stop-all: 停止所有服务和程序
3. start <container> <program>: 启动特定容器中的程序
4. stop <container> <program>: 停止特定容器中的程序
5. status: 显示所有容器和程序的状态
"""

import os
import yaml
import docker
import time
from docker import DockerClient
import click
import subprocess
from rich.console import Console
from rich.table import Table
from typing import Dict, Any
import sys

console = Console()

def check_docker_version():
    """检查 Docker SDK 版本并确保兼容性"""
    try:
        import pkg_resources
        docker_version = pkg_resources.get_distribution('docker').version
        console.print(f"[cyan]当前 Docker SDK 版本: {docker_version}[/cyan]")
        
        # 如果版本低于 6.0.0，建议升级
        if pkg_resources.parse_version(docker_version) < pkg_resources.parse_version('6.0.0'):
            console.print("[yellow]警告: Docker SDK 版本过低，建议升级到 6.0.0 或更高版本[/yellow]")
            console.print("[yellow]可以使用以下命令升级: pip install --upgrade 'docker>=6.0.0'[/yellow]")
            return False
        return True
    except Exception as e:
        console.print(f"[red]检查 Docker SDK 版本时出错: {str(e)}[/red]")
        return False

class DockerManager:
    """Docker 容器管理器
    
    负责管理多个 Docker 容器和其中运行的程序，提供以下功能：
    - 容器的启动和停止
    - 程序的启动和停止
    - 终端窗口管理
    - 容器状态监控
    
    属性:
        config_path (str): 配置文件路径
        client (DockerClient): Docker客户端实例
        running_containers (dict): 跟踪运行中的容器
        terminal_processes (dict): 跟踪终端进程信息
        temp_dir (str): 临时文件目录路径
    """

    def __init__(self, config_path: str = "docker_config.yaml"):
        """初始化 Docker 管理器
        
        Args:
            config_path: 配置文件路径，默认为 "docker_config.yaml"
            
        初始化过程：
        1. 加载配置文件
        2. 初始化Docker客户端
        3. 创建临时文件目录
        4. 初始化状态追踪字典
        """
        self.config_path = config_path
        self.load_config()
        
        # 检查 Docker SDK 版本
        if not check_docker_version():
            console.print("[yellow]继续尝试初始化 Docker 客户端...[/yellow]")
        
        try:
            # 直接使用 Unix socket
            self.client = DockerClient(base_url='unix://var/run/docker.sock')
        except Exception as e:
            console.print(f"[red]Docker 客户端初始化失败: {str(e)}[/red]")
            console.print("[yellow]请确保 Docker daemon 正在运行，并且您有权限访问它[/yellow]")
            sys.exit(1)
        
        self.running_containers = {}  # 跟踪运行中的容器
        self.terminal_processes = {}  # 跟踪终端进程 {title: process_info}
        
        # 创建临时目录用于存储进程信息和脚本
        self.temp_dir = "/tmp/docker_manager"
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)

    def load_config(self):
        """加载并验证配置文件
        
        从YAML文件加载配置并验证必需的配置部分是否存在：
        - paths: 路径配置
        - command_templates: 命令模板
        - terminal: 终端配置
        - containers: 容器配置
        
        Raises:
            ValueError: 如果缺少必需的配置部分
        """
        with open(self.config_path, 'r') as f:
            self.config = yaml.safe_load(f)
            
        required_sections = ['paths', 'command_templates', 'terminal', 'containers']
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Missing required configuration section: {section}")

    def wait_for_containers_ready(self, timeout=60):
        """等待所有容器准备就绪
        
        检查每个服务的状态，直到所有服务都处于运行状态或超时。
        
        Args:
            timeout: 等待超时时间（秒），默认60秒
            
        Returns:
            bool: 是否所有容器都已就绪
            
        过程：
        1. 循环检查每个服务的状态
        2. 使用docker compose ps命令获取状态
        3. 验证所有服务是否都在运行
        4. 如果超时则返回False
        """
        console.print("[yellow]等待容器准备就绪...[/yellow]")
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # 检查每个服务的状态
                docker_compose_dir = self.config['paths']['docker_compose']
                all_running = True
                
                for service_name in self.running_containers:
                    if service_name == "docker_compose":
                        continue
                        
                    status_cmd = f"cd {docker_compose_dir} && docker compose ps {service_name} --format '{{{{.State}}}}'"
                    result = subprocess.run(
                        status_cmd,
                        shell=True,
                        capture_output=True,
                        text=True
                    )
                    
                    if result.returncode != 0:
                        console.print(f"[yellow]无法获取服务 {service_name} 的状态[/yellow]")
                        all_running = False
                        break
                        
                    if 'running' not in result.stdout.lower():
                        console.print(f"[yellow]服务 {service_name} 状态: {result.stdout.strip()}[/yellow]")
                        all_running = False
                        break
                
                if all_running:
                    console.print("[green]所有服务已就绪[/green]")
                    return True
                    
            except Exception as e:
                console.print(f"[red]检查服务状态时出错: {str(e)}[/red]")
                
            time.sleep(2)
            
        console.print("[red]等待服务就绪超时[/red]")
        return False

    def wait_for_compose_ready(self, timeout=60):
        """等待docker compose服务就绪
        
        监控docker compose服务的启动过程，直到所有服务就绪或超时。
        
        Args:
            timeout: 等待超时时间（秒），默认60秒
            
        Returns:
            bool: 是否所有compose服务都已就绪
            
        过程：
        1. 定期检查所有服务状态
        2. 记录并报告每个服务的状态变化
        3. 当所有服务运行时返回True
        4. 超时时返回False
        """
        console.print("[yellow]等待docker compose服务启动...[/yellow]")
        start_time = time.time()
        running_services = set()  # 记录已经运行的服务
        
        while time.time() - start_time < timeout:
            try:
                # 检查docker compose服务是否在运行
                docker_compose_dir = self.config['paths']['docker_compose']
                result = subprocess.run(
                    f"cd {docker_compose_dir} && docker compose ps --format '{{{{.Service}}}}\t{{{{.State}}}}'",
                    shell=True,
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    # 解析每个服务的状态
                    all_running = True
                    services_status = {}
                    
                    for line in result.stdout.strip().split('\n'):
                        if line:
                            service, state = line.split('\t')
                            services_status[service] = state
                            
                            # 如果服务正在运行且之前未报告
                            if 'running' in state.lower() and service not in running_services:
                                console.print(f"[green]服务 {service} 已就绪[/green]")
                                running_services.add(service)
                            # 如果服务不在运行状态
                            elif 'running' not in state.lower():
                                all_running = False
                                if service not in running_services:  # 只显示未报告过的服务状态
                                    console.print(f"[yellow]服务 {service} 状态: {state}[/yellow]")
                    
                    # 所有服务都在运行
                    if all_running and services_status:
                        console.print("[green]所有Docker compose服务已就绪[/green]")
                        return True
                    
            except Exception as e:
                console.print(f"[red]检查compose状态时出错: {str(e)}[/red]")
                
            time.sleep(2)
            
        console.print("[red]等待docker compose服务超时[/red]")
        return False

    def get_command_template(self, template_type: str, template_name: str = 'base') -> str:
        """获取命令模板
        
        从配置中获取指定类型和名称的命令模板。
        
        Args:
            template_type: 'compose' 或 'container'
            template_name: 模板名称，默认为'base'
            
        Returns:
            str: 命令模板字符串
        """
        return self.config['command_templates'][template_type][template_name]

    def format_command(self, command: str, template_type: str, template_name: str = 'base', **kwargs) -> str:
        """使用模板格式化命令
        
        将命令应用到指定的模板中，并填充所有必要的参数。
        
        Args:
            command: 要执行的命令
            template_type: 'compose' 或 'container'
            template_name: 模板名称，默认为'base'
            **kwargs: 其他模板参数
            
        Returns:
            str: 格式化后的命令
        """
        template = self.get_command_template(template_type, template_name)
        return template.format(command=command, **kwargs)

    def get_container(self, container_name: str):
        """获取容器实例
        
        根据容器名称获取Docker容器实例。
        
        Args:
            container_name: 容器名称
            
        Returns:
            Container: Docker容器实例，如果不存在则返回None
            
        特殊情况：
        - 如果container_name为"docker_compose"，返回True
        - 如果容器不存在，返回None
        """
        if container_name == "docker_compose":
            return True  # docker_compose是特殊情况，不需要获取容器实例
        try:
            container_config = self.config['containers'][container_name]
            return self.client.containers.get(container_config['name'])
        except docker.errors.NotFound:
            return None
            
    def start_container(self, service_name: str) -> bool:
        """启动容器
        
        启动指定的Docker服务，并等待其就绪。
        
        Args:
            service_name: 服务名称（配置文件中containers下的键名）
            
        Returns:
            bool: 容器是否成功启动
            
        过程：
        1. 检查服务是否已在运行
        2. 如果未运行，则启动服务
        3. 等待服务进入运行状态
        4. 返回启动结果
        """
        if service_name == "docker_compose":
            return True  # docker_compose不需要启动容器
            
        # 如果容器已经在运行，直接返回
        if service_name in self.running_containers:
            return True
            
        container_config = self.config['containers'].get(service_name)
        if not container_config:
            console.print(f"[red]Service {service_name} not found in config[/red]")
            return False
            
        try:
            # 检查服务是否已经在运行
            docker_compose_dir = self.config['paths']['docker_compose']
            status_cmd = f"cd {docker_compose_dir} && docker compose ps {service_name} --format '{{{{.State}}}}'"
            result = subprocess.run(
                status_cmd,
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0 and 'running' in result.stdout.lower():
                console.print(f"[green]Service {service_name} is already running[/green]")
                self.running_containers[service_name] = True
                return True
                
            # 启动服务
            console.print(f"[yellow]Starting service {service_name}...[/yellow]")
            start_cmd = f"cd {docker_compose_dir} && docker compose up -d {service_name}"
            result = subprocess.run(
                start_cmd,
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                console.print(f"[red]Failed to start service {service_name}:[/red]")
                console.print(f"[red]Error: {result.stderr}[/red]")
                return False
                
            # 等待服务启动
            for _ in range(30):  # 最多等待30秒
                status_result = subprocess.run(
                    status_cmd,
                    shell=True,
                    capture_output=True,
                    text=True
                )
                
                if status_result.returncode == 0 and 'running' in status_result.stdout.lower():
                    console.print(f"[green]Successfully started service {service_name}[/green]")
                    self.running_containers[service_name] = True
                    return True
                    
                time.sleep(1)
                
            console.print(f"[red]Timeout waiting for service {service_name} to start[/red]")
            return False
            
        except Exception as e:
            console.print(f"[red]Failed to start service {service_name}: {str(e)}[/red]")
            return False

    def create_terminal_command(self, program_config: dict, service_name: str = None) -> str:
        """创建在新终端中运行程序的命令
        
        根据程序配置创建终端命令，支持在新终端窗口中启动程序。
        
        Args:
            program_config: 程序配置字典
            service_name: 服务名称，如果是compose命令则为None
            
        Returns:
            str: 完整的终端命令
        """
        raw_command = program_config['command']
        docker_compose_dir = self.config['paths']['docker_compose']
        
        if service_name is None:
            # Compose命令: compose.base + 用户命令
            formatted_command = self.format_command(raw_command, 'compose')
            cmd = f"cd {docker_compose_dir} && {formatted_command}"
        else:
            # 容器命令: compose.exec_it + container.base + 用户命令
            container_cmd = self.format_command(raw_command, 'container')
            exec_cmd = self.format_command(container_cmd, 'compose', 'exec_it', service=service_name)
            cmd = f"cd {docker_compose_dir} && {exec_cmd}"
            
        title = program_config.get('terminal_title', f"{service_name or 'Compose'} - {raw_command}")
        
        terminal_config = self.config['terminal']
        try:
            subprocess.run(['which', terminal_config['preferred']], check=True, capture_output=True)
            terminal_cmd = terminal_config['preferred']
        except subprocess.CalledProcessError:
            console.print(f"[yellow]{terminal_config['preferred']} not found, falling back to {terminal_config['fallback']}[/yellow]")
            terminal_cmd = terminal_config['fallback']

        # 创建一个唯一的标识符（避免使用空格）
        unique_id = f"term_{int(time.time())}_{os.getpid()}"
        pid_file = os.path.join(self.temp_dir, f"{unique_id}.pid")
        script_file = os.path.join(self.temp_dir, f"{unique_id}.sh")
        
        # 构建内部命令
        inner_cmd = f"""#!/usr/bin/env bash
# 保存PID
echo "$$" > "{pid_file}"

# 设置错误处理
set -e

# 执行主命令
{cmd}

# 如果命令失败，保持终端打开
if [ $? -ne 0 ]; then
    echo "Command failed. Press Enter to close..."
    read
fi
"""
        
        # 保存命令到临时文件
        try:
            with open(script_file, 'w') as f:
                f.write(inner_cmd)
            os.chmod(script_file, 0o755)  # 使脚本可执行
            
            # 构建启动命令
            if terminal_cmd == 'gnome-terminal':
                launch_cmd = f"{terminal_cmd} --title=\"{title}\" -- \"{script_file}\""
            else:
                launch_cmd = f"{terminal_cmd} -title \"{title}\" -e \"{script_file}\""

            # 启动终端
            process = subprocess.Popen(launch_cmd, shell=True)
            time.sleep(1)  # 给终端一点时间来创建PID文件
            
            # 读取PID文件
            try:
                with open(pid_file, 'r') as f:
                    pid = f.read().strip()
                    process_info = {
                        'pid': pid,
                        'unique_id': unique_id,
                        'script_file': script_file,
                        'pid_file': pid_file,
                        'title': title
                    }
                    self.terminal_processes[title] = process_info
                    
                    # 保存进程信息到持久文件
                    process_info_file = os.path.join(self.temp_dir, f"{unique_id}.info")
                    with open(process_info_file, 'w') as f:
                        yaml.dump(process_info, f)
                        
                    console.print(f"[green]Started terminal {title} with PID {pid}[/green]")
            except FileNotFoundError:
                console.print(f"[yellow]Warning: Could not read PID file for {title}[/yellow]")
                
        except Exception as e:
            console.print(f"[red]Failed to start terminal {title}: {str(e)}[/red]")
            # 清理文件
            try:
                if os.path.exists(script_file):
                    os.remove(script_file)
                if os.path.exists(pid_file):
                    os.remove(pid_file)
            except Exception as cleanup_error:
                console.print(f"[yellow]Warning: Failed to clean up temporary files: {str(cleanup_error)}[/yellow]")

        return ""  # 返回空字符串，因为我们已经启动了终端

    def exec_command(self, command: str, program_config: dict = None) -> bool:
        """执行compose命令
        
        在docker compose环境中执行命令。
        
        Args:
            command: 要执行的命令
            program_config: 程序配置字典，可选
            
        Returns:
            bool: 命令是否执行成功
            
        特性：
        - 支持在新终端中执行
        - 处理命令执行错误
        - 返回执行状态
        """
        try:
            if program_config and program_config.get('terminal', False):
                terminal_cmd = self.create_terminal_command(program_config)
                subprocess.Popen(terminal_cmd, shell=True)
            else:
                docker_compose_dir = self.config['paths']['docker_compose']
                formatted_command = self.format_command(command, 'compose')
                cmd = f"cd {docker_compose_dir} && {formatted_command}"
                subprocess.Popen(cmd, shell=True)
            return True
        except Exception as e:
            console.print(f"[red]Failed to execute command: {str(e)}[/red]")
            return False

    def exec_in_container(self, service_name: str, command: str, program_config: dict = None) -> bool:
        """在容器中执行命令
        
        在指定的容器中执行命令。
        
        Args:
            service_name: 服务名称
            command: 要执行的命令
            program_config: 程序配置字典，可选
            
        Returns:
            bool: 命令是否执行成功
        """
        container_config = self.config['containers'].get(service_name)
        if not container_config:
            console.print(f"[red]Service {service_name} not found in config[/red]")
            return False
            
        container_name = container_config['container_name']
        
        try:
            # 先检查容器状态
            docker_compose_dir = self.config['paths']['docker_compose']
            status_cmd = f"cd {docker_compose_dir} && docker compose ps {service_name} --format '{{{{.State}}}}'"
            result = subprocess.run(
                status_cmd,
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                console.print(f"[red]Failed to get status for service {service_name}: {result.stderr}[/red]")
                return False
                
            if 'running' not in result.stdout.lower():
                console.print(f"[red]Service {service_name} is not running (Status: {result.stdout.strip()})[/red]")
                return False
                
            if program_config and program_config.get('terminal', False):
                terminal_cmd = self.create_terminal_command(program_config, service_name)
                try:
                    subprocess.Popen(terminal_cmd, shell=True)
                    console.print(f"[green]Started terminal for {service_name}: {program_config.get('terminal_title', command)}[/green]")
                    return True
                except Exception as e:
                    console.print(f"[red]Failed to start terminal for {service_name}: {str(e)}[/red]")
                    return False
            else:
                # 非交互式命令: compose.exec + container.base + 用户命令
                container_cmd = self.format_command(command, 'container')
                exec_cmd = self.format_command(container_cmd, 'compose', 'exec', service=service_name)
                full_cmd = f"cd {docker_compose_dir} && {exec_cmd}"
                
                console.print(f"[yellow]Executing command in {service_name}...[/yellow]")
                result = subprocess.run(
                    full_cmd,
                    shell=True,
                    capture_output=True,
                    text=True
                )
                
                if result.returncode != 0:
                    console.print(f"[red]Command failed in {service_name}:[/red]")
                    console.print(f"[red]Command: {full_cmd}[/red]")
                    console.print(f"[red]Error: {result.stderr}[/red]")
                    return False
                    
                console.print(f"[green]Successfully executed command in {service_name}[/green]")
                return True
                
        except Exception as e:
            console.print(f"[red]Failed to execute command in {service_name}: {str(e)}[/red]")
            return False

    def start_program(self, container_name: str, program_name: str):
        """启动程序
        
        在指定容器中启动程序。
        
        Args:
            container_name: 容器名称
            program_name: 程序名称
            
        过程：
        1. 处理compose命令的特殊情况
        2. 验证容器和程序配置
        3. 确保容器已启动
        4. 在容器中启动程序
        """
        if container_name == "docker_compose":
            # 处理compose命令
            program_config = self.config['compose_commands'].get(program_name)
            if not program_config:
                console.print(f"[red]Compose command {program_name} not found in config[/red]")
                return
            if self.exec_command(program_config['command'], program_config):
                console.print(f"[green]Started compose command: {program_name}[/green]")
            return

        # 处理容器中的程序
        container_config = self.config['containers'].get(container_name)
        if not container_config:
            console.print(f"[red]Container {container_name} not found in config[/red]")
            return
            
        program_config = container_config['programs'].get(program_name)
        if not program_config:
            console.print(f"[red]Program {program_name} not found in config[/red]")
            return
            
        if self.start_container(container_name):
            if self.exec_in_container(container_name, program_config['command'], program_config):
                console.print(f"[green]Started program {program_name} in {container_name}[/green]")

    def stop_program(self, container_name: str, program_name: str):
        """停止程序
        
        停止指定容器中的程序。
        
        Args:
            container_name: 容器名称
            program_name: 程序名称
            
        过程：
        1. 验证容器和程序配置
        2. 在容器中执行停止命令
        3. 报告停止结果
        """
        if container_name == "docker_compose":
            console.print("[yellow]Cannot stop docker_compose programs directly[/yellow]")
            return
            
        container_config = self.config['containers'].get(container_name)
        if not container_config:
            console.print(f"[red]Container {container_name} not found in config[/red]")
            return
            
        program_config = container_config['programs'].get(program_name)
        if not program_config:
            console.print(f"[red]Program {program_name} not found in config[/red]")
            return
            
        stop_command = f"pkill -f '{program_config['command']}'"
        if self.exec_in_container(container_name, stop_command):
            console.print(f"[green]Stopped program {program_name} in {container_name}[/green]")
            
    def list_status(self):
        """显示所有容器和程序的状态
        
        创建一个表格显示：
        - 容器名称
        - 容器状态
        - 容器中的程序列表
        - 程序的终端状态
        
        使用rich库创建格式化的表格输出。
        """
        table = Table(title="Docker Containers and Programs Status")
        table.add_column("Container", style="cyan")
        table.add_column("Status", style="magenta")
        table.add_column("Programs", style="green")
        table.add_column("Terminal", style="yellow")
        
        # 遍历所有容器
        for container_name, container_config in self.config['containers'].items():
            # 获取容器状态
            if container_name == "docker_compose":
                status = "Local"
            else:
                container = self.get_container(container_name)
                status = container.status if container else "Not running"
                
            # 获取程序列表
            programs = []
            for prog_name, prog_config in container_config['programs'].items():
                terminal_status = "✓" if prog_config.get('terminal', False) else "✗"
                programs.append(f"{prog_name} [{terminal_status}]")
            programs_str = "\n".join(programs)
            
            # 添加到表格
            table.add_row(container_name, status, programs_str, "")
            
        console.print(table)

    def stop_all_containers(self):
        """停止所有服务
        
        按照以下顺序停止所有服务和程序：
        1. 关闭容器程序的终端窗口
        2. 停止 docker compose 服务
        3. 关闭 compose up 的终端窗口
        
        过程：
        1. 从临时文件恢复进程信息
        2. 关闭所有程序终端
        3. 执行docker compose down
        4. 关闭compose终端
        5. 清理所有临时文件
        """
        # 首先关闭容器程序的终端窗口
        console.print("[yellow]正在关闭容器程序终端...[/yellow]")
        
        # 尝试从临时文件恢复进程信息
        try:
            for filename in os.listdir(self.temp_dir):
                if filename.endswith('.info'):
                    with open(os.path.join(self.temp_dir, filename), 'r') as f:
                        process_info = yaml.safe_load(f)
                        if process_info and 'title' in process_info:
                            self.terminal_processes[process_info['title']] = process_info
        except Exception as e:
            console.print(f"[yellow]Warning: Failed to load process information: {str(e)}[/yellow]")
        
        # 列出所有当前终端进程
        console.print("[cyan]当前终端进程:[/cyan]")
        for title, process_info in self.terminal_processes.items():
            console.print(f"[cyan]- {title} (PID: {process_info['pid']})[/cyan]")
            if "Docker Compose - Up" not in title:
                try:
                    # 确保进程存在
                    if subprocess.run(f"ps -p {process_info['pid']}", shell=True, capture_output=True).returncode == 0:
                        # 先尝试正常终止
                        subprocess.run(f"kill {process_info['pid']}", shell=True, check=True)
                        time.sleep(0.5)
                        # 如果进程还在，强制终止
                        if subprocess.run(f"ps -p {process_info['pid']}", shell=True, capture_output=True).returncode == 0:
                            subprocess.run(f"kill -9 {process_info['pid']}", shell=True, check=True)
                        console.print(f"[green]成功关闭终端: {title}[/green]")
                    else:
                        console.print(f"[yellow]终端进程已不存在: {title}[/yellow]")
                    
                    # 清理临时文件
                    try:
                        for ext in ['.sh', '.pid', '.info']:
                            temp_file = os.path.join(self.temp_dir, f"{process_info['unique_id']}{ext}")
                            if os.path.exists(temp_file):
                                os.remove(temp_file)
                    except Exception as cleanup_error:
                        console.print(f"[yellow]Warning: Failed to clean up temporary files: {str(cleanup_error)}[/yellow]")
                        
                except subprocess.CalledProcessError as e:
                    console.print(f"[red]关闭终端失败 {title}: {str(e)}[/red]")

        # 等待终端关闭
        time.sleep(2)
        
        # 停止docker compose
        console.print("[yellow]正在停止docker compose...[/yellow]")
        try:
            docker_compose_dir = self.config['paths']['docker_compose']
            subprocess.run(
                f"cd {docker_compose_dir} && docker compose down",
                shell=True,
                check=True
            )
            console.print("[green]成功停止所有服务[/green]")
        except subprocess.CalledProcessError as e:
            console.print(f"[red]停止docker compose失败: {str(e)}[/red]")

        # 最后关闭compose终端
        compose_process = None
        for title, process_info in self.terminal_processes.items():
            if "Docker Compose - Up" in title:
                compose_process = process_info
                break

        if compose_process:
            console.print("[yellow]正在关闭compose终端...[/yellow]")
            try:
                time.sleep(2)  # 给一点额外时间确保compose down完成
                if subprocess.run(f"ps -p {compose_process['pid']}", shell=True, capture_output=True).returncode == 0:
                    subprocess.run(f"kill {compose_process['pid']}", shell=True, check=True)
                    time.sleep(0.5)
                    # 如果进程还在，强制终止
                    if subprocess.run(f"ps -p {compose_process['pid']}", shell=True, capture_output=True).returncode == 0:
                        subprocess.run(f"kill -9 {compose_process['pid']}", shell=True, check=True)
                    console.print("[green]成功关闭compose终端[/green]")
                else:
                    console.print("[yellow]Compose终端进程已不存在[/yellow]")
                    
                # 清理临时文件
                try:
                    for ext in ['.sh', '.pid', '.info']:
                        temp_file = os.path.join(self.temp_dir, f"{compose_process['unique_id']}{ext}")
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                except Exception as cleanup_error:
                    console.print(f"[yellow]Warning: Failed to clean up temporary files: {str(cleanup_error)}[/yellow]")
                    
            except subprocess.CalledProcessError as e:
                console.print(f"[red]关闭compose终端失败: {str(e)}[/red]")

        # 清空运行状态
        self.running_containers.clear()
        self.terminal_processes.clear()
        
        # 清理临时目录
        try:
            for filename in os.listdir(self.temp_dir):
                try:
                    os.remove(os.path.join(self.temp_dir, filename))
                except:
                    pass
            os.rmdir(self.temp_dir)
        except:
            pass

    def start_all(self):
        """启动所有自动启动的程序
        
        按照以下顺序启动：
        1. 启动 Docker Compose 服务
        2. 等待 Docker Compose 服务就绪
        3. 启动其他容器
        4. 等待所有容器就绪
        5. 启动配置为自动启动的程序
        
        过程：
        1. 启动基础设施
        2. 等待服务就绪
        3. 启动自动启动的程序
        4. 监控启动过程
        """
        # 首先启动docker compose
        console.print("[yellow]正在启动docker compose...[/yellow]")
        compose_up = self.config['compose_commands'].get('up')
        if compose_up and compose_up.get('auto_start', False):
            self.start_program("docker_compose", "up")
        
        # 等待docker compose就绪
        if not self.wait_for_compose_ready():
            console.print("[red]Docker compose启动失败[/red]")
            return
        
        # 启动其他容器
        for container_name in self.config['containers']:
            self.start_container(container_name)
        
        # 等待容器就绪
        if not self.wait_for_containers_ready():
            console.print("[red]容器启动失败[/red]")
            return
        
        # 启动所有自动启动的程序
        for container_name, container_config in self.config['containers'].items():
            for program_name, program_config in container_config['programs'].items():
                if program_config.get('auto_start', False):
                    self.start_program(container_name, program_name)

# CLI 命令定义
@click.group()
def cli():
    """Docker container and program manager
    
    命令行界面入口点，提供以下命令：
    - start: 启动特定程序
    - stop: 停止特定程序
    - status: 显示系统状态
    - start-all: 启动所有服务
    - stop-all: 停止所有服务
    """
    pass

@click.command()
@click.argument('container_name')
@click.argument('program_name')
def start(container_name, program_name):
    """Start a program in a container
    
    在指定容器中启动程序
    
    Args:
        container_name: 容器名称
        program_name: 程序名称
    """
    manager = DockerManager()
    manager.start_program(container_name, program_name)

@click.command()
@click.argument('container_name')
@click.argument('program_name')
def stop(container_name, program_name):
    """Stop a program in a container
    
    停止指定容器中的程序
    
    Args:
        container_name: 容器名称
        program_name: 程序名称
    """
    manager = DockerManager()
    manager.stop_program(container_name, program_name)

@click.command()
def status():
    """Show status of all containers and programs
    
    显示所有容器和程序的状态
    """
    manager = DockerManager()
    manager.list_status()

@click.command(name='start-all')
def start_all():
    """Start all containers and auto-start programs
    
    启动所有容器和自动启动的程序
    """
    manager = DockerManager()
    manager.start_all()

@click.command(name='stop-all')
def stop_all():
    """Stop all containers and programs
    
    停止所有容器和程序
    """
    manager = DockerManager()
    manager.stop_all_containers()

# 注册命令
cli.add_command(start)
cli.add_command(stop)
cli.add_command(status)
cli.add_command(start_all)
cli.add_command(stop_all)

if __name__ == '__main__':
    cli() 