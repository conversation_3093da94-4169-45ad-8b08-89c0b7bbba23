#!/bin/bash

# 默认值设置为false（真实硬件模式）
USE_MOCK_HARDWARE="false"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --sim)
      USE_MOCK_HARDWARE="true"
      shift
      ;;
    --real)
      USE_MOCK_HARDWARE="false"
      shift
      ;;
    *)
      echo "未知参数: $1"
      echo "用法: $0 [--sim|--real]"
      exit 1
      ;;
  esac
done

echo "运行模式: $([ "$USE_MOCK_HARDWARE" == "true" ] && echo "模拟" || echo "真实硬件")"

source /workspace/install/setup.bash &

ros2 launch papjia_ydsb_config arm.launch.py use_mock_hardware:=$USE_MOCK_HARDWARE &
ros2 launch papjia_ydsb_config bt.launch.py &

wait  # 等待所有后台进程结束

