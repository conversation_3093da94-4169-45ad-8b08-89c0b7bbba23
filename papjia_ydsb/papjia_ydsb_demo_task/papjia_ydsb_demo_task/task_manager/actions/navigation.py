#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rclpy.callback_groups import ReentrantCallbackGroup
from geometry_msgs.msg import Pose, Point, Quaternion, PoseStamped
from papjia_odom_controller.action import Forward, Rotate, NavigateToPose
from nav2_msgs.action import NavigateToPose as Nav2NavigateToPose
import math
from typing import Optional, Tuple
from nav2_msgs.action import DockRobot
import argparse
from typing import Dict, Any, List
from .zmq_client_db import create_client

class OdomControllerClient(Node):
    """里程计控制器客户端，提供简单的函数接口来执行Action"""
    
    _instance = None  # 单例实例
    
    def __init__(self, create_new_node=True):
        if create_new_node:
            super().__init__('odom_controller_client')
        else:
            # 如果不需要创建新节点，使用一个特殊的节点名
            super().__init__('odom_controller_client_existing')
        
        # 创建Action客户端
        self._forward_client = ActionClient(
            self, Forward, 'odom_move/move_forward',
            callback_group=ReentrantCallbackGroup())
        self._rotate_client = ActionClient(
            self, Rotate, 'odom_move/rotate',
            callback_group=ReentrantCallbackGroup())
        self._navigate_client = ActionClient(
            self, NavigateToPose, 'odom_move/navigate_to_pose',
            callback_group=ReentrantCallbackGroup())
        
        
        # Navigation2 Action客户端
        self._nav2_client = ActionClient(
            self, Nav2NavigateToPose, 'navigate_to_pose',
            callback_group=ReentrantCallbackGroup())

        self._dock_client = ActionClient(
            self, DockRobot, 'dock_robot',
             callback_group=ReentrantCallbackGroup())

        self.get_logger().info("OdomControllerClient initialized")

    @classmethod
    def get_instance(cls, node=None):
        """
        获取OdomControllerClient实例
        
        Args:
            node: 现有的ROS2节点，如果提供则使用该节点创建客户端
            
        Returns:
            OdomControllerClient: 客户端实例
        """
        if node is not None:
            # 使用现有节点创建客户端
            client = cls.__new__(cls)
            client._node = node
            client._forward_client = ActionClient(
                node, Forward, 'odom_move/move_forward',
                callback_group=ReentrantCallbackGroup())
            client._rotate_client = ActionClient(
                node, Rotate, 'odom_move/rotate',
                callback_group=ReentrantCallbackGroup())
            client._navigate_client = ActionClient(
                node, NavigateToPose, 'odom_move/navigate_to_pose',
                callback_group=ReentrantCallbackGroup())
            client._nav2_client = ActionClient(
                node, Nav2NavigateToPose, 'navigate_to_pose',
                callback_group=ReentrantCallbackGroup())
            client._dock_client = ActionClient(
                node, DockRobot, 'dock_robot',
                callback_group=ReentrantCallbackGroup())


            client.get_logger = node.get_logger
            client.get_clock = node.get_clock
            client.destroy_node = lambda: None  # 不销毁现有节点
            
            client.get_logger().info("OdomControllerClient initialized with existing node")
            return client
        else:
            # 创建新的客户端实例
            return cls(create_new_node=True)

    def wait_for_servers(self):
        """等待所有Action服务器就绪"""
        self.get_logger().info("Waiting for action servers...")
        
        # self._forward_client.wait_for_server()
        # self._rotate_client.wait_for_server()
        # self._navigate_client.wait_for_server()
        # self._dock_client.wait_for_server()
        # self._nav2_client.wait_for_server()
        
        self.get_logger().info("All action servers are ready!")

    def move_forward(self, distance: float, timeout: float = 60.0) -> bool:
        """
        前进指定距离
        
        Args:
            distance: 前进距离（米），正数前进，负数后退
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功完成
        """
        if not self._forward_client.server_is_ready():
            self.get_logger().error("Forward action server not ready")
            return False
        
        goal = Forward.Goal()
        goal.distance = distance
        
        self.get_logger().info(f"Sending forward goal: {distance:.2f} meters")
        
        try:
            # 发送goal并获取future
            future = self._forward_client.send_goal_async(goal)
            
            # 等待goal完成
            if hasattr(self, '_node'):
                # 使用现有节点
                rclpy.spin_until_future_complete(self._node, future, timeout_sec=timeout)
            else:
                # 使用自己的节点
                rclpy.spin_until_future_complete(self, future, timeout_sec=timeout)
            
            # 获取goal handle
            goal_handle = future.result()
            
            if not goal_handle.accepted:
                self.get_logger().error("Forward goal was rejected")
                return False
            
            self.get_logger().info("Forward goal accepted, waiting for result...")
            
            # 获取结果
            result_future = self._forward_client._get_result_async(goal_handle)
            if hasattr(self, '_node'):
                # 使用现有节点
                rclpy.spin_until_future_complete(self._node, result_future, timeout_sec=timeout)
            else:
                # 使用自己的节点
                rclpy.spin_until_future_complete(self, result_future, timeout_sec=timeout)
            
            result = result_future.result()
            success = result.result.success
            
            if success:
                self.get_logger().info(f"Forward completed successfully: {distance:.2f} meters")
            else:
                self.get_logger().warn(f"Forward failed: {distance:.2f} meters")
            
            return success
            
        except Exception as e:
            self.get_logger().error(f"Forward action failed: {e}")
            return False

    def rotate(self, angle_degrees: float, timeout: float = 60.0) -> bool:
        """
        旋转指定角度
        
        Args:
            angle_degrees: 旋转角度（度），正数逆时针，负数顺时针
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功完成
        """
        if not self._rotate_client.server_is_ready():
            self.get_logger().error("Rotate action server not ready")
            return False
        
        # 检查角度限制
        if abs(angle_degrees) > 170:
            self.get_logger().error(f"Angle {angle_degrees:.1f}° exceeds 170° limit")
            return False
        
        goal = Rotate.Goal()
        goal.angle = math.radians(angle_degrees)
        
        self.get_logger().info(f"Sending rotate goal: {angle_degrees:.1f} degrees")
        
        try:
            # 发送goal并获取future
            future = self._rotate_client.send_goal_async(goal)
            
            # 等待goal完成
            if hasattr(self, '_node'):
                # 使用现有节点
                rclpy.spin_until_future_complete(self._node, future, timeout_sec=timeout)
            else:
                # 使用自己的节点
                rclpy.spin_until_future_complete(self, future, timeout_sec=timeout)
            
            # 获取goal handle
            goal_handle = future.result()
            
            if not goal_handle.accepted:
                self.get_logger().error("Rotate goal was rejected")
                return False
            
            self.get_logger().info("Rotate goal accepted, waiting for result...")
            
            # 获取结果
            result_future = self._rotate_client._get_result_async(goal_handle)
            if hasattr(self, '_node'):
                # 使用现有节点
                rclpy.spin_until_future_complete(self._node, result_future, timeout_sec=timeout)
            else:
                # 使用自己的节点
                rclpy.spin_until_future_complete(self, result_future, timeout_sec=timeout)
            
            result = result_future.result()
            success = result.result.success
            
            if success:
                self.get_logger().info(f"Rotate completed successfully: {angle_degrees:.1f} degrees")
            else:
                self.get_logger().warn(f"Rotate failed: {angle_degrees:.1f} degrees")
            
            return success
            
        except Exception as e:
            self.get_logger().error(f"Rotate action failed: {e}")
            return False

    def navigate_to_pose(self, x: float, y: float, yaw_degrees: float = 0.0, 
                              timeout: float = 300.0) -> bool:
        """
        导航到指定位姿
        
        Args:
            x: 目标X坐标（米）
            y: 目标Y坐标（米）
            yaw_degrees: 目标朝向角度（度），默认为0
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功完成
        """
        if not self._navigate_client.server_is_ready():
            self.get_logger().error("Navigate action server not ready")
            return False
        
        # 创建目标位姿
        goal = NavigateToPose.Goal()
        goal.target_pose = Pose()
        goal.target_pose.position = Point(x=x, y=y, z=0.0)
        
        # 将角度转换为四元数
        yaw_rad = math.radians(yaw_degrees)
        goal.target_pose.orientation = Quaternion(
            x=0.0,
            y=0.0,
            z=math.sin(yaw_rad / 2.0),
            w=math.cos(yaw_rad / 2.0)
        )
        
        self.get_logger().info(f"Sending navigate goal: ({x:.2f}, {y:.2f}, {yaw_degrees:.1f}°)")
        
        try:
            # 发送goal并获取future
            future = self._navigate_client.send_goal_async(goal)
            
            # 等待goal完成
            if hasattr(self, '_node'):
                # 使用现有节点
                rclpy.spin_until_future_complete(self._node, future, timeout_sec=timeout)
            else:
                # 使用自己的节点
                rclpy.spin_until_future_complete(self, future, timeout_sec=timeout)
            
            # 获取goal handle
            goal_handle = future.result()
            
            if not goal_handle.accepted:
                self.get_logger().error("Navigate goal was rejected")
                return False
            
            self.get_logger().info("Navigate goal accepted, waiting for result...")
            
            # 获取结果
            result_future = self._navigate_client._get_result_async(goal_handle)
            if hasattr(self, '_node'):
                # 使用现有节点
                rclpy.spin_until_future_complete(self._node, result_future, timeout_sec=timeout)
            else:
                # 使用自己的节点
                rclpy.spin_until_future_complete(self, result_future, timeout_sec=timeout)
            
            result = result_future.result()
            success = result.result.success
            
            if success:
                self.get_logger().info(f"Navigation completed successfully: ({x:.2f}, {y:.2f}, {yaw_degrees:.1f}°)")
            else:
                self.get_logger().warn(f"Navigation failed: ({x:.2f}, {y:.2f}, {yaw_degrees:.1f}°)")
            
            return success
            
        except Exception as e:
            self.get_logger().error(f"Navigation action failed: {e}")
            return False

    def navigate_to_pose_with_pose(self, pose: Pose, timeout: float = 300.0) -> bool:
        """
        导航到指定位姿（使用Pose对象）
        
        Args:
            pose: 目标位姿
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功完成
        """
        if not self._navigate_client.server_is_ready():
            self.get_logger().error("Navigate action server not ready")
            return False
        
        goal = NavigateToPose.Goal()
        goal.target_pose = pose
        
        x = pose.position.x
        y = pose.position.y
        self.get_logger().info(f"Sending navigate goal with pose: ({x:.2f}, {y:.2f})")
        
        try:
            # 发送goal并获取future
            future = self._navigate_client.send_goal_async(goal)
            
            # 等待goal完成
            if hasattr(self, '_node'):
                # 使用现有节点
                rclpy.spin_until_future_complete(self._node, future, timeout_sec=timeout)
            else:
                # 使用自己的节点
                rclpy.spin_until_future_complete(self, future, timeout_sec=timeout)
            
            # 获取goal handle
            goal_handle = future.result()
            
            if not goal_handle.accepted:
                self.get_logger().error("Navigate goal was rejected")
                return False
            
            self.get_logger().info("Navigate goal accepted, waiting for result...")
            
            # 获取结果
            result_future = self._navigate_client._get_result_async(goal_handle)
            if hasattr(self, '_node'):
                # 使用现有节点
                rclpy.spin_until_future_complete(self._node, result_future, timeout_sec=timeout)
            else:
                # 使用自己的节点
                rclpy.spin_until_future_complete(self, result_future, timeout_sec=timeout)
            
            result = result_future.result()
            success = result.result.success
            
            if success:
                self.get_logger().info(f"Navigation completed successfully: ({x:.2f}, {y:.2f})")
            else:
                self.get_logger().warn(f"Navigation failed: ({x:.2f}, {y:.2f})")
            
            return success
            
        except Exception as e:
            self.get_logger().error(f"Navigation action failed: {e}")
            return False

    def navigate_with_nav2(self, x: float, y: float, yaw_degrees: float = 0.0, 
                                timeout: float = 300.0, behavior_tree: str = "") -> bool:
        """
        使用Navigation2导航到指定位姿
        
        Args:
            x: 目标X坐标（米）
            y: 目标Y坐标（米）
            yaw_degrees: 目标朝向角度（度），默认为0
            timeout: 超时时间（秒）
            behavior_tree: 行为树文件路径（可选）
            
        Returns:
            bool: 是否成功完成
        """
        if not self._nav2_client.server_is_ready():
            self.get_logger().error("Navigation2 action server not ready")
            return False
        
        # 创建目标位姿
        goal = Nav2NavigateToPose.Goal()
        goal.pose = PoseStamped()
        goal.pose.header.frame_id = "map"
        goal.pose.header.stamp = self.get_clock().now().to_msg()
        goal.pose.pose.position = Point(x=x, y=y, z=0.0)
        
        # 将角度转换为四元数
        yaw_rad = math.radians(yaw_degrees)
        goal.pose.pose.orientation = Quaternion(
            x=0.0,
            y=0.0,
            z=math.sin(yaw_rad / 2.0),
            w=math.cos(yaw_rad / 2.0)
        )
        
        # 设置行为树（如果提供）
        if behavior_tree:
            goal.behavior_tree = behavior_tree
        
        self.get_logger().info(f"Sending Navigation2 goal: ({x:.2f}, {y:.2f}, {yaw_degrees:.1f}°)")
        
        try:
            # 发送goal并获取future
            future = self._nav2_client.send_goal_async(goal)
            
            # 等待goal完成
            if hasattr(self, '_node'):
                # 使用现有节点
                rclpy.spin_until_future_complete(self._node, future, timeout_sec=timeout)
            else:
                # 使用自己的节点
                rclpy.spin_until_future_complete(self, future, timeout_sec=timeout)
            
            # 获取goal handle
            goal_handle = future.result()
            
            if not goal_handle.accepted:
                self.get_logger().error("Navigation2 goal was rejected")
                return False
            
            self.get_logger().info("Navigation2 goal accepted, waiting for result...")
            
            # 获取结果
            result_future = self._nav2_client._get_result_async(goal_handle)
            if hasattr(self, '_node'):
                # 使用现有节点
                rclpy.spin_until_future_complete(self._node, result_future, timeout_sec=timeout)
            else:
                # 使用自己的节点
                rclpy.spin_until_future_complete(self, result_future, timeout_sec=timeout)

            result = result_future.result()   
            error_code = result.result.error_code
            error_msg = result.result.error_msg

            if error_code == 0:  # NONE
                self.get_logger().info(f"Navigation2 completed successfully: ({x:.2f}, {y:.2f}, {yaw_degrees:.1f}°)")
                success = True
            else:
                self.get_logger().warn(f"Navigation2 failed with code {error_code}, msg: {error_msg}")
                success = False

            return success
            
        except Exception as e:
            self.get_logger().error(f"Navigation2 action failed: {e}")
            return False

    def navigate_with_nav2_pose(self, pose: PoseStamped, timeout: float = 300.0, 
                                     behavior_tree: str = "") -> bool:
        """
        使用Navigation2导航到指定位姿（使用PoseStamped对象）
        
        Args:
            pose: 目标位姿（PoseStamped）
            timeout: 超时时间（秒）
            behavior_tree: 行为树文件路径（可选）
            
        Returns:
            bool: 是否成功完成
        """
        if not self._nav2_client.server_is_ready():
            self.get_logger().error("Navigation2 action server not ready")
            return False
        
        goal = Nav2NavigateToPose.Goal()
        goal.pose = pose
        
        # 设置行为树（如果提供）
        if behavior_tree:
            goal.behavior_tree = behavior_tree
        
        x = pose.pose.position.x
        y = pose.pose.position.y
        self.get_logger().info(f"Sending Navigation2 goal with pose: ({x:.2f}, {y:.2f})")
        
        try:
            # 发送goal并获取future
            future = self._nav2_client.send_goal_async(goal)
            
            # 等待goal完成
            if hasattr(self, '_node'):
                # 使用现有节点
                rclpy.spin_until_future_complete(self._node, future, timeout_sec=timeout)
            else:
                # 使用自己的节点
                rclpy.spin_until_future_complete(self, future, timeout_sec=timeout)
            
            # 获取goal handle
            goal_handle = future.result()
            
            if not goal_handle.accepted:
                self.get_logger().error("Navigation2 goal was rejected")
                return False
            
            self.get_logger().info("Navigation2 goal accepted, waiting for result...")
            
            # 获取结果
            result_future = self._nav2_client._get_result_async(goal_handle)
            if hasattr(self, '_node'):
                # 使用现有节点
                rclpy.spin_until_future_complete(self._node, result_future, timeout_sec=timeout)
            else:
                # 使用自己的节点
                rclpy.spin_until_future_complete(self, result_future, timeout_sec=timeout)
            
            result = result_future.result()
            success = result.result.success
            
            if success:
                self.get_logger().info(f"Navigation2 completed successfully: ({x:.2f}, {y:.2f})")
            else:
                self.get_logger().warn(f"Navigation2 failed: ({x:.2f}, {y:.2f})")
            
            return success
            
        except Exception as e:
            self.get_logger().error(f"Navigation2 action failed: {e}")
            return False

    def _dock_feedback_cb(self, feedback_msg):
        fb = feedback_msg.feedback
        state_names = {
            1: "INITIAL_PERCEPTION",
            2: "CONTROLLING",
            3: "WAIT_FOR_CHARGE",
            4: "NAV_TO_STAGING_POSE",
        }
        state_name = state_names.get(fb.state, f"UNKNOWN({fb.state})")
        self.get_logger().info(
            f"Feedback: state={state_name}, time={fb.docking_time.sec}s, retries={fb.num_retries}"
        )

    def dockRobot(self, dock_id: str = "home_dock",
                navigate_to_staging_pose: bool = False,
                timeout: float = 300.0) -> bool:
        """触发 Nav2 DockRobot 动作"""
        if not self._dock_client.wait_for_server(timeout_sec=1.0):
            self.get_logger().info("Waiting for 'DockRobot' action server...")
            self._dock_client.wait_for_server()

        goal = DockRobot.Goal()
        goal.use_dock_id = True
        goal.dock_id = dock_id
        goal.navigate_to_staging_pose = False

        self.get_logger().info(
            f"Sending DockRobot goal: id={dock_id}, staging={navigate_to_staging_pose}"
        )
        send_goal_future = self._dock_client.send_goal_async(
            goal, feedback_callback=self._dock_feedback_cb
        )

        rclpy.spin_until_future_complete(self if not hasattr(self, "_node") else self._node,
                                        send_goal_future, timeout_sec=timeout)
        if not send_goal_future.done():
            self.get_logger().error("DockRobot goal send timed out")
            return False

        goal_handle = send_goal_future.result()
        if not goal_handle.accepted:
            self.get_logger().error("DockRobot goal was rejected")
            return False

        result_future = goal_handle.get_result_async()
        rclpy.spin_until_future_complete(self if not hasattr(self, "_node") else self._node,
                                        result_future, timeout_sec=timeout)

        if not result_future.done():
            self.get_logger().error("DockRobot result timed out, canceling goal...")
            try:
                cancel_future = goal_handle.cancel_goal_async()
                rclpy.spin_until_future_complete(self if not hasattr(self, "_node") else self._node,
                                                cancel_future, timeout_sec=5.0)
            finally:
                return False

        result = result_future.result().result
        error_codes = {
            0: "SUCCESS",
            901: "DOCK_NOT_IN_DB",
            902: "DOCK_NOT_VALID",
            903: "FAILED_TO_STAGE",
            904: "FAILED_TO_DETECT_DOCK",
            905: "FAILED_TO_CONTROL",
            906: "FAILED_TO_CHARGE",
            999: "UNKNOWN",
        }
        error_name = error_codes.get(result.error_code, f"UNKNOWN({result.error_code})")
        self.get_logger().info(
            f"DockRobot result: success={result.success}, error={error_name}, retries={result.num_retries}"
        )
        return bool(result.success)


# 便捷函数，用于直接调用
def move_forward(distance: float, timeout: float = 60.0, node=None) -> bool:
    """便捷函数：前进指定距离"""
    if node is None:
        rclpy.init()
        client = OdomControllerClient()
        client.wait_for_servers()
        result = client.move_forward(distance, timeout)
        client.destroy_node()
        rclpy.shutdown()
        return result
    else:
        client = OdomControllerClient.get_instance(node)
        client.wait_for_servers()
        return client.move_forward(distance, timeout)

def rotate(angle_degrees: float, timeout: float = 60.0, node=None) -> bool:
    """便捷函数：旋转指定角度"""
    if node is None:
        rclpy.init()
        client = OdomControllerClient()
        client.wait_for_servers()
        result = client.rotate(angle_degrees, timeout)
        client.destroy_node()
        rclpy.shutdown()
        return result
    else:
        client = OdomControllerClient.get_instance(node)
        client.wait_for_servers()
        return client.rotate(angle_degrees, timeout)

def navigate_to_pose(x: float, y: float, yaw_degrees: float = 0.0, 
                          timeout: float = 300.0, node=None) -> bool:
    """便捷函数：导航到指定位姿"""
    if node is None:
        rclpy.init()
        client = OdomControllerClient()
        client.wait_for_servers()
        result = client.navigate_to_pose(x, y, yaw_degrees, timeout)
        client.destroy_node()
        rclpy.shutdown()
        return result
    else:
        client = OdomControllerClient.get_instance(node)
        client.wait_for_servers()
        return client.navigate_to_pose(x, y, yaw_degrees, timeout)

def navigate_with_nav2(x: float, y: float, yaw_degrees: float = 0.0, 
                            timeout: float = 300.0, behavior_tree: str = "", node=None) -> bool:
    """便捷函数：使用Navigation2导航到指定位姿"""
    if node is None:
        rclpy.init()
        client = OdomControllerClient()
        client.wait_for_servers()
        result = client.navigate_with_nav2(x, y, yaw_degrees, timeout, behavior_tree)
        client.destroy_node()
        rclpy.shutdown()
        return result
    else:
        client = OdomControllerClient.get_instance(node)
        client.wait_for_servers()
        return client.navigate_with_nav2(x, y, yaw_degrees, timeout, behavior_tree)

def navigate_with_nav2_pose(pose: PoseStamped, timeout: float = 300.0, 
                                 behavior_tree: str = "", node=None) -> bool:
    """便捷函数：使用Navigation2导航到指定位姿（使用PoseStamped对象）"""
    if node is None:
        rclpy.init()
        client = OdomControllerClient()
        client.wait_for_servers()
        result = client.navigate_with_nav2_pose(pose, timeout, behavior_tree)
        client.destroy_node()
        rclpy.shutdown()
        return result
    else:
        client = OdomControllerClient.get_instance(node)
        client.wait_for_servers()
        return client.navigate_with_nav2_pose(pose, timeout, behavior_tree)

def dock_robot(dock_id: str = "home_dock",
               navigate_to_staging_pose: bool = False,
               timeout: float = 300.0,
               node=None) -> bool:
    """便捷函数：DockRobot 充电对接"""
    if node is None:
        rclpy.init()
        client = OdomControllerClient()
        client.wait_for_servers()
        result = client.dockRobot(dock_id=dock_id,
                                  navigate_to_staging_pose=navigate_to_staging_pose,
                                  timeout=timeout)
        client.destroy_node()
        rclpy.shutdown()
        return result
    else:
        client = OdomControllerClient.get_instance(node)
        client.wait_for_servers()
        return client.dockRobot(dock_id=dock_id,
                                navigate_to_staging_pose=navigate_to_staging_pose,
                                timeout=timeout)
    
def navigate_with_nav2_location(location: str,
                              timeout: float = 300.0, 
                              behavior_tree: str = "", 
                              node=None) -> bool:
    """便捷函数：使用Navigation2导航到指定位置"""
    # 查询数据库获取位置信息
    points = get_points_by_location(location)
    
    if not points:
        print(f"未找到位置 '{location}' 的导航点")
        return False
    
    # 使用第一个点位进行导航（如果有多个点位，已按order排序）
    point = points[0]
    pose = point.get('pose', {})
    x = pose.get('x', 0.0)
    y = pose.get('y', 0.0)
    yaw = pose.get('yaw', 0.0)
    
    print(f"导航到位置 '{location}': x={x}, y={y}, yaw={yaw}")
    if node is None:
        rclpy.init()
        try:
            client = OdomControllerClient()
            client.wait_for_servers()
            result = client.navigate_with_nav2(x, y, yaw, timeout, behavior_tree)
        finally:
            client.destroy_node()
            rclpy.shutdown()
        return result
    else:
        client = OdomControllerClient.get_instance(node)
        client.wait_for_servers()
        return client.navigate_with_nav2(x, y, yaw, timeout, behavior_tree)


def get_points_by_location(location: str, server_address: str = "tcp://127.0.0.1:5555") -> List[Dict[str, Any]]:
    """获取特定区域的所有点位，按order排序（如果有）
    
    Args:
        location: 位置名称
        server_address: 数据库服务地址
        
    Returns:
        排序后的点位列表
    """
    try:
        # 创建数据库客户端连接
        with create_client(server_address) as client:
            # 查询特定区域的所有点位
            result = client.query({"location": location}, "exhibition_points")
            
            if isinstance(result, dict):
                points = result.get("SUCCESS", [])
                if points:
                    # 如果点位有order字段，按order排序
                    if any("order" in point for point in points):
                        return sorted(points, key=lambda x: x.get("order", float("inf")))
                    return points
            return []
            
    except Exception as e:
        print(f"查询数据库时发生错误: {e}")
        return []


def main():
    """示例用法"""
    def example():
        rclpy.init()
        client = OdomControllerClient()
        
        # 等待服务器就绪
        client.wait_for_servers()
        
        # 示例1：前进1米
        print("=== 示例1：前进1米 ===")
        success = client.move_forward(-1.0)
        print(f"前进结果: {success}")
        
        # # 示例2：旋转90度
        # print("\n=== 示例2：旋转90度 ===")
        # success = client.rotate(90.0)
        # print(f"旋转结果: {success}")
        
        # # # 示例3：导航到指定位置
        # # print("\n=== 示例3：导航到(2, 1)位置 ===")
        # # success = client.navigate_to_pose(1.0, 0.0, 45.0)
        # # print(f"导航结果: {success}")
        
        # 示例4：使用Navigation2导航
        # print("\n=== 示例4：使用Navigation2导航到(0, 0)位置 ===")
        # success = client.navigate_with_nav2(0.0, 0.0, 90.0)
        # print(f"Navigation2导航结果: {success}")
        
        # 示例5：使用Docking对码
        print("=== 示例5：对码 ===")
        success = client.dockRobot("home_dock")
        print(f"dock结果: {success}")

        client.destroy_node()
        rclpy.shutdown()
    
    def example_with_existing_node():
        """使用现有节点的示例"""
        print("\n=== 使用现有节点的示例 ===")
        
        # 创建现有节点
        rclpy.init()
        existing_node = rclpy.create_node('my_existing_node')
        
        try:
            # 使用现有节点调用便捷函数
            # print("1. 使用现有节点前进1米...")
            # success = move_forward(-1.0, node=existing_node)
            # print(f"   结果: {'成功' if success else '失败'}")
            
            # print("2. 使用现有节点旋转45度...")
            # success = rotate(45.0, node=existing_node)
            # print(f"   结果: {'成功' if success else '失败'}")
            
            # print("3. 使用现有节点Navigation2导航...")
            # success = navigate_with_nav2(1.0, 1.0, 0.0, node=existing_node)
            # print(f"   结果: {'成功' if success else '失败'}")


            print("4. 使用现有节点docking...")
            success = dock_robot("home_dock", node=existing_node)
            print(f"   结果: {'成功' if success else '失败'}")
            
        finally:
            existing_node.destroy_node()
            rclpy.shutdown()
    
    # 运行示例
    example()
    # example_with_existing_node()

from ..core.setup_strategies import register_setup_command, SetupCommandStrategy
@register_setup_command("navigation")
class NavigationStrategy(SetupCommandStrategy):
    def can_handle(self, command: str) -> bool:
        return command == "navigation"
    
    def execute(self, params: dict, task_builder) -> None:
        if (params['cmd'] == 'move_forward'):
            move_forward(params['distance'], node=task_builder.task_node)
        elif (params['cmd'] == 'rotate'):
            rotate(params['angle'], node=task_builder.task_node)
        elif (params['cmd'] == 'navigate_to_pose'):
            navigate_to_pose(params['x'], params['y'], params['yaw'], node=task_builder.task_node)
        elif (params['cmd'] == 'navigate_with_nav2'):
            navigate_with_nav2(params['x'], params['y'], params['yaw'], node=task_builder.task_node)
        elif (params['cmd'] == 'navigate_with_nav2_location'):
            navigate_with_nav2_location(params['location'], node=task_builder.task_node)
        elif (params['cmd'] == 'dock_robot'):
            dock_robot(dock_id=params['dock_id'],
                    node=task_builder.task_node)

if __name__ == '__main__':
    main() 