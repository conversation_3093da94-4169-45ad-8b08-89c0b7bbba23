import os
from abc import ABC, abstractmethod
from datetime import datetime

from papjia_skill.action_models import ActionModels
from papjia_skill.atom.papjia_delay_async import PapjiaDelayAsync
from papjia_skill.atom.pause_until_signal import PauseUntilSignal

from papjia_ydsb_demo_task.task_manager.actions.arm_move import get_waypoint_from_config, get_plan_and_execute_waypoints_sequence
from papjia_ydsb_demo_task.task_manager.actions.object_detect import gen_sequence_detect_json_objects

# TODO 要把这个干掉
action_model_config_filepath = os.getenv(
    'ACTION_MODEL_CONFIG_FILEPATH', 
    '/workspace/src/papjia_szyj/papjia_ydsb_demo_task/config/action_model.yaml'
)
ActionModels(action_model_config_filepath)

class ActionNodeCreator(ABC):
    """动作节点创建器基类"""
    @abstractmethod
    def create_node(self, params):
        """创建行为树节点"""
        pass

class ArmNodeCreator(ActionNodeCreator):
    def create_node(self, params):
        return get_plan_and_execute_waypoints_sequence(params['waypoints'], params['vel'])


class DelayNodeCreator(ActionNodeCreator):
    def create_node(self, params):
        return PapjiaDelayAsync(delay_duration=float(params['duration']))


class PauseNodeCreator(ActionNodeCreator):
    def create_node(self, params):
        return PauseUntilSignal(service_name="pause_until_signal", signal_id='', timeout=0.0)

class ObjectDetectNodeCreator(ActionNodeCreator):
    def create_node(self, params):
        service_name = params.get('service_name', "/papjia/vision/local/object/detect")
        max_num = params.get('max_num', 10)
        min_score = params.get('min_score', 0.5)
        key = params.get('key', "detect_objects")
        return gen_sequence_detect_json_objects(service_name, max_num, min_score, key)


class WaypointConfigDecorator(ActionNodeCreator):
    """路径点配置装饰器"""
    def __init__(self, creator, waypoint_configs):
        self._creator = creator
        self._waypoint_configs = waypoint_configs

    def create_node(self, params):
        # 获取路径点名称
        waypoint_names = params.get('waypoints', [])
        if not waypoint_names:
            raise ValueError("未指定路径点名称")

        # 获取对象名称
        object_name = params.get('object_name')
        if not object_name:
            raise ValueError("未指定对象名称")

        # 检查对象是否存在
        if object_name not in self._waypoint_configs:
            raise KeyError(f"未找到对象配置: {object_name}")

        # 获取路径点配置并转换为Waypoint对象
        waypoints = []
        for name in waypoint_names:
            if name not in self._waypoint_configs[object_name]:
                raise KeyError(f"未找到路径点配置: {name} 在对象 {object_name} 中")
            waypoint_config = self._waypoint_configs[object_name][name]
            waypoint = get_waypoint_from_config(name, waypoint_config)
            waypoints.append(waypoint)

        # 更新参数
        new_params = params.copy()
        new_params['waypoints'] = waypoints

        # 调用原始创建器
        return self._creator.create_node(new_params)


class ActionNodeFactory:
    """动作节点工厂"""
    def __init__(self, waypoint_configs=None, blackboard=None):
        self._creators = {
            'arm': ArmNodeCreator(),
            'delay': DelayNodeCreator(),
            'pause': PauseNodeCreator(),
            'object_detect': ObjectDetectNodeCreator(),
        }
        
        # 如果有路径点配置，为arm创建器添加装饰器
        if waypoint_configs:
            self._creators['arm'] = WaypointConfigDecorator(self._creators['arm'], waypoint_configs)
        
        # 设置blackboard
        self.blackboard = blackboard
    
    def create_node(self, action_type, params):
        """创建动作节点"""
        if action_type not in self._creators:
            raise ValueError(f"未知的动作类型: {action_type}")
        
        # 处理参数中的blackboard引用
        processed_params = self._process_params(params)
        return self._creators[action_type].create_node(processed_params)
    
    def _process_params(self, params):
        """处理参数中的blackboard引用"""
        if not self.blackboard:
            return params
            
        processed_params = {}
        for key, value in params.items():
            if isinstance(value, dict) and 'blackboard' in value:
                # 从blackboard获取值
                blackboard_key = value['blackboard']
                processed_params[key] = self.blackboard.get(blackboard_key)
            else:
                processed_params[key] = value
        return processed_params
    
    def check_has_action_type(self, action_type):
        """检查是否存在动作类型"""
        return action_type in self._creators.keys()


