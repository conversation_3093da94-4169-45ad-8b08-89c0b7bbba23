import time

from .gripper_dh_control import <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>ripperD<PERSON>
from .gripper_soft_control import <PERSON><PERSON><PERSON> as <PERSON>ripperSoft
from ..core.setup_strategies import register_setup_command, SetupCommandStrategy


@register_setup_command("gripper")
class GripperStrategy(SetupCommandStrategy): 
    def can_handle(self, command: str) -> bool:
        return command == "gripper"
    
    def execute(self, params: dict, task_builder) -> None:
        arm_prefix = task_builder.blackboard.get('arm_prefix')
        if arm_prefix is None:
            raise ValueError("Arm prefix not found in blackboard")

        action = params.get("action")
        gripper_percentage = 0
        if action == "open":
            gripper_percentage = task_builder.blackboard.get('gripper_open')
        elif action == "close":
            gripper_percentage = task_builder.blackboard.get('gripper_close')
        else:
            raise ValueError(f"Action not expected: {action}")
        
        if gripper_percentage is None:
            raise ValueError("Gripper percentage not found in blackboard")
        gripper_percentage = int(gripper_percentage)
        
        gripper = None
        if arm_prefix == "右臂-":
            gripper = GripperDH("**************", port=5001)
            if not gripper.is_inited():
                gripper.init()
            # 将百分比转换为行程位置：0% -> 1000, 100% -> 0
            position = 1000 - (gripper_percentage * 10)  # 线性映射：0%->1000, 100%->0
            gripper.move(40, 40, position)
            time.sleep(0.5)
            while gripper.is_moving():
                time.sleep(0.5)
                continue
            gripper.close()

        elif arm_prefix == "左臂-":
            gripper = GripperSoft("**************", 5001)
            gripper.set_percentage(gripper_percentage)
            time.sleep(1.0)
        
        else:
            raise ValueError(f"Arm prefix not expected: {arm_prefix}")