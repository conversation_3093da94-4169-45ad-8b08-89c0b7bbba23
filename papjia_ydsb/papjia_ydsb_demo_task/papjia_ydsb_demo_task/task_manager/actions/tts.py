import os
import time
import json
import zmq
from typing import Optional

from ..core.setup_strategies import register_setup_command, SetupCommandStrategy
from typing import Dict, Any, List
from .zmq_client_db import create_client

# TTS服务配置 - 使用与tts_server.py相同的端口
TTS_REQ_ADDRESS = "tcp://localhost:5569"

def tts(text: str, rate: int = 0, pitch: int = 0, volume: int = 0) -> bool:
    """
    调用TTS服务进行语音合成
    
    Args:
        text (str): 要合成的文本
        rate (int): 语速调整（未使用，保持兼容性）
        pitch (int): 音调调整（未使用，保持兼容性）
        volume (int): 音量调整（未使用，保持兼容性）
    
    Returns:
        bool: 是否成功发送TTS请求
    """
    try:
        context = zmq.Context()
        print(f"🔊 连接到TTS服务器: {TTS_REQ_ADDRESS}")
        socket = context.socket(zmq.REQ)
        socket.setsockopt(zmq.RCVTIMEO, 10000)  # 10秒超时
        socket.connect(TTS_REQ_ADDRESS)

        # 构造TTS请求消息，格式与tts_server.py一致
        message = {
            "text": text,
            "type": "default",
            "timestamp": time.time()
        }
        
        print(f"📤 发送TTS请求: {text}")
        socket.send_json(message)
        
        # 接收响应
        response = socket.recv_json()
        print(f"📥 TTS服务器响应: {response}")
        
        # 检查响应状态
        if response.get("status") == "success":
            print(f"✅ TTS请求成功: {text}")
            return True
        else:
            print(f"❌ TTS请求失败: {response.get('message', '未知错误')}")
            return False
            
    except zmq.error.Again:
        print("⏰ TTS请求超时")
        return False
    except Exception as e:
        print(f"❌ TTS服务器错误: {e}")
        return False
    finally:
        try:
            socket.close()
            context.term()
        except:
            pass
        
def get_point_commentary(location: str, server_address: str = "tcp://127.0.0.1:5555") -> List[Dict[str, Any]]:
    """获取特定区域的所有解说词
    
    Args:
        location: 位置名称
        server_address: 数据库服务地址
        
    Returns:
        排序后的点位列表
    """
    try:
        # 创建数据库客户端连接
        with create_client(server_address) as client:
            # 查询特定区域的所有点位
            result = client.query({"location": location}, "exhibition_points")
            if isinstance(result, dict):
                points = result.get("SUCCESS", [])
                if points and points[0].get("commentary"):
                    return [item["content"] for item in points[0]["commentary"]]
            return []
            
    except Exception as e:
        print(f"查询数据库时发生错误: {e}")
        return []

@register_setup_command("tts")
class TTSStrategy(SetupCommandStrategy):
    """TTS策略实现"""
    
    def can_handle(self, command: str) -> bool:
        return command == "tts"
    
    def execute(self, params: dict, task_builder) -> None:
        """
        执行TTS命令
        
        Args:
            params (dict): 包含text字段的参数
            task_builder: 任务构建器实例
        """
        location = params.get('location', '')
        text = get_point_commentary(location)
        if not text:
            print("⚠️ TTS命令缺少text参数")
            return
        
        # 调用TTS服务
        success = tts(text)
        if success:
            print(f"✅ TTS执行成功: {text}")
        else:
            print(f"❌ TTS执行失败: {text}")

def test_tts():
    """测试TTS功能"""
    print("🧪 测试TTS功能...")
    
    test_texts = [
        "你好，我是小爱同学，我是一个语音助手，我可以帮助你完成各种任务。",
        "正在执行机器人操作",
        "操作完成"
    ]
    
    for text in test_texts:
        print(f"\n测试文本: {text}")
        success = tts(text)
        if success:
            print("✅ 测试成功")
        else:
            print("❌ 测试失败")
        time.sleep(1)  # 等待TTS处理完成

if __name__ == "__main__":
    test_tts()


''' fixed gt bug
sudo apt install python3-gst-1.0
'''
