"""
ZeroMQ client module for PapJia Database Service.

This module provides a convenient client interface for other services
to communicate with the database service through ZeroMQ.
"""

import zmq
import json
import logging
from typing import Dict, Any, Optional, List


class ZMQDatabaseClient:
    """
    ZeroMQ client for database operations.
    
    Provides a high-level interface for communicating with the database service
    through ZeroMQ messages.
    """
    
    def __init__(self, server_address: str = "tcp://127.0.0.1:5555", timeout: int = 5000):
        """
        Initialize ZeroMQ client.
        
        Args:
            server_address: ZeroMQ server address (e.g., "tcp://127.0.0.1:5555")
            timeout: Request timeout in milliseconds
        """
        self.server_address = server_address
        self.timeout = timeout
        self.context = zmq.Context()
        self.socket = None
        self.logger = logging.getLogger(__name__)
        
        # Connect to server
        self._connect()
    
    def _connect(self):
        """Connect to the ZeroMQ server."""
        try:
            self.socket = self.context.socket(zmq.REQ)
            self.socket.setsockopt(zmq.RCVTIMEO, self.timeout)
            self.socket.setsockopt(zmq.SNDTIMEO, self.timeout)
            self.socket.connect(self.server_address)
            self.logger.info(f"Connected to ZeroMQ server at {self.server_address}")
        except Exception as e:
            self.logger.error(f"Failed to connect to ZeroMQ server: {e}")
            raise
    
    def _send_request(self, operation: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send a request to the server and return the response.
        
        Args:
            operation: Database operation to perform
            params: Operation parameters
            
        Returns:
            Server response
            
        Raises:
            ConnectionError: If communication with server fails
            TimeoutError: If request times out
        """
        request = {
            "operation": operation,
            "params": params
        }
        
        try:
            # Send request
            self.socket.send_json(request)
            
            # Receive response
            response = self.socket.recv_json()
            
            return response
            
        except zmq.Again:
            # Timeout occurred
            self.logger.error(f"Request timeout for operation: {operation}")
            # Close and recreate socket on timeout
            self._reconnect()
            raise TimeoutError(f"Request timeout for operation: {operation}")
        
        except Exception as e:
            self.logger.error(f"Communication error: {e}")
            self._reconnect()
            raise ConnectionError(f"Failed to communicate with server: {e}")
    
    def _reconnect(self):
        """Reconnect to the server after an error."""
        try:
            if self.socket:
                self.socket.close()
            self._connect()
        except Exception as e:
            self.logger.error(f"Failed to reconnect: {e}")
    
    def _handle_response(self, response: Dict[str, Any]) -> Any:
        """
        Handle server response and extract result or raise exception.
        
        Args:
            response: Server response
            
        Returns:
            Operation result
            
        Raises:
            RuntimeError: If server returned an error
        """
        if response.get("status") == "success":
            return response.get("result")
        elif response.get("status") == "error":
            error_message = response.get("message", "Unknown error")
            error_code = response.get("code", "UNKNOWN_ERROR")
            raise RuntimeError(f"Server error [{error_code}]: {error_message}")
        else:
            raise RuntimeError(f"Invalid response format: {response}")
    
    def insert(self, record: Dict[str, Any], collection_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Insert a record into the database.
        
        Args:
            record: Record to insert
            collection_name: Optional collection name
            
        Returns:
            Insert result
        """
        params = {"record": record}
        if collection_name:
            params["collection_name"] = collection_name
        
        response = self._send_request("insert", params)
        return self._handle_response(response)
    
    def insert_or_update(self, record: Dict[str, Any], collection_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Insert or update a record in the database.
        
        Args:
            record: Record to insert or update
            collection_name: Optional collection name
            
        Returns:
            Operation result
        """
        params = {"record": record}
        if collection_name:
            params["collection_name"] = collection_name
        
        response = self._send_request("insert_or_update", params)
        return self._handle_response(response)
    
    def query(self, query: Dict[str, Any], collection_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Query records from the database.
        
        Args:
            query: Query filter
            collection_name: Optional collection name
            
        Returns:
            Query result
        """
        params = {"query": query}
        if collection_name:
            params["collection_name"] = collection_name
        
        response = self._send_request("query", params)
        return self._handle_response(response)
    
    def update(self, query: Dict[str, Any], record: Dict[str, Any], 
               collection_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Update records in the database.
        
        Args:
            query: Query filter for records to update
            record: Update data
            collection_name: Optional collection name
            
        Returns:
            Update result
        """
        params = {
            "query": query,
            "record": record
        }
        if collection_name:
            params["collection_name"] = collection_name
        
        response = self._send_request("update", params)
        return self._handle_response(response)
    
    def delete(self, query: Dict[str, Any], collection_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Delete records from the database.
        
        Args:
            query: Query filter for records to delete
            collection_name: Optional collection name
            
        Returns:
            Delete result
        """
        params = {"query": query}
        if collection_name:
            params["collection_name"] = collection_name
        
        response = self._send_request("delete", params)
        return self._handle_response(response)
    
    def health_check(self) -> Dict[str, Any]:
        """
        Check the health of the database service.
        
        Returns:
            Health check result
        """
        response = self._send_request("health_check", {})
        return self._handle_response(response)
    
    def close(self):
        """Close the client connection."""
        if self.socket:
            self.socket.close()
            self.logger.info("ZeroMQ client connection closed")
        
        if self.context:
            self.context.term()
            self.logger.info("ZeroMQ context terminated")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()


# Convenience functions for simple usage
def create_client(server_address: str = "tcp://127.0.0.1:5555", 
                  timeout: int = 5000) -> ZMQDatabaseClient:
    """
    Create a new ZeroMQ database client.
    
    Args:
        server_address: ZeroMQ server address
        timeout: Request timeout in milliseconds
        
    Returns:
        ZMQDatabaseClient instance
    """
    return ZMQDatabaseClient(server_address, timeout)





# Example usage
if __name__ == "__main__":
    import logging
    
    logging.basicConfig(level=logging.INFO)
    
    # Example with single client
    with create_client() as client:
        # Health check
        health = client.health_check()
        print(f"Health check: {health}")
        
        # Insert a record
        record = {
            "name": "Test User123",
            "email": "<EMAIL>",
            "category": "user"
        }
        
        try:
            result = client.insert(record, "test_table")
            print(f"Insert result: {result}")
        except Exception as e:
            print(f"Insert failed: {e}")
        
        # Query records
        try:
            query_result = client.query({"name": "Test User123"}, "test_table")
            print(f"Query result: {query_result}")
        except Exception as e:
            print(f"Query failed: {e}")