import json
from copy import deepcopy

import json
import rclpy
import math

from tf_transformations import euler_from_quaternion, quaternion_from_euler, quaternion_matrix

def select_target(worldmodel, prefer='any'):
    """选择目标物体
    Args:
        worldmodel: 世界模型数据
        prefer: 偏好类型，'any'表示任意物体，其他值表示特定前缀
    Returns:
        str: 目标物体ID，如果没有找到则返回None
    """
    if len(worldmodel["objects"]) == 0:
        return None
    
    if prefer == 'any':
        return list(worldmodel['objects'].keys())[0]
    
    if prefer == 'any_reverse':
        return list(worldmodel['objects'].keys())[-1]   
    # 查找指定前缀的物体
    for key in worldmodel['objects'].keys():
        if key.startswith(prefer):
            return key
    
    if len(worldmodel['objects']) > 0:
        return None
    
    return None


def update_pick_waypoint(worldmodel, object_id, grasp_offset, waypoint_name, waypoint_config, frame_id="arm_base"):
    """更新抓取路径点
    Args:
        worldmodel: 世界模型数据
        object_id: 目标物体ID
        grasp_offset: 抓取偏移量 [dx, dy, dz, droll, dpitch, dyaw]
        waypoint_name: 路径点名称
        waypoint_config: 路径点配置字典
        frame_id: 坐标系ID，默认为"arm_base"
    Returns:
        bool: 是否成功更新
    """
    try:
        if object_id not in worldmodel['objects']:
            print(f"ERROR: Object {object_id} not found in worldmodel")
            return False
            
        target = worldmodel['objects'][object_id]
        if target == None:
            print(f"ERROR: Target object {object_id} data is None")
            return False
        
        combined_pose = apply_pose_offset(target["pose"][:], grasp_offset)
        
        waypoint_config[waypoint_name]["pose"] = combined_pose[:]
        waypoint_config[waypoint_name]["frame_id"] = frame_id
        
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to update waypoint {waypoint_name}: {e}")
        return False


from tf_transformations import quaternion_matrix, translation_matrix, translation_from_matrix, quaternion_from_matrix, euler_matrix
def apply_pose_offset(target_pose, offset_pose):
    """应用位姿偏移量计算
    参数:
        target_pose (list): 基础位姿 [x, y, z, qx, qy, qz, qw]
        offset_pose (list): 相对偏移 [dx, dy, dz, dqx, dqy, dqz, dqw]
    返回:
        list: 组合后的新位姿 [x', y', z', qx', qy', qz', qw']
    """
    # 解包基础位姿
    base_trans = target_pose[0:3]
    base_quat = target_pose[3:7]
    
    # 解包偏移量
    offset_trans = offset_pose[0:3]
    offset_eular = [angle * math.pi / 180.0 for angle in offset_pose[3:]]  # 列表推导式逐个转换
    
    # 构建变换矩阵
    base_matrix = translation_matrix(base_trans) @ quaternion_matrix(base_quat)
    offset_matrix = translation_matrix(offset_trans) @ euler_matrix(offset_eular[0], offset_eular[1], offset_eular[2], axes='sxyz')
    
    # 组合变换 (注意矩阵相乘顺序)
    combined_matrix = base_matrix @ offset_matrix
    
    # 提取结果
    new_trans = translation_from_matrix(combined_matrix)
    new_quat = quaternion_from_matrix(combined_matrix)
    
    return [
        new_trans[0], new_trans[1], new_trans[2],
        new_quat[0], new_quat[1], new_quat[2], new_quat[3]
    ]


    


