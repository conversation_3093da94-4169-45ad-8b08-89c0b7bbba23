import json
from papjia_skill.atom.object_detect import ObjectDetect
from papjia_skill.atom.object_to_json import ObjectToJson
# from papjia_skill.atom.json_to_object import JsonToObject
from papjia_skill.atom.remove_escape_character import RemoveEscape<PERSON>haracter
from papjia_skill.btree import Sequence
from papjia_skill.utils import add_escape_character, swap_quotes
from copy import deepcopy


def gen_sequence_detect_json_objects(
        service_name="/papjia/vision/local/object/detect", 
        max_num=10, min_score=0.5, key="detect_objects"):
    seq = Sequence("seq_detect_objects")
    detect_objects = ObjectDetect(service_name=service_name, max_num=max_num, min_score=min_score, objects="{objects}")
    to_json = ObjectToJson(objects="{objects}", json_result=f"{{{key}}}")
    seq.add_child(detect_objects)
    seq.add_child(to_json)
    return seq

# def gen_action_dict2object(obj_dict, key="result_object"):
#     json_data = json.dumps(obj_dict)
#     json_object = RemoveEscapeCharacter(json_input=add_escape_character(json_data), json_result="{json_object}")
#     json2obj = JsonToObject(json_object="{json_object}", result_object=f"{{{key}}}")
#     seq = Sequence("seq_dict2object")
#     seq.add_child(json_object)
#     seq.add_child(json2obj)
#     return seq

def select_nearest_object(object_list, base_position=[0.0, 0.0, 0.0]):
    """
    从物体列表中选择距离最近的物体
    """
    if not object_list:
        return (None, None)

    min_distance = float("inf")
    nearest_object = None
    nearest_index = None
    for index, obj in enumerate(object_list):
        obj_position = obj["pose"][:3]  # 只取xyz坐标
        distance = (
            (obj_position[0] - base_position[0]) ** 2
            + (obj_position[1] - base_position[1]) ** 2
            + (obj_position[2] - base_position[2]) ** 2
        ) ** 0.5
        if distance < min_distance:
            min_distance = distance
            nearest_object = deepcopy(obj)
            nearest_index = index
    return (nearest_object, nearest_index)

def select_grasp_position(object):
    """
    根据物体的位置选择机械臂的抓取位置
    """
    if not object:
        return None

    # 获取物体位置
    object_position = object["pose"][:3]
    object_size = object["scale"]
    # 计算顶端位置
    top_position = [object_position[0], object_position[1], object_position[2] + object_size[2] / 2]
    # 计算抓取位置
    grasp_position = [top_position[0] + 0.10, top_position[1], top_position[2] + 0.01 - 0.16]
    print(
        f"物体位置: ({object_position[0]:.2f}, {object_position[1]:.2f}, "
        f"{object_position[2]:.2f}) --> 抓取位置: ({grasp_position[0]:.2f}, "
        f"{grasp_position[1]:.2f}, {grasp_position[2]:.2f})"
    )
    return grasp_position

def is_valid_position(position):
    is_valid = False
    can_grasp = False
    need_move_dist = 0.0
    x, y, z = position[0:3]
    rangey = [-0.9, 1.05]
    rangez = [1.55, 1.85]
    if y >= rangey[0] and y <= rangey[1] and z >= rangez[0] and z <= rangez[1]:
        is_valid = True
        if abs(y) < 0.45:
            need_move_dist = x - 1.50
        elif abs(y) < 0.6:
            need_move_dist = x - 1.40
        elif abs(y) < 0.75:
            need_move_dist = x - 1.35
        else:
            need_move_dist = x - 1.30
    
    rangex = [1.0, 1.55]
    rangey = [-0.95, 1.05]
    rangez = [1.55, 1.75]
    if (
        x >= rangex[0]
        and x <= rangex[1]
        and y >= rangey[0]
        and y <= rangey[1]
        and z >= rangez[0]
        and z <= rangez[1]
    ):
        can_grasp = True
    rangex = [1.30, 1.40]
    rangey = [-0.75, 0.75]
    rangez = [1.55, 1.75]
    if (
        x >= rangex[0]
        and x <= rangex[1]
        and y >= rangey[0]
        and y <= rangey[1]
        and z >= rangez[0]
        and z <= rangez[1]
    ):
        can_grasp = True
    rangex = [1.40, 1.50]
    rangey = [-0.5, 0.6]
    if (
        x >= rangex[0]
        and x <= rangex[1]
        and y >= rangey[0]
        and y <= rangey[1]
        and z >= rangez[0]
        and z <= rangez[1]
    ):
        can_grasp = True
    rangex = [1.50, 1.60]
    rangey = [-0.30, 0.40]
    if (
        x >= rangex[0]
        and x <= rangex[1]
        and y >= rangey[0]
        and y <= rangey[1]
        and z >= rangez[0]
        and z <= rangez[1]
    ):
        can_grasp = True
    return (can_grasp, is_valid, need_move_dist)

def filter_and_select_object(objects):
    can_grasp_objs = []
    valid_grasp_info = []
    min_move_dist = 1000.0
    for obj in objects:
        if obj["scale"][2] < 0.20:
            print(f"物体高度小于0.20米，跳过: {obj['scale']}")
            continue
        grasp_position = select_grasp_position(obj)
        can_grasp, is_valid, need_move_dist = is_valid_position(grasp_position)
        print(f"can_grasp: {can_grasp}, is_valid: {is_valid}, need_move_dist: {need_move_dist:.2f}")
        if can_grasp:
            can_grasp_obj = deepcopy(obj)
            can_grasp_obj["original_pose"] = obj["pose"][:]
            can_grasp_obj["original_scale"] = obj["scale"][:]
            can_grasp_obj["pose"][:3] = grasp_position
            can_grasp_objs.append(can_grasp_obj)
        elif is_valid:  # 不是可以抓取的位置，但是是有效的位置
            valid_grasp_info.append((can_grasp, is_valid, need_move_dist))
    for can_grasp, is_valid, need_move_dist in valid_grasp_info:
        if need_move_dist > 0.01 and need_move_dist < min_move_dist:
            min_move_dist = need_move_dist
    obj, index = select_nearest_object(can_grasp_objs)
    return obj, min_move_dist