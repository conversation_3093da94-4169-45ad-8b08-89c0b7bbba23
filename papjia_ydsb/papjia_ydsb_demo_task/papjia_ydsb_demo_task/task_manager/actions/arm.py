import os
import time
import json
import zmq
from typing import Optional, Dict, Any, List

from ..core.setup_strategies import register_setup_command, SetupCommandStrategy
from .zmq_client_db import create_client

# 数据库服务地址，保持在文件顶部方便修改
DB_SERVER_ADDRESS = "tcp://127.0.0.1:5555"

# 假设这是一个模拟的函数，用于调用实际的臂展动作接口
def arm_move_by_name(name: str, wait: bool = True) -> bool:
    """
    模拟调用机器人手臂动作接口，通过名称移动到指定点位。
    
    Args:
        name (str): 点位名称。
        wait (bool): 是否等待动作完成。
        
    Returns:
        bool: 动作是否成功完成。
    """
    print(f"🤖 正在执行手臂动作: 移动到点位 '{name}'")

    time.sleep(1)
    if name:  # 假设只要名称不为空，动作就成功
        if wait:
            print(f"✅ 移动到点位 '{name}' 成功。")
        else:
            print(f"✅ 移动命令已发送，不等待完成。")
        return True
    else:
        print(f"❌ 移动到点位失败: 名称为空。")
        return False

def get_point_actions(location: str) -> List[Dict[str, Any]]:
    """
    获取特定区域的所有手臂动作。
    
    Args:
        location (str): 位置名称。
        
    Returns:
        List[Dict[str, Any]]: 包含手臂动作的字典列表。
    """
    try:
        # 创建数据库客户端连接
        with create_client(DB_SERVER_ADDRESS) as client:
            # 查询特定区域的动作配置
            result = client.query({"location": location}, "exhibition_points")
            if isinstance(result, dict):
                points = result.get("SUCCESS", [])
                if points and points[0].get("arm_actions"): # 假设数据库中的字段名为 'arm_actions'
                    return points[0]["arm_actions"]
            return []
    except Exception as e:
        print(f"查询数据库时发生错误: {e}")
        return []

@register_setup_command("arm_action")
class ArmActionStrategy(SetupCommandStrategy):
    """手臂动作策略实现"""
    
    def can_handle(self, command: str) -> bool:
        return command == "arm_action"
    
    def execute(self, params: dict, task_builder) -> None:
        """
        执行手臂动作命令。
        
        Args:
            params (dict): 包含location字段的参数。
            task_builder: 任务构建器实例。
        """
        location = params.get('location', '')
        if not location:
            print("⚠️ Arm Action命令缺少location参数。")
            return
        
        arm_actions = get_point_actions(location)
        if not arm_actions:
            print(f"⚠️ 在位置 '{location}' 未找到任何手臂动作。")
            return
            
        # 遍历所有手臂动作并执行
        for action in arm_actions:
            point_name = action.get('name')
            wait = action.get('wait', True)
            
            if point_name:
                success = arm_move_by_name(point_name, wait)
                if not success:
                    print(f"❌ 手臂动作 '{point_name}' 执行失败，任务中断。")
                    return
            else:
                print(f"⚠️ 发现一个无效动作配置: {action}")
        
        print(f"✅ 所有手臂动作在位置 '{location}' 执行成功。")