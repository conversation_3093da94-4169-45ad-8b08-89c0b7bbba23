import json
from copy import deepcopy

import json
import rclpy
import math

from tf_transformations import euler_from_quaternion, quaternion_from_euler, quaternion_matrix

class WorldModelManager:
    def __init__(self):
        self.obj_vision_map = {
            "tape": "tape",
            "coffee": "coffee",
            "gloves": "glove",
            "usb": "usb",
            "milk": "milk",
            "glue": "glue",
            "bolts": "bolts",
        }
        self.default_height = {
            "tape": -0.412,
            "coffee": -0.412,
            "gloves": -0.412,
            "usb": -0.412,
            "milk": -0.412,
            "glue": -0.412,
            "bolts": -0.412,
        }

        
        # 初始化处理模块
        self.mapper = ObjectMapper(self.obj_vision_map)
        self.height_adjuster = HeightAdjuster(self.default_height)
        self.orientation_filter = OrientationFilter()

        # 世界模型存储
        self.world_model_data = {
            "objects": {},
        }

    def update_model_from_vision(self, vision_data):
        """更新世界模型的视觉入口"""
        # 如果vision_data是字符串，先解析为JSON
        if isinstance(vision_data, str):
            try:
                vision_data = json.loads(vision_data)
            except json.JSONDecodeError as e:
                print(f"ERROR: Failed to parse vision_data JSON: {e}")
                return
        
        # 确保vision_data是列表
        if not isinstance(vision_data, list):
            print(f"ERROR: vision_data should be a list, got {type(vision_data)}")
            return
            
        # 第一步：重新映射物体
        objects = self.mapper.remap_objects(vision_data)
        
        # 第二步：调整高度
        self.height_adjuster.adjust(objects)
        
        # 第三步：过滤角度
        for obj_info in objects.values():
            obj_info["pose"] = self.orientation_filter.adjust_orientation(obj_info["pose"], obj_info["category"])

        sorted_objects = dict(sorted(objects.items(), 
                                   key=lambda x: x[1]["pose"][1],
                                   reverse=True))
        objects = sorted_objects
        # 更新世界模型
        self.world_model_data["objects"] = objects

    
class ObjectMapper:
    def __init__(self, obj_vision_map):
        self.reverse_map = {v: k for k, v in obj_vision_map.items()}
    
    def remap_objects(self, vision_results):
        """维护方式1：物品种类/ID重映射和排序"""
        categorized = {}
        
        # 按视觉类别分组并收集y坐标
        for item in vision_results:
            if (obj_type := self.reverse_map.get(item["category"])) is None:
                continue
            y = item["pose"][1]
            categorized.setdefault(obj_type, []).append((y, item))

        # 按y降序排序并生成ID
        objects = {}
        for obj_type, items in categorized.items():
            sorted_items = sorted(items, key=lambda x: -x[0])
            for idx, (y, item) in enumerate(sorted_items):
                obj_id = f"{obj_type}_{idx}"
                objects[obj_id] = {
                    "pose": item["pose"].copy(),
                    "category": obj_type,
                    "raw_data": item  # 保留原始数据
                }
        return objects

class HeightAdjuster:
    def __init__(self, default_heights):
        self.default_heights = default_heights
    
    def adjust(self, objects):
        """维护方式2：调整物品高度"""
        for obj_info in objects.values():
            obj_type = obj_info["category"]
            obj_info["pose"][2] = self.default_heights.get(obj_type, 0)


class OrientationFilter:
    @staticmethod
    def adjust_orientation(pose, category=None):
        """过滤roll/pitch，仅保留yaw（绕Z轴旋转）。对于Cylinder类别，yaw强制设为0"""
        x, y, z = pose[0], pose[1], pose[2]
        qx, qy, qz, qw = pose[3], pose[4], pose[5], pose[6]
        
        # 如果是Cylinder，直接返回yaw为0的四元数
        if category == "Cylinder":
            new_quat = quaternion_from_euler(0, 0, 0)
            return [x, y, z, new_quat[0], new_quat[1], new_quat[2], new_quat[3]]
        
        # 将四元数转换为旋转矩阵（注意参数顺序为[w, x, y, z]）
        rotation_matrix = quaternion_matrix([qx, qy, qz, qw])
        R = rotation_matrix[:3, :3]  # 提取3x3旋转矩阵
        
        # 直接从旋转矩阵计算yaw角
        yaw = math.atan2(R[1, 0], R[0, 0])
        if yaw < -math.pi / 2.0:
            yaw = yaw + math.pi
        if yaw > math.pi / 2.0:
            yaw = yaw - math.pi
        # 生成仅绕Z轴旋转的新四元数
        new_quat = quaternion_from_euler(0, 0, yaw)
        return [x, y, z, new_quat[0], new_quat[1], new_quat[2], new_quat[3]]
    

if __name__ == "__main__":
    manager = WorldModelManager()
    
    # 模拟视觉输入
    vision_input = [{
        "category": "tape",
        "pose": [0.1, 0.4, 0.02, 0, 0, 0, 1],  # x, y, z, qx, qy, qz, qw
        "score": 90
    }]
    
    manager.update_model_from_vision(vision_input)
    print("更新后的世界模型:", manager.world_model_data)
    


