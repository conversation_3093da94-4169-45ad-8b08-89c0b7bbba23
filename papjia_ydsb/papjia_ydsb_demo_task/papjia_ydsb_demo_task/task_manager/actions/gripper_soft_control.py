import time
import socket
from rclpy.logging import get_logger


class Gripper():
    # 夹爪角度范围定义
    MIN_ANGLE = -80   # 张开角度 (0%)
    MAX_ANGLE = -160  # 闭合角度 (100%)
    
    def __init__(self, host="**************", port=5002):
        self.host = host
        self.port = port
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.logger = get_logger('Gripper')
        
        try:
            self.sock.connect((self.host, self.port))
            self.logger.info(f"gripper controller ready, connected to {self.host}:{self.port}")
        except Exception as e:
            self.logger.error(f"connect failed: {e}")
            raise Exception(f"connect failed: {e}")

    def check_sum(self, s, length, data):
        sum = 0
        for i in range(length):
            sum = sum + data[s+i]
        return sum & 0xff

    def validate_angle(self, angle):
        """验证角度是否在安全范围内，超出范围时返回边界值"""
        if angle > self.MIN_ANGLE:  # -80度是最小值（张开），所以大于-80度就是超出范围
            self.logger.warning(f"Angle {angle}° is above minimum {self.MIN_ANGLE}°, using minimum value")
            return self.MIN_ANGLE
        elif angle < self.MAX_ANGLE:  # -150度是最大值（闭合），所以小于-150度就是超出范围
            self.logger.warning(f"Angle {angle}° is below maximum {self.MAX_ANGLE}°, using maximum value")
            return self.MAX_ANGLE
        return angle

    def percentage_to_angle(self, percentage):
        """将0-100的百分比值映射到MIN_ANGLE到MAX_ANGLE的角度范围"""
        # 限制百分比范围在0-100
        percentage = max(0, min(100, percentage))
        # 线性映射: 0% -> MIN_ANGLE, 100% -> MAX_ANGLE
        angle = self.MIN_ANGLE + (percentage / 100.0) * (self.MAX_ANGLE - self.MIN_ANGLE)
        self.logger.info(f"Percentage {percentage}% -> Angle {angle}° (MIN: {self.MIN_ANGLE}°, MAX: {self.MAX_ANGLE}°)")
        return angle

    def send_cmd(self, id, value, is_percentage=True):
        if is_percentage:
            angle = self.percentage_to_angle(value)
        else:
            angle = value
        
        # 严格检查角度范围，防止夹爪损坏
        angle = self.validate_angle(angle)
        self.logger.info(f"Sending command: ID={id}, Final Angle={angle}°")
            
        angle = (int)(angle * 1000)
        cmd = [0x3E, 0xA3, 0x01, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]
        cmd[2] = id & 0xff
        cmd[4] = self.check_sum(0, 4, cmd)
        cmd[5] = int(angle) & 0xff
        cmd[6] = (int(angle) >> 8) & 0xff
        cmd[7] = (int(angle) >> 16) & 0xff
        cmd[8] = (int(angle) >> 24) & 0xff
        cmd[9] = (int(angle) >> 32) & 0xff
        cmd[10] = (int(angle) >> 40) & 0xff
        cmd[11] = (int(angle) >> 48) & 0xff
        cmd[12] = (int(angle) >> 56) & 0xff
        cmd[13] = self.check_sum(5, 8, cmd)
        
        try:
            self.sock.send(bytes(cmd))
        except Exception as e:
            self.logger.error(f"send command failed: {e}")
            raise

    def set_percentage(self, percentage):
        """设置夹爪开合百分比 (0-100)"""
        self.send_cmd(1, percentage, is_percentage=True)
        time.sleep(0.5)
        self.logger.info(f"set gripper to {percentage}%")

    def open(self):
        self.send_cmd(1, 0, is_percentage=True)  # 0% = 张开
        time.sleep(0.5)
        self.logger.info("open gripper")
    
    def close(self):
        self.send_cmd(1, 100, is_percentage=True)  # 100% = 闭合
        time.sleep(0.5)
        self.logger.info("close gripper")

    def __del__(self):
        if hasattr(self, 'sock'):
            self.sock.close()

# from ..core.setup_strategies import register_setup_command, SetupCommandStrategy

# @register_setup_command("gripper_soft")
# class GripperSoftStrategy(SetupCommandStrategy): 
#     def can_handle(self, command: str) -> bool:
#         return command == "gripper_soft"
    
#     def execute(self, params: dict, task_builder) -> None:
#         gripper = Gripper("**************", 5001)
#         gripper.set_percentage(params['percentage'])
#         time.sleep(1.0)


if __name__ == "__main__":
    gripper = Gripper("**************", 5001)
    gripper.open()
    time.sleep(1)
    # gripper.close()
    # time.sleep(1)
    # gripper.open()
    
    # time.sleep(1)
    gripper.set_percentage(0)
    # time.sleep(1)
    # gripper.set_percentage(30)
    # time.sleep(1)
    # gripper.set_percentage(50)
    # time.sleep(1)
    # gripper.set_percentage(20)
    # time.sleep(1)
    # gripper.open()
