import rclpy
import json
import os


from papjia_ydsb_demo_task.task_manager.tasks.box.box_operation import BoxOperation
from papjia_ydsb_demo_task.task_manager.core.task_builder import TaskBuilder

rclpy.init()
step_run = False

waypoint_file_path = os.getenv('WAYPOINT_CONFIG_FILEPATH', "/workspace/src/papjia_ydsb/papjia_ydsb_config/config/waypoint_configs.json")
with open(waypoint_file_path, "r") as f:
    all_waypoint_configs = json.load(f)

box_operation = BoxOperation()

task_builder = TaskBuilder(debug=False)
task_builder.register_object_waypoint_configs(box_operation.object_name, all_waypoint_configs['glass_tube'])
task_builder.register_object_action_configs(box_operation.object_name, box_operation.actions)

task_sequence = [
        (box_operation.object_name, '回原点'),
        (box_operation.object_name, '到过渡点'),
        (box_operation.object_name, '准备搬框'),
        (box_operation.object_name, '夹框'),
        (box_operation.object_name, '搬起框'),
        (box_operation.object_name, '把框抱到胸前'),
        (box_operation.object_name, '等待信号'),
        (box_operation.object_name, '把框送前去'),
        (box_operation.object_name, '松开框'),
    ]

task_builder.execute_task_sequence(task_sequence, step_run)
rclpy.shutdown()