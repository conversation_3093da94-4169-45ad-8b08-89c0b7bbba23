class BoxOperation(object):
    def __init__(self):
        self.object_name = "box_operation"

        self.actions = {
            '回原点': [
                ('parallel', '回原点', [
                    [('arm', {'waypoints': ['home_left'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['home_right'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            '到过渡点': [
                ('parallel', '拉框准备1', [
                    [('arm', {'waypoints': ['左手-框-过渡'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右手-框-过渡'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            '准备搬框': [
                ('parallel', '拉框准备2', [
                    [('arm', {'waypoints': ['左手-框-左边'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右手-框-右边'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],
            '夹框': [
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['左手-框-左边'],
                        'offset': {'y': -0.1},
                        'object_name': self.object_name
                    })
                ]),
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['右手-框-右边'],
                        'offset': {'y': 0.1},
                        'object_name': self.object_name
                    })
                ]),
                ('parallel', '拉框准备', [
                    [('arm', {'waypoints': ['左手-框-左边'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右手-框-右边'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],
            '拉框': [
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['左手-框-左边'],
                        'offset': {'x': -0.1},
                        'object_name': self.object_name
                    })
                ]),
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['右手-框-右边'],
                        'offset': {'x': -0.1},
                        'object_name': self.object_name
                    })
                ]),
                ('parallel', '拉框就绪', [
                    [('arm', {'waypoints': ['左手-框-左边'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右手-框-右边'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            '再次夹框': [
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['左手-框-左边'],
                        'offset': {'y': 0.05},
                        'object_name': self.object_name
                    })
                ]),
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['右手-框-右边'],
                        'offset': {'y': -0.05},
                        'object_name': self.object_name
                    })
                ]),
                ('parallel', '拉框准备', [
                    [('arm', {'waypoints': ['左手-框-左边'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右手-框-右边'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['左手-框-左边'],
                        'offset': {'x': 0.1},
                        'object_name': self.object_name
                    })
                ]),
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['右手-框-右边'],
                        'offset': {'x': 0.1},
                        'object_name': self.object_name
                    })
                ]),
                ('parallel', '拉框就绪', [
                    [('arm', {'waypoints': ['左手-框-左边'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右手-框-右边'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['左手-框-左边'],
                        'offset': {'y': -0.05},
                        'object_name': self.object_name
                    })
                ]),
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['右手-框-右边'],
                        'offset': {'y': 0.05},
                        'object_name': self.object_name
                    })
                ]),
                ('parallel', '拉框准备', [
                    [('arm', {'waypoints': ['左手-框-左边'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右手-框-右边'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],
            '搬起框': [
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['左手-框-左边'],
                        'offset': {'z': 0.15},
                        'object_name': self.object_name
                    })
                ]),
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['右手-框-右边'],
                        'offset': {'z': 0.15},
                        'object_name': self.object_name
                    })
                ]),
                ('parallel', '拉框准备', [
                    [('arm', {'waypoints': ['左手-框-左边'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右手-框-右边'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            '把框抱到胸前': [
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['左手-框-左边'],
                        'offset': {'x': -0.1},
                        'object_name': self.object_name
                    })
                ]),
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['右手-框-右边'],
                        'offset': {'x': -0.1},
                        'object_name': self.object_name
                    })
                ]),
                ('parallel', '拉框准备', [
                    [('arm', {'waypoints': ['左手-框-左边'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右手-框-右边'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            '等待信号': [
                ('sequence', '等待信息', [
                    ('pause', {})
                ])
            ],
            
            '把框送前去': [
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['左手-框-左边'],
                        'offset': {'x': 0.1},
                        'object_name': self.object_name
                    })
                ]),
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['右手-框-右边'],
                        'offset': {'x': 0.1},
                        'object_name': self.object_name
                    })
                ]),
                ('parallel', '拉框准备', [
                    [('arm', {'waypoints': ['左手-框-左边'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右手-框-右边'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            
            '松开框': [
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['左手-框-左边'],
                        'offset': {'y': 0.09},
                        'object_name': self.object_name
                    })
                ]),
                ('setup', '设置拉框参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['右手-框-右边'],
                        'offset': {'y': -0.09},
                        'object_name': self.object_name
                    })
                ]),
                ('parallel', '拉框准备', [
                    [('arm', {'waypoints': ['左手-框-左边'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右手-框-右边'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],
        }