import rclpy
import sys
import json
import os


from papjia_ydsb_demo_task.task_manager.tasks.guide.guide_operation import GuideOperation
from papjia_ydsb_demo_task.task_manager.core.task_builder import TaskBuilder


rclpy.init()
step_run = False

# waypoint_file_path = "/workspace/src/papjia_ydsb/papjia_ydsb_config/config/zt_configs.json"
# with open(waypoint_file_path, "r") as f:
#     all_waypoint_configs = json.load(f)

guide_operation = GuideOperation()

task_builder = TaskBuilder(debug=False)

# task_builder.register_object_waypoint_configs(guide_operation.object_name, all_waypoint_configs["guide_operation"])
task_builder.register_object_action_configs(guide_operation.object_name, guide_operation.actions)

task_sequence = [
        # (guide_operation.object_name, '导航测试')
        (guide_operation.object_name, '解说'),
    ]

task_builder.execute_task_sequence(task_sequence, step_run)
rclpy.shutdown()