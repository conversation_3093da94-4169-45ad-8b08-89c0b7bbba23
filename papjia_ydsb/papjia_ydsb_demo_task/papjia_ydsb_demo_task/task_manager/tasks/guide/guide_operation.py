class GuideOperation(object):
    def __init__(self):
        self.object_name = "guide_operation"

        self.actions = {
            '导航测试': [
                ('setup', '导航测试', [
                    ('navigation', {'cmd': 'navigate_with_nav2_location', 'location': "厂区分布图"}),
                ]),
            ],
            "解说": [
                ('setup', '解说', [
                    ('tts', {'location': '厂区分布图'}),
                ]),
            ],
            
        }