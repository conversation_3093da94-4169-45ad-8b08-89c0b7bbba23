import rclpy
import sys
import json
import os
import time

from papjia_ydsb_demo_task.task_manager.tasks.show.show_operation import ShowOperation
from papjia_ydsb_demo_task.task_manager.core.task_builder import TaskBuilder
from papjia_ydsb_demo_task.task_manager.tasks.gripper.gripper_operation import GripperOperation

rclpy.init()
step_run = False

waypoint_file_path = "/workspace/src/papjia_ydsb/papjia_ydsb_config/config/show_waypoint_configs.json"
with open(waypoint_file_path, "r") as f:
    all_waypoint_configs = json.load(f)

show_operation = ShowOperation()
gripper_operation = GripperOperation()
task_builder = TaskBuilder(debug=False)

task_builder.register_object_waypoint_configs(show_operation.object_name, all_waypoint_configs[show_operation.object_name])
task_builder.register_object_action_configs(show_operation.object_name, show_operation.actions)

task_builder.register_object_action_configs(gripper_operation.object_name, gripper_operation.actions)

task_sequence = [
        (show_operation.object_name, '去折叠点'),
        (show_operation.object_name, '去向下点'),
        (show_operation.object_name, '去展示夹具点'),
        (gripper_operation.object_name, '夹具测试'),
        (show_operation.object_name, '去折叠点')
    ]


i = 0
while(i<1):
    i += 1
    print(f"第{i}次执行")
    # 执行任务序列
    task_builder.execute_task_sequence(task_sequence, step_run)

rclpy.shutdown()
