class ShowOperation(object):
    def __init__(self):
        self.object_name = "show_operation"

        self.actions = {

            '回原点': [
                ('parallel', '回原点', [
                    [('arm', {'waypoints': ['home_left'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['home_right'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],
            '去折叠点': [
                ('parallel', '去折叠点', [
                    [('arm', {'waypoints': ['左臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            '去展示夹具点': [
                ('parallel', '去展示夹具点', [
                    [('arm', {'waypoints': ['左臂-展示夹具点'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右臂-展示夹具点'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            '去向下点': [
                ('parallel', '去向下点', [
                    [('arm', {'waypoints': ['左臂-向下点'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右臂-向下点'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],

            '开关爪子': [
                ('setup', '开关爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('gripper', {'action': 'open'}),
                    ('gripper', {'action': 'close'}),
                    ('gripper', {'action': 'open'}),
                ]),

                ('setup', '开关爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('gripper', {'action': 'open'}),
                    ('gripper', {'action': 'close'}),
                    ('gripper', {'action': 'open'}),
                ]),
                
            ],

        }