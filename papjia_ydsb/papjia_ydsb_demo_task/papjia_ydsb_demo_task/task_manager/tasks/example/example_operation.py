class ExampleOperation(object):
    """
    示例操作类，用于演示如何定义和调用操作
    """
    def __init__(self):
        self.object_name = "example_operation"

        self.actions = {
            'move': [
                ('setup', '移动完成提示', [
                    ('tts', {'text': '机器人在位置间移动'})
                ])
            ],
            'take_with_hand': [
                ('setup', '抓取完成提示', [
                    ('tts', {'text': '机器人单手从智能体处取物体'})
                ])
            ],
            'pickup_with_hand': [
                ('setup', '放置完成提示', [
                    ('tts', {'text': '机器人单手从位置拾取物体'})
                ])
            ],
            'give_with_hand': [
                ('setup', '语音提示导航', [
                    ('tts', {'text': '机器人单手将物体传递给智能体'})
                ]),
            ],
            'put_with_hand': [
                ('setup', '语音提示回家', [
                    ('tts', {'text': '机器人单手将物体放置到位置'})
                ]),
            ],
        }
