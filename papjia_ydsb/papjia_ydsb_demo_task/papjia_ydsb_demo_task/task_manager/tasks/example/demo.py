import rclpy
import json
import os

from papjia_ydsb_demo_task.task_manager.tasks.example.example_operation import ExampleOperation
from papjia_ydsb_demo_task.task_manager.core.task_builder import TaskBuilder

rclpy.init()
step_run = False


# 创建示例操作实例
example_operation = ExampleOperation()
# 创建任务构建器
task_builder = TaskBuilder(debug=False)
# 定义任务序列 - 现在每个动作都有语音提示
print("开始执行示例任务序列...")
print("每个动作都会发出语音指令...")
task_builder.register_object_action_configs(example_operation.object_name, example_operation.actions)

# 基础操作序列
task_sequence = [
    (example_operation.object_name, 'move'),
    (example_operation.object_name, 'take_with_hand'),
    (example_operation.object_name, 'pickup_with_hand'),
    (example_operation.object_name, 'give_with_hand'),
    (example_operation.object_name, 'put_with_hand'),
]

# 执行任务序列
print(f"开始执行任务序列，共{len(task_sequence)}个任务...")
task_builder.execute_task_sequence(task_sequence, step_run)
print("任务序列执行完成")

rclpy.shutdown()
