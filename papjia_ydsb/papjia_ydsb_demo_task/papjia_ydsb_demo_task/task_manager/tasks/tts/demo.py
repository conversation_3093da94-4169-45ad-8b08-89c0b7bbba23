import rclpy
import sys
import json
import os

from papjia_ydsb_demo_task.task_manager.tasks.tts.tts_operation import TTSOperation
from papjia_ydsb_demo_task.task_manager.core.task_builder import TaskBuilder

rclpy.init()
step_run = True

tts_operation = TTSOperation()

task_builder = TaskBuilder(debug=False)

task_builder.register_object_action_configs(tts_operation.tts_name, tts_operation.actions)

task_sequence = [
        (tts_operation.tts_name, '说话'),
    ]

task_builder.execute_task_sequence(task_sequence)
rclpy.shutdown()