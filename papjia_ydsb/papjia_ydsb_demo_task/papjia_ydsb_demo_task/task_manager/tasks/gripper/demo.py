import rclpy
import sys
import json
import os


from papjia_ydsb_demo_task.task_manager.tasks.gripper.gripper_operation import GripperOperation
from papjia_ydsb_demo_task.task_manager.core.task_builder import TaskBuilder


rclpy.init()
step_run = True

waypoint_file_path = "/workspace/src/papjia_ydsb/papjia_ydsb_config/config/waypoint_configs.json"
with open(waypoint_file_path, "r") as f:
    all_waypoint_configs = json.load(f)

gripper_operation = GripperOperation()

task_builder = TaskBuilder(debug=False)

task_builder.register_object_waypoint_configs(gripper_operation.object_name, all_waypoint_configs["glass_tube"])
task_builder.register_object_action_configs(gripper_operation.object_name, gripper_operation.actions)

task_sequence = [
        (gripper_operation.object_name, '夹具测试'),
    ]

task_builder.execute_task_sequence(task_sequence)
rclpy.shutdown()