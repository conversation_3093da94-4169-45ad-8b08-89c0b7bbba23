class GripperOperation(object):
    def __init__(self):
        self.object_name = "gripper_operation"

        self.actions = {
            '夹具测试': [
                ('setup', '夹具测试', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                    
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                    
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_close', 'value': '100'}),
                    ('gripper', {'action': 'close'}),
                    
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_close', 'value': '100'}),
                    ('gripper', {'action': 'close'}),
                    
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                    
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),

                # ('setup', '夹具开', [
                #     ('gripper', {'percentage': 0, 'blackboard': 'arm_prefix'}),
                #     ('delay', {'seconds': 3}),
                # ])
            ]
        }