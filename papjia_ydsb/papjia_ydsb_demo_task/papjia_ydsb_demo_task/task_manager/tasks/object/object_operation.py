class ObjectOperation(object):
    def __init__(self):
        self.object_name = "object_operation"

        self.pre_height = 0.15
        self.after_height = 0.15

        self.object_functionality = {
            'tape': {
                'left_grasp_offset': [0.01, 0, 0.04, 0, 0, 0.0],
                'right_grasp_offset': [0.01, -0.015, 0.08, 0, 0, 0.0],
                'left_gripper_open': 0,
                'left_gripper_close': 95,
                'right_gripper_open': 0,
                'right_gripper_close': 100,
                'is_cylinder': True
            },
            'gloves': {
                'left_grasp_offset': [-0.02, 0, 0.028, 0, 0, 0.0],
                'right_grasp_offset': [-0.02, 0, 0.07, 0, 0, 0.0],
                'left_gripper_open': 0,
                'left_gripper_close': 100,
                'right_gripper_open': 0,
                'right_gripper_close': 100,
                'is_cylinder': True
            },
            'usb': {
                'left_grasp_offset': [-0.02, 0.01, 0.028, 0, 0, 0.0],
                'right_grasp_offset': [-0.02, 0, 0.07, 0, 0, 0.0],
                'left_gripper_open': 0,
                'left_gripper_close': 95,
                'right_gripper_open': 0,
                'right_gripper_close': 100,
                'is_cylinder': False
            },
            'glue': {
                'left_grasp_offset': [0.02, 0, 0.04, 0, 0, 0.0],
                'right_grasp_offset': [0.02, 0.01, 0.07, 0, 0, 0.0],
                'left_gripper_open': 0,
                'left_gripper_close': 95,
                'right_gripper_open': 0,
                'right_gripper_close': 70,
                'is_cylinder': False
            },
            'bolts': {
                'left_grasp_offset': [-0.02, 0, 0.030, 0, 0, 0.0],
                'right_grasp_offset': [-0.02, 0, 0.07, 0, 0, 0.0],
                'left_gripper_open': 0,
                'left_gripper_close': 100,
                'right_gripper_open': 0,
                'right_gripper_close': 100,
                'is_cylinder': True
            }
        }

        self.actions = {

            '回原点': [
                ('parallel', '回原点', [
                    [('arm', {'waypoints': ['home_left'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['home_right'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],
            '去初始点': [
                ('parallel', '去初始点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            '去折叠点': [
                ('parallel', '去折叠点', [
                    [('arm', {'waypoints': ['左臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            "交互-0": [
                ('setup', '说话', [
                    ('tts', {'text': '您好,您需要什么,我们有矿泉水,可乐,雪碧'}),
                    ('delay', {'seconds': 3}),
                    ('tts', {'text': '确认一下,您是要矿泉水?'}),
                    ('delay', {'seconds': 1}),
                    ('tts', {'text': '好的,请稍等'})
                ])
            ],
            "交互-0.5": [
                ('setup', '说话', [
                    ('tts', {'text': '请给我一瓶矿泉水'}),
                ]),
            ],
            "拿水": [
                ('sequence', '拿水', [
                    ('arm', {'waypoints': ['右臂-拿水'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
            ],

            "交互-0.6": [
                ('setup', '说话', [
                    ('tts', {'text': '给错了,我要的是矿泉水'}),
                    ('delay', {'seconds': 5}),
                    ('tts', {'text': '已拿到矿泉水'}),
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_close', 'value': '100'}),
                    ('gripper', {'action': 'close'}),
                ])
            ],
            "交互-1": [
                ('setup', '说话', [
                    ('tts', {'text': '好的，您是要我去工具桌，看看上面有什么东西，如果有U盘的话把他带给你吗'}),
                    ('delay', {'seconds': 3}),
                    ('tts', {'text': '好的，请稍等'})
                ])
            ],
            "交互-2": [
                ('setup', '说话', [
                    ('tts', {'text': '我看到桌上有手套、胶带和物料框。这是U盘。'}),
                    ('delay', {'seconds': 10}),
                    ('tts', {'text': '您是要我把U盘带给客人,然后整理桌面,把物料框带给你吗'}),
                    ('delay', {'seconds': 1}),
                    ('tts', {'text': '好的，请稍等'})
                ])
            ],

            "交互-2.5": [
                ('setup', '说话', [
                    ('tts', {'text': '您好,U盘和水'}),
                    ('delay', {'seconds': 2}),
                ]),
                ('parallel', '去给水点', [
                    [('arm', {'waypoints': ['左臂-给水'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右臂-给水'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('setup', '说话', [
                    ('tts', {'text': '您不要U盘是吗'}),
                    ('delay', {'seconds': 1}),
                    ('tts', {'text': '好的,这是水'}),
                ]),

            ],
            "给水": [
                ('setup', '给水', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                    ('delay', {'seconds': 4}),
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_close', 'value': '100'}),
                    ('gripper', {'action': 'close'}),

                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),

                ]),

            ],
            "设左手": [
                ('setup', '给水', [

                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_close', 'value': '100'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),

                ]),

            ],

            "交互-3": [
                ('setup', '说话', [
                    ('tts', {'text': '您好，您要的物料框'}),
                ])
            ],

            "交互-前往工具桌": [
                ('setup', '说话', [
                    ('tts', {'text': '准备前往工具桌'}),
                ])
            ],

            "交互-前往柜台": [
                ('setup', '说话', [
                    ('tts', {'text': '准备前往柜台'}),
                ])
            ],

            "交互-前往餐桌": [
                ('setup', '说话', [
                    ('tts', {'text': '准备前往餐桌'}),
                ])
            ],

            "交互-准备放框": [
                ('setup', '说话', [
                    ('tts', {'text': '准备前往柜台'}),
                ])
            ],

            '识别': [
                # ('setup', '说话', [
                #     ('tts', {'text': '准备开始进行物体识别'})
                # ]),
                ('sequence', '识别', [
                    ('object_detect', {'service_name': '/papjia_vision/service_object_detect', 'max_num': 8, 'min_score': 0.7, 'keys': {'detect_objects': 'detect_objects'}}),
                ]),
                # ('setup', '说话', [
                #     ('tts', {'text': '更新世界模型'})
                # ]),
                ('setup', '导入世界模型', [
                    ('update_world_model', {'vision_data': 'detect_objects', 'world_model': 'world_model_data'}),
                ]),  
            ],
            # 0.30000005396229784,-0.3000000641293728
            '垂直抓取任意物体': [
                ('setup', '更新位姿', [
                    ('update_target', {'world_model': 'world_model_data', 
                                       'object_name': self.object_name, 
                                       'prefer': 'any', 
                                       'functionality': self.object_functionality,
                                       'waypoint_name': '垂直抓取-就绪', 
                                       'frame_id': 'arm_base'}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-就绪', 
                                              'object_name': self.object_name}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-初始', 
                                              'object_name': self.object_name}),
                ]),   
                # ('setup', '说话', [
                #     ('tts', {'text': '准备抓取物体'})
                # ]),
                ('setup', '开爪子', [
                    ('gripper', {'action': 'open'}),
                ]),
                ('setup', '调整预备点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去预备点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '调整就绪点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去就绪点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '关爪子', [
                    ('gripper', {'action': 'close'}),
                    ('delay', {'seconds': 2}),
                ]),
                ('setup', '调整撤退点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去撤退点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '重置就绪点', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去初始点', [
                    ('arm', {'waypoints': ['垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
            ],
            '放框': [

                ('setup', '更新位姿', [
                    # ('tts', {'text': '准备把物体放入物料框内'}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '放入框内-初始', 
                                              'object_name': self.object_name}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '放入框内-预备', 
                                              'object_name': self.object_name}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '放入框内-就绪', 
                                              'object_name': self.object_name}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '放入框内-撤退', 
                                              'object_name': self.object_name}),
                ]), 
                ('sequence', '去预备点', [
                    ('arm', {'waypoints': ['放入框内-预备'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('sequence', '去就绪点', [
                    ('arm', {'waypoints': ['放入框内-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '开爪子', [
                    ('gripper', {'action': 'open'}),
                ]),
                ('setup', '关爪子', [
                    ('gripper', {'action': 'close'}),
                ]),
                ('sequence', '去撤退点', [
                    ('arm', {'waypoints': ['放入框内-撤退'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('sequence', '回初始点', [
                    ('arm', {'waypoints': ['放入框内-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
            ],

            # U盘一定要左手抓，放左边
            '垂直抓取U盘': [
                ('setup', '更新位姿', [
                    ('update_target', {'world_model': 'world_model_data', 
                                       'object_name': self.object_name, 
                                       'prefer': 'usb',# 'usb', 
                                       'functionality': self.object_functionality,
                                       'waypoint_name': '垂直抓取-就绪', 
                                       'frame_id': 'arm_base'}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-就绪', 
                                              'object_name': self.object_name}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-初始', 
                                              'object_name': self.object_name}),
                ]),   
                # ('setup', '说话', [
                #     ('tts', {'text': '准备抓取U盘'})
                # ]),
                ('setup', '开爪子', [
                    ('gripper', {'action': 'open'}),
                ]),
                ('setup', '调整预备点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去预备点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '调整就绪点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去就绪点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '关爪子', [
                    ('gripper', {'action': 'close'}),
                ]),
                ('setup', '调整撤退点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去撤退点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '重置就绪点', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去初始点', [
                    ('arm', {'waypoints': ['垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
            ],

            '把U盘放桌上': [
                ('sequence', '去预备点', [
                    ('arm', {'waypoints': ['左臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('sequence', '去就绪点', [
                    ('arm', {'waypoints': ['左臂-放桌上-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '开爪子', [
                    ('gripper', {'action': 'open'}),
                ]),
                ('setup', '关爪子', [
                    ('gripper', {'action': 'close'}),
                ]),
                ('sequence', '回初始点', [
                    ('arm', {'waypoints': ['左臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
            ],
            
            '反向垂直抓取任意物体': [
                ('setup', '更新位姿', [
                    ('update_target', {'world_model': 'world_model_data', 
                                       'object_name': self.object_name, 
                                       'prefer': 'any_reverse', 
                                       'functionality': self.object_functionality,
                                       'waypoint_name': '垂直抓取-就绪', 
                                       'frame_id': 'arm_base'}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-就绪', 
                                              'object_name': self.object_name}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-初始', 
                                              'object_name': self.object_name}),
                ]),   
                # ('setup', '说话', [
                #     ('tts', {'text': '准备抓取物体'})
                # ]),
                ('setup', '开爪子', [
                    ('gripper', {'action': 'open'}),
                ]),
                ('setup', '调整预备点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去预备点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '调整就绪点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去就绪点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '关爪子', [
                    ('gripper', {'action': 'close'}),
                     ('delay', {'seconds': 2}),
                ]),
                ('setup', '调整撤退点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去撤退点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '重置就绪点', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去初始点', [
                    ('arm', {'waypoints': ['垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
            ],            
        }
