import rclpy
import sys
import json
import os

from papjia_ydsb_demo_task.task_manager.tasks.object.object_operation import ObjectOperation
from papjia_ydsb_demo_task.task_manager.tasks.box.box_operation import BoxOperation
from papjia_ydsb_demo_task.task_manager.tasks.navigation.navigation_operation import NavigationOperation
from papjia_ydsb_demo_task.task_manager.core.task_builder import TaskBuilder

rclpy.init()
step_run = False

waypoint_file_path = "/workspace/src/papjia_ydsb/papjia_ydsb_config/config/waypoint_configs.json"
with open(waypoint_file_path, "r") as f:
    all_waypoint_configs = json.load(f)

object_operation = ObjectOperation()
task_builder = TaskBuilder(debug=False)

task_builder.register_object_waypoint_configs(object_operation.object_name, all_waypoint_configs[object_operation.object_name])
task_builder.register_object_action_configs(object_operation.object_name, object_operation.actions)

navigation_operation = NavigationOperation()
task_builder.register_object_action_configs(navigation_operation.object_name, navigation_operation.actions)

box_operation = BoxOperation()
task_builder.register_object_waypoint_configs(box_operation.object_name, all_waypoint_configs['glass_tube'])
task_builder.register_object_action_configs(box_operation.object_name, box_operation.actions)

task_sequence = [
        # (object_operation.object_name, '去折叠点'),
        # (box_operation.object_name, '等待信号'),
        # (navigation_operation.object_name, '去餐桌'),
        # (object_operation.object_name, '交互-0'),

        # (navigation_operation.object_name, '去吧台'),
        # (object_operation.object_name, '交互-0.5'),
        # (object_operation.object_name, '拿水'),
        # (object_operation.object_name, '交互-0.6'),
        # (object_operation.object_name, '去折叠点'),
        # (box_operation.object_name, '等待信号'),# 等待客人指令
        # (object_operation.object_name, '交互-1'),


        # (object_operation.object_name, '交互-前往工具桌'),
        # (navigation_operation.object_name, '去工作台'),

        # (object_operation.object_name, '识别'),
        # (object_operation.object_name, '去初始点'),
        # (object_operation.object_name, '垂直抓取U盘'),
        # (object_operation.object_name, '去折叠点'),

        # (object_operation.object_name, '交互-前往柜台'),
        # (navigation_operation.object_name, '去吧台'),
        # (object_operation.object_name, '交互-2'),

        # (object_operation.object_name, '交互-前往餐桌'),
        # (navigation_operation.object_name, '去餐桌'),
        # (object_operation.object_name, '交互-2.5'),

        # (box_operation.object_name, '等待信号'),
        # (object_operation.object_name, '给水'),
        # (object_operation.object_name, '把U盘放桌上'), #(object_operation.object_name, '把U盘放桌上'),
        # (object_operation.object_name, '去折叠点'),
        # (object_operation.object_name, '设左手'),
        # (object_operation.object_name, '交互-前往工具桌'),
        # (navigation_operation.object_name, '去工作台'),

        # (object_operation.object_name, '放框'),

        (object_operation.object_name, '去折叠点'),
        #(box_operation.object_name, '等待信号'),
        (object_operation.object_name, '识别'),
        (object_operation.object_name, '去初始点'),
        (object_operation.object_name, '垂直抓取任意物体'),
        # (object_operation.object_name, '放框'),

        # (object_operation.object_name, '识别'),
        # (object_operation.object_name, '去初始点'),
        # (object_operation.object_name, '反向垂直抓取任意物体'),
        # (object_operation.object_name, '放框'),


        # (box_operation.object_name, '回原点'),
        # (box_operation.object_name, '到过渡点'),
        # (box_operation.object_name, '准备搬框'),
        # (box_operation.object_name, '夹框'),
        # (box_operation.object_name, '搬起框'),
        # (box_operation.object_name, '把框抱到胸前'),
        # (object_operation.object_name, '交互-前往柜台'),
        # (navigation_operation.object_name, '去给框'),
        # (object_operation.object_name, '交互-3'),
        # (box_operation.object_name, '等待信号'),
        # (box_operation.object_name, '把框送前去'),
        # (box_operation.object_name, '松开框'),

        (object_operation.object_name, '去折叠点'),
        # 机器人走开
    ]

task_builder.execute_task_sequence(task_sequence, step_run)
rclpy.shutdown()
