class NavigationOperation(object):
    def __init__(self):
        self.object_name = "navigation_operation"

        self.actions = {
            '导航测试': [
                ('setup', '导航测试', [
                    ('navigation', {'cmd': 'move_forward', 'distance': 1.0}),
                ]),
                ('sequence', '等待信息', [
                    ('pause', {})
                ]),
                ('setup', '导航测试', [
                    ('navigation', {'cmd': 'move_forward', 'distance': -1.0}),
                ]),
                ('sequence', '等待信息', [
                    ('pause', {})
                ]),
                ('setup', '导航测试', [
                    ('navigation', {'cmd': 'navigate_with_nav2', 'x': 0.0, 'y': 0.0, 'yaw': 10.0}),
                ]),
                ('sequence', '等待信息', [
                    ('pause', {})
                ]),
                ('setup', '导航测试', [
                    ('navigation', {'cmd': 'navigate_to_pose', 'x': 1.0, 'y': 0.1, 'yaw': -45.0}),
                ]),
                ('sequence', '等待信息', [
                    ('pause', {})
                ]),
            ],
            '去吧台': [
                ('setup', '去吧台', [
                    ('navigation', {'cmd': 'navigate_with_nav2', 'x': 6.388, 'y': -0.304, 'yaw': -13.7}),
                ]),                
            ],
            '去给框': [
                ('setup', '去给框', [
                    ('navigation', {'cmd': 'navigate_with_nav2', 'x': 5.212, 'y': -0.358, 'yaw': -54.976}),
                ]),                
            ],
            '去餐桌': [
                ('setup', '去餐桌', [
                    ('navigation', {'cmd': 'navigate_with_nav2', 'x': 2.6, 'y': 0.963, 'yaw': 24.0}),
                ]),                
            ],
            '去工作台': [
                ('setup', '去工作台', [
                    ('navigation', {'cmd': 'navigate_with_nav2', 'x': 0.486, 'y': 1.766, 'yaw': 170.488}),
                    ('navigation', {'cmd': 'dock_robot', 'dock_id': "home_dock"}),
                    
                ]),   
                # ('setup', '去工作台', [
                #     ('navigation', {'cmd': 'move_forward', 'distance': 1.3}),
                    # ('navigation', {'cmd': 'navigate_to_pose', 'x': -0.416, 'y': 2.055, 'yaw': 170.488}),
                    
                # ]),  
                # ('sequence', '等待信息', [
                #     ('pause', {})
                # ]),             
            ],
            '对码': [
                ('setup', '对码', [
                    ('navigation', {'cmd': 'dock_robot', 'dock_id': "home_dock"}),
                ]),                
            ],
            '退后': [
                ('setup', '退后', [
                    ('navigation', {'cmd': 'move_forward', 'distance': -0.5}),
                ]), 
                ('sequence', '等待信息', [
                    ('pause', {})
                ]),                
            ]
        }