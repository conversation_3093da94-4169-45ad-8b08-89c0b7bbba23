import rclpy
import sys
import json
import os


from papjia_ydsb_demo_task.task_manager.tasks.navigation.navigation_operation import NavigationOperation
from papjia_ydsb_demo_task.task_manager.core.task_builder import TaskBuilder


rclpy.init()
step_run = False

waypoint_file_path = "/workspace/src/papjia_ydsb/papjia_ydsb_config/config/waypoint_configs.json"
with open(waypoint_file_path, "r") as f:
    all_waypoint_configs = json.load(f)

navigation_operation = NavigationOperation()

task_builder = TaskBuilder(debug=False)

task_builder.register_object_waypoint_configs(navigation_operation.object_name, all_waypoint_configs["glass_tube"])
task_builder.register_object_action_configs(navigation_operation.object_name, navigation_operation.actions)

task_sequence = [
        # (navigation_operation.object_name, '去吧台'),
        # (navigation_operation.object_name, '去餐桌'),
        # (navigation_operation.object_name, '去工作台'),
        # (navigation_operation.object_name, '去给框'),
        (navigation_operation.object_name, '测试'),
    ]

task_builder.execute_task_sequence(task_sequence, step_run)
rclpy.shutdown()