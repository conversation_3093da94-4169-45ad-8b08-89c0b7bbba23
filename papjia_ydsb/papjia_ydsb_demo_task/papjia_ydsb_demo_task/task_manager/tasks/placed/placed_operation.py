class PlacedOperation(object):
    def __init__(self):
        self.object_name = "placed_operation"

        self.pre_height = 0.15
        self.after_height = 0.15

        self.object_functionality = {
            'tape': {
                'left_grasp_offset': [0.01, 0, 0.04, 0, 0, 0.0],
                'right_grasp_offset': [0.01, -0.015, 0.08, 0, 0, 0.0],
                'left_gripper_open': 0,
                'left_gripper_close': 95,
                'right_gripper_open': 0,
                'right_gripper_close': 100,
                'is_cylinder': True
            },
            'gloves': {
                'left_grasp_offset': [-0.02, 0, 0.028, 0, 0, 0.0],
                'right_grasp_offset': [-0.02, 0, 0.07, 0, 0, 0.0],
                'left_gripper_open': 0,
                'left_gripper_close': 100,
                'right_gripper_open': 0,
                'right_gripper_close': 100,
                'is_cylinder': True
            },
            'usb': {
                'left_grasp_offset': [-0.02, 0.01, 0.028, 0, 0, 0.0],
                'right_grasp_offset': [-0.02, 0, 0.07, 0, 0, 0.0],
                'left_gripper_open': 0,
                'left_gripper_close': 95,
                'right_gripper_open': 0,
                'right_gripper_close': 100,
                'is_cylinder': False
            },
            'glue': {
                'left_grasp_offset': [0.02, 0, 0.04, 0, 0, 0.0],
                'right_grasp_offset': [0.02, 0.01, 0.07, 0, 0, 0.0],
                'left_gripper_open': 0,
                'left_gripper_close': 95,
                'right_gripper_open': 0,
                'right_gripper_close': 70,
                'is_cylinder': False
            },
            'bolts': {
                'left_grasp_offset': [-0.02, 0, 0.030, 0, 0, 0.0],
                'right_grasp_offset': [-0.02, 0, 0.07, 0, 0, 0.0],
                'left_gripper_open': 0,
                'left_gripper_close': 100,
                'right_gripper_open': 0,
                'right_gripper_close': 100,
                'is_cylinder': True
            }
        }

        # self.object_functionality = {
        #     'tape': {
        #         'left_grasp_offset': [0.0, 0.0, 0.3, -81.351, 58.869, 87.205],
        #         'right_grasp_offset': [0, -0.032, 0.08, 0, 0, 0.0],
        #         'left_gripper_open': 0,
        #         'left_gripper_close': 95,
        #         'right_gripper_open': 0,
        #         'right_gripper_close': 100,
        #         'is_cylinder': True
        #     },
        #     'gloves': {
        #         'left_grasp_offset': [0, 0.0, 0.2, -81.351, 58.869, 87.205],
        #         'right_grasp_offset': [0, 0, 0.07, 0, 0, 0.0],
        #         'left_gripper_open': 0,
        #         'left_gripper_close': 100,
        #         'right_gripper_open': 0,
        #         'right_gripper_close': 100,
        #         'is_cylinder': True
        #     },
        #     'usb': {
        #         'left_grasp_offset':[-0.02, 0, 0.08, 0, 80, 180],
        #         'right_grasp_offset': [0, 0, 0.07, 0, 0, 0.0],
        #         'left_gripper_open': 0,
        #         'left_gripper_close': 100,
        #         'right_gripper_open': 0,
        #         'right_gripper_close': 100,
        #         'is_cylinder': False
        #     },
        #     'glue': {
        #         'left_grasp_offset': [0.0, 0.0, 0.3, -81.351, 58.869, 87.205],
        #         'right_grasp_offset': [0, 0, 0.07, 0, 0, 0.0],
        #         'left_gripper_open': 0,
        #         'left_gripper_close': 95,
        #         'right_gripper_open': 0,
        #         'right_gripper_close': 70,
        #         'is_cylinder': False
        #     },
        # }

        self.actions = {

            '回原点': [
                ('parallel', '回原点', [
                    [('arm', {'waypoints': ['home_left'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['home_right'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],
            # '去初始点': [
            #     ('parallel', '去初始点', [
            #         # [('arm', {'waypoints': ['左臂-水平抓取-初始'], 'vel': 0.5, 'object_name': self.object_name})],
            #         [('arm', {'waypoints': ['左臂-垂直抓取-初始'], 'vel': 0.5, 'object_name': self.object_name})],
            #         [('arm', {'waypoints': ['右臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name})]
            #     ])
            # ],
            '左臂去水平初始点': [
                ('parallel', '左臂去水平初始点', [
                    [('arm', {'waypoints': ['左臂-水平抓取-初始'], 'vel': 0.5, 'object_name': self.object_name})],
                ])
            ],
            '去折叠点': [
                ('parallel', '去水平折叠点', [
                    [('arm', {'waypoints': ['左臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            '去柜台水平放置点': [
                ('parallel', '去柜台水平放置点', [
                    [('arm', {'waypoints': ['左臂-柜台水平放置点'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右臂-柜台水平放置点'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            '去餐桌水平放置点': [
                ('parallel', '去餐桌水平放置点', [
                    [('arm', {'waypoints': ['左臂-餐桌水平放置点'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右臂-餐桌水平放置点'], 'vel': 1.0, 'object_name': self.object_name})]
                ])
            ],
            '开两个爪子':[
                ('setup', '开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('setup', '开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                    # ('delay', {'seconds': 2}),
                ])
            ],
            '关两个爪子':[
                ('setup', '关爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_close', 'value': '80'}),
                    ('gripper', {'action': 'close'}),
                ]),
                ('setup', '关爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_close', 'value': '80'}),
                    ('gripper', {'action': 'close'}),
                ])
            ],
            "拿水": [
                ('sequence', '拿水', [
                    ('arm', {'waypoints': ['右臂-拿水'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
            ],

            "左手垂直放置工作台": [
                ('parallel', '左臂去垂直折叠点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '左臂去初始点', [
                    ('arm', {'waypoints': ['左臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('sequence', '左臂去就绪点', [
                    ('arm', {'waypoints': ['左臂-放桌上-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '左臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('sequence', '左臂回初始点', [
                    ('arm', {'waypoints': ['左臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('parallel', '左臂去垂直折叠点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            "左手垂直放置餐桌": [
                ('parallel', '左臂去垂直折叠点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '左臂去初始点', [
                    ('arm', {'waypoints': ['左臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('sequence', '左臂去就绪点', [
                    ('arm', {'waypoints': ['左臂-放桌上-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '左臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('sequence', '左臂回初始点', [
                    ('arm', {'waypoints': ['左臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('parallel', '左臂去垂直折叠点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            "左手水平放置柜台": [
                ('parallel', '左臂去水平折叠点', [
                    [('arm', {'waypoints': ['左臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '左臂去柜台水平放置点', [
                    ('arm', {'waypoints': ['左臂-柜台水平放置点'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '左臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '左臂去水平折叠点', [
                    [('arm', {'waypoints': ['左臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            "左手给人工作台": [
                ('parallel', '左臂去水平折叠点', [
                    [('arm', {'waypoints': ['左臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '左臂去餐桌水平放置点', [
                    ('arm', {'waypoints': ['左臂-餐桌水平放置点'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '左臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '左臂去水平折叠点', [
                    [('arm', {'waypoints': ['左臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            "左手给人餐桌": [
                ('parallel', '左臂去水平折叠点', [
                    [('arm', {'waypoints': ['左臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '左臂去餐桌水平放置点', [
                    ('arm', {'waypoints': ['左臂-餐桌水平放置点'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '左臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '左臂去水平折叠点', [
                    [('arm', {'waypoints': ['左臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            "左手给人柜台": [
                ('parallel', '左臂去水平折叠点', [
                    [('arm', {'waypoints': ['左臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '左臂去柜台水平放置点', [
                    ('arm', {'waypoints': ['左臂-柜台水平放置点'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '左臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '左臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '左臂去水平折叠点', [
                    [('arm', {'waypoints': ['左臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            
            "右手垂直放置工作台": [
                ('parallel', '右臂去垂直折叠点', [
                    [('arm', {'waypoints': ['右臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '右臂去初始点', [
                    ('arm', {'waypoints': ['右臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('sequence', '右臂去就绪点', [
                    ('arm', {'waypoints': ['右臂-放入框内-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '右臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('sequence', '右臂回初始点', [
                    ('arm', {'waypoints': ['右臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('parallel', '右臂去垂直折叠点', [
                    [('arm', {'waypoints': ['右臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            "右手垂直放置餐桌": [
                ('parallel', '右臂去垂直折叠点', [
                    [('arm', {'waypoints': ['右臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '右臂去初始点', [
                    ('arm', {'waypoints': ['右臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('sequence', '右臂去就绪点', [
                    ('arm', {'waypoints': ['右臂-放入框内-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '右臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('sequence', '右臂回初始点', [
                    ('arm', {'waypoints': ['右臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('parallel', '右臂去垂直折叠点', [
                    [('arm', {'waypoints': ['右臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            "右手水平放置柜台": [
                ('parallel', '右臂去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '右臂去柜台水平放置点', [
                    ('arm', {'waypoints': ['右臂-柜台水平放置点'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '右臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '右臂去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            "右手水平放置餐桌": [
                ('parallel', '右臂去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '右臂去餐桌水平放置点', [
                    ('arm', {'waypoints': ['右臂-餐桌水平放置点'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '右臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '右臂去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            "右手水平放置工作台": [
                ('parallel', '右臂去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '右臂去餐桌水平放置点', [
                    ('arm', {'waypoints': ['右臂-餐桌水平放置点'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '右臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '右臂去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            "右手给人工作台": [
                ('parallel', '右臂去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '右臂去餐桌水平放置点', [
                    ('arm', {'waypoints': ['右臂-餐桌水平放置点'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '右臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '右臂去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            "右手给人餐桌": [
                ('parallel', '右臂去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '右臂去餐桌水平放置点', [
                    ('arm', {'waypoints': ['右臂-餐桌水平放置点'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '右臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '右臂去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            "右手给人柜台": [
                ('parallel', '右臂去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '右臂去柜台水平放置点', [
                    ('arm', {'waypoints': ['右臂-柜台水平放置点'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '右臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '右臂去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
            ],

            '左手垂直抓取U盘': [
                ('parallel', '去垂直折叠点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '识别', [
                    ('object_detect', {'service_name': '/papjia_vision/service_object_detect', 'max_num': 8, 'min_score': 0.7, 'keys': {'detect_objects': 'detect_objects'}}),
                ]),
                ('setup', '导入世界模型', [
                    ('update_world_model', {'vision_data': 'detect_objects', 'world_model': 'world_model_data'}),
                ]),  
                ('setup', '更新位姿', [
                    ('update_target', {'world_model': 'world_model_data', # 世界模型数据的黑板键名
                                       'object_name': self.object_name,  # 路径点存储对象名称
                                       'prefer': 'usb',# 'usb', 抓取倾向
                                       'functionality': self.object_functionality,
                                       'waypoint_name': '垂直抓取-就绪', # 需要更新的路径点名称
                                       'frame_id': 'arm_base'}),

                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-就绪', 
                                              'object_name': self.object_name}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-初始', 
                                              'object_name': self.object_name}),
                ]),
                ('parallel', '左臂去垂直折叠点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('setup', '左臂开爪子', [
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '去垂直初始点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('setup', '调整预备点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去预备点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '调整就绪点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去就绪点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '左臂关爪子', [
                    ('gripper', {'action': 'close'}),
                ]),
                ('parallel', '去垂直初始点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('setup', '调整撤退点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                # ('sequence', '去撤退点', [
                #     ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                # ]),
                ('setup', '重置就绪点', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('parallel', '去垂直折叠点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})],
                  ]),
            ],
            '左手垂直抓取手套': [
                ('parallel', '去垂直折叠点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '识别', [
                    ('object_detect', {'service_name': '/papjia_vision/service_object_detect', 'max_num': 8, 'min_score': 0.7, 'keys': {'detect_objects': 'detect_objects'}}),
                ]),
                ('setup', '导入世界模型', [
                    ('update_world_model', {'vision_data': 'detect_objects', 'world_model': 'world_model_data'}),
                ]), 
                 
                ('setup', '更新位姿', [
                    ('update_target', {'world_model': 'world_model_data', # 世界模型数据的黑板键名
                                       'object_name': self.object_name,  # 路径点存储对象名称
                                       'prefer': 'gloves',# 'gloves', 抓取倾向
                                       'functionality': self.object_functionality,
                                       'waypoint_name': '垂直抓取-就绪', # 需要更新的路径点名称
                                       'frame_id': 'arm_base'}),

                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-就绪', 
                                              'object_name': self.object_name}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-初始', 
                                              'object_name': self.object_name}),
                ]),
                ('setup', '左臂开爪子', [
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '去垂直初始点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('setup', '调整预备点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去预备点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '调整就绪点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去就绪点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '左臂关爪子', [
                    ('gripper', {'action': 'close'}),
                ]),
                ('parallel', '去垂直初始点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('setup', '调整撤退点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('setup', '重置就绪点', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('parallel', '去垂直折叠点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})],
                  ]),
            ],
            '右手垂直抓取胶水': [
                ('parallel', '去垂直折叠点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '识别', [
                    ('object_detect', {'service_name': '/papjia_vision/service_object_detect', 'max_num': 8, 'min_score': 0.7, 'keys': {'detect_objects': 'detect_objects'}}),
                ]),
                ('setup', '导入世界模型', [
                    ('update_world_model', {'vision_data': 'detect_objects', 'world_model': 'world_model_data'}),
                ]),  
                ('setup', '更新位姿', [
                    ('update_target', {'world_model': 'world_model_data', # 世界模型数据的黑板键名
                                       'object_name': self.object_name,  # 路径点存储对象名称
                                       'prefer': 'glue',# 'glue', 抓取倾向
                                       'functionality': self.object_functionality,
                                       'waypoint_name': '垂直抓取-就绪', # 需要更新的路径点名称
                                       'frame_id': 'arm_base'}),

                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-就绪', 
                                              'object_name': self.object_name}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-初始', 
                                              'object_name': self.object_name}),
                ]),
                ('setup', '右臂开爪子', [
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '去垂直初始点', [
                    [('arm', {'waypoints': ['右臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('setup', '调整预备点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去预备点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '调整就绪点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去就绪点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '右臂关爪子', [
                    ('gripper', {'action': 'close'}),
                    ('delay', {'seconds': 2}),
                ]),
                ('setup', '调整撤退点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('setup', '重置就绪点', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去初始点', [
                    ('arm', {'waypoints': ['垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('parallel', '去垂直折叠点', [
                    [('arm', {'waypoints': ['右臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})],
                ]),
            ],

            '右手垂直抓取胶带': [
                ('parallel', '去垂直折叠点', [
                    [('arm', {'waypoints': ['左臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})],
                    [('arm', {'waypoints': ['右臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('sequence', '识别', [
                    ('object_detect', {'service_name': '/papjia_vision/service_object_detect', 'max_num': 8, 'min_score': 0.7, 'keys': {'detect_objects': 'detect_objects'}}),
                ]),
                ('setup', '导入世界模型', [
                    ('update_world_model', {'vision_data': 'detect_objects', 'world_model': 'world_model_data'}),
                ]),  
                ('setup', '更新位姿', [
                    ('update_target', {'world_model': 'world_model_data', # 世界模型数据的黑板键名
                                       'object_name': self.object_name,  # 路径点存储对象名称
                                       'prefer': 'tape',# 'tape', 抓取倾向
                                       'functionality': self.object_functionality,
                                       'waypoint_name': '垂直抓取-就绪', # 需要更新的路径点名称
                                       'frame_id': 'arm_base'}),

                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-就绪', 
                                              'object_name': self.object_name}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '垂直抓取-初始', 
                                              'object_name': self.object_name}),
                ]),
                ('setup', '右臂开爪子', [
                    ('gripper', {'action': 'open'}),
                ]),
                ('parallel', '去垂直初始点', [
                    [('arm', {'waypoints': ['右臂-垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('setup', '调整预备点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去预备点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '调整就绪点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.pre_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去就绪点', [
                    ('arm', {'waypoints': ['垂直抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '右臂关爪子', [
                    ('gripper', {'action': 'close'}),
                    ('delay', {'seconds': 2}),
                ]),
                ('setup', '调整撤退点参数', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('setup', '重置就绪点', [
                    ('set_waypoint_config', {
                        'waypoint_names': ['垂直抓取-就绪'],
                        'offset': {'z': -self.after_height},
                        'object_name': self.object_name
                    })
                ]),
                ('sequence', '去初始点', [
                    ('arm', {'waypoints': ['垂直抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('parallel', '去垂直折叠点', [
                    [('arm', {'waypoints': ['右臂-垂直抓取-折叠点'], 'vel': 1.0, 'object_name': self.object_name})],
                ]),
            ],

            '右手水平抓取物体': [
                ('parallel', '去水平折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})]
                ]),
                ('setup', '右臂开爪子', [
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_open', 'value': '0'}),
                    ('gripper', {'action': 'open'}),
                ]),
                ('sequence', '拿水', [
                    ('arm', {'waypoints': ['右臂-拿水'], 'vel': 1.0, 'object_name': self.object_name}),
                    
                ]),
                
                ('setup', '右臂关爪子', [
                    ('delay', {'seconds': 3}),
                    ('set_blackboard_value', {'key': 'arm_prefix', 'value': '右臂-'}),
                    ('set_blackboard_value', {'key': 'gripper_close', 'value': '80'}),
                    ('gripper', {'action': 'close'}),
                ]),
                ('parallel', '去垂直折叠点', [
                    [('arm', {'waypoints': ['右臂-折叠点'], 'vel': 1.0, 'object_name': self.object_name})],
                ]),
            ],
            '水平抓取任意物体': [
                ('setup', '更新位姿', [
                    ('update_target', {'world_model': 'world_model_data', # 世界模型数据的黑板键名
                                       'object_name': self.object_name,  # 路径点存储对象名称
                                       'prefer': 'any',# 抓取倾向
                                       'functionality': self.object_functionality,
                                       'waypoint_name': '水平抓取-就绪', # 需要更新的路径点名称
                                       'frame_id': 'arm_base'}),

                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '水平抓取-就绪', 
                                              'object_name': self.object_name}),
                    ('waypoint_from_prefix', {'arm_prefix': 'arm_prefix', 
                                              'waypoint_name': '水平抓取-初始', 
                                              'object_name': self.object_name}),
                ]),   
                ('setup', '开爪子', [
                    ('gripper', {'action': 'open'}),
                ]),
                
                ('sequence', '去就绪点', [
                    ('arm', {'waypoints': ['水平抓取-就绪'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
                ('setup', '关爪子', [
                    ('gripper', {'action': 'close'}),
                ]),
                ('sequence', '左臂去水平初始点', [
                    ('arm', {'waypoints': ['水平抓取-初始'], 'vel': 1.0, 'object_name': self.object_name}),
                ]),
            ],
        }