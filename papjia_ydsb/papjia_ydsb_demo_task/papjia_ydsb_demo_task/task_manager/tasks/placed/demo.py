import rclpy
import sys
import json
import os

from papjia_ydsb_demo_task.task_manager.tasks.placed.placed_operation import PlacedOperation
from papjia_ydsb_demo_task.task_manager.tasks.navigation.navigation_operation import NavigationOperation
from papjia_ydsb_demo_task.task_manager.core.task_builder import TaskBuilder

rclpy.init()
step_run = False

waypoint_file_path = "/workspace/src/papjia_ydsb/papjia_ydsb_config/config/placed_waypoint_configs.json"
with open(waypoint_file_path, "r") as f:
    all_waypoint_configs = json.load(f)

placed_operation = PlacedOperation()
task_builder = TaskBuilder(debug=False)

task_builder.register_object_waypoint_configs(placed_operation.object_name, all_waypoint_configs[placed_operation.object_name])
task_builder.register_object_action_configs(placed_operation.object_name, placed_operation.actions)

navigation_operation = NavigationOperation()
task_builder.register_object_action_configs(navigation_operation.object_name, navigation_operation.actions)


task_sequence = [

        # (placed_operation.object_name, '去折叠点'),
        # (navigation_operation.object_name, '去给框'),

        # (navigation_operation.object_name, '去餐桌'),
        # (navigation_operation.object_name, '对码'),

        # (placed_operation.object_name, '左手垂直放置工作台'),
        # (placed_operation.object_name, '左手垂直放置餐桌'),
        # (placed_operation.object_name, '左手水平放置柜台'),
        # (placed_operation.object_name, '左手给人工作台'),
        # (placed_operation.object_name, '左手给人餐桌'),
        # (placed_operation.object_name, '左手给人柜台'),

        # (placed_operation.object_name, '右手垂直放置工作台'),
        # (placed_operation.object_name, '右手垂直放置餐桌'),
        # (placed_operation.object_name, '右手水平放置柜台'),
        # (placed_operation.object_name, '右手水平放置餐桌'),
        # (placed_operation.object_name, '右手水平放置工作台'),
        # (placed_operation.object_name, '右手给人工作台'),
        # (placed_operation.object_name, '右手给人餐桌'),
        # (placed_operation.object_name, '右手给人柜台'),

        # (placed_operation.object_name, '左手垂直抓取手套'),
        # (placed_operation.object_name, '左手垂直放置工作台'),
        # (placed_operation.object_name, '右手垂直抓取胶带'),
        # (placed_operation.object_name, '右手垂直放置工作台'),

        # (placed_operation.object_name, '右手水平抓取物体'),
        # (navigation_operation.object_name, '去工作台'),
        # (navigation_operation.object_name, '对码'),
        # (placed_operation.object_name, '去餐桌水平放置点'),
        # (placed_operation.object_name, '开两个爪子'),
        # (placed_operation.object_name, '去折叠点'),
        # (navigation_operation.object_name, '去工作台'),

        # (navigation_operation.object_name, '对码'),
        # (placed_operation.object_name, '关两个爪子'),


        # (navigation_operation.object_name, '对码'),
        # (placed_operation.object_name, '去餐桌水平放置点'),

        # (navigation_operation.object_name, '去餐桌'), 
        # (navigation_operation.object_name, '去工作台'),
        # (placed_operation.object_name, '拿水'),
        # (placed_operation.object_name, '左臂去水平初始点'),

        # (placed_operation.object_name, '识别'),
        # (placed_operation.object_name, '左臂去水平初始点'),
        
        # # (placed_operation.object_name, '水平抓取U盘'),
        # (placed_operation.object_name, '水平抓取任意物体'),
        # (placed_operation.object_name, '去折叠点'),


        # (placed_operation.object_name, '给水'),
        # (placed_operation.object_name, '去折叠点'),

        # (placed_operation.object_name, '识别'),
        # (placed_operation.object_name, '去初始点'),
        # (placed_operation.object_name, '垂直抓取任意物体'),
        # (placed_operation.object_name, '放框'),

        # (placed_operation.object_name, '识别'),
        # (placed_operation.object_name, '去初始点'),
        # (placed_operation.object_name, '去吧台水平放置点'),
        # (placed_operation.object_name, '放框'),

    ]

task_builder.execute_task_sequence(task_sequence, step_run)
rclpy.shutdown()
