import rclpy
import sys
import json
import os
from datetime import datetime
from enum import Enum

from papjia_ydsb_demo_task.task_manager.core.task_builder import TaskBuilder


class TaskStatus(Enum):
    NONE = "none"
    INITIALIZED = "initialized"   # 已初始化
    CONFIGURED = "configured"     # 已配置
    RUNNING = "running"           # 运行中
    PAUSING = "pausing"           # 暂停中
    PAUSED = "paused"             # 已暂停
    STOPPING = "stopping"         # 停止中
    STOPPED = "stopped"           # 已停止
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"             # 已失败

class InvalidStateError(Exception):
    """非法状态转换异常"""
    pass

class Task:
    def __init__(self, task_name, executor, db_client, debug=False, params=None):
        self.task_name = task_name
        self.params = params or {}
        self.status = TaskStatus.INITIALIZED
        self.created_at = datetime.now()
        self.started_at = None
        self.finished_at = None
        self.paused_at = None
        self.resumed_at = None
        self.result = None
        self.error = None
        
        self.task_builder = TaskBuilder(executor, db_client, debug)
        
        self.current_action_description = {
            'action_no': 0,
            'total_actions': 0,
            'action_name': None,
        }
        self.task_progress = {
            'step': 0,
            'total_steps': 0,
            'step_name': None,
            'current_action': self.current_action_description
        }

    def to_dict(self):
        """序列化为字典"""
        return {
            "task_name": self.task_name,
            "params": self.params,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "finished_at": self.finished_at.isoformat() if self.finished_at else None,
            "paused_at": self.paused_at.isoformat() if self.paused_at else None,
            "resumed_at": self.resumed_at.isoformat() if self.resumed_at else None,
            "progress": self.task_progress,
            "result": self.result,
            "error": self.error
        }

    def configure(self, params):
        """完成参数配置"""
        if self.status != TaskStatus.INITIALIZED:
            raise InvalidStateError("只能在初始化状态进行配置")
        self.params.update(params)
        self.status = TaskStatus.CONFIGURED

    def start(self):
        """启动任务运行"""
        if self.status != TaskStatus.CONFIGURED:
            raise InvalidStateError(f"无法从 {self.status} 状态启动")
        
        self.status = TaskStatus.RUNNING
        if not self.started_at:
            self.started_at = datetime.now()

    def pause(self):
        """暂停任务"""
        if self.status != TaskStatus.RUNNING:
            raise InvalidStateError("只有运行中的任务可以暂停")
        self.status = TaskStatus.PAUSING

    def confirm_pause(self):
        """确认任务已暂停"""
        if self.status != TaskStatus.PAUSING:
            raise InvalidStateError("未在暂停中的任务无法确认")
        self.status = TaskStatus.PAUSED
        self.paused_at = datetime.now()
    
    def resume(self):
        """恢复暂停的任务"""
        if self.status != TaskStatus.PAUSED:
            raise InvalidStateError("只有暂停的任务可以恢复")
        self.status = TaskStatus.RUNNING
        self.resumed_at = datetime.now()

    def stop(self):
        """请求停止任务"""
        allowed = [TaskStatus.RUNNING, TaskStatus.PAUSED]
        if self.status not in allowed:
            raise InvalidStateError(f"无法从 {self.status} 状态停止")
        self.status = TaskStatus.STOPPING

    def confirm_stop(self):
        """确认任务已停止"""
        if self.status != TaskStatus.STOPPING:
            raise InvalidStateError("未在停止中的任务无法确认")
        self.status = TaskStatus.STOPPED
        self.finished_at = datetime.now()

    def confirm_complete(self, result=None):
        """标记任务完成"""
        if self.status != TaskStatus.RUNNING:
            raise InvalidStateError("只有运行中的任务可以完成")
        self.status = TaskStatus.COMPLETED
        self.finished_at = datetime.now()
        self.result = result

    def confirm_fail(self, error=None):
        """标记任务失败"""
        self.status = TaskStatus.FAILED
        self.finished_at = datetime.now()
        self.error = str(error) if error else None
    
    def register(self, object_name, object_waypoint_configs, object_action_configs):
        """
        注册对象的点位和动作配置
        Args:
            object_name: 对象名称
            object_waypoint_configs: 对象的点位配置
            object_action_configs: 对象的动作配置
        """
        self.task_builder.register_object_waypoint_configs(object_name, object_waypoint_configs)
        self.task_builder.register_object_action_configs(object_name, object_action_configs)

    def run_step(self, action_object, action_name):
        for i, action in enumerate(self.task_builder.action_configs[action_object][action_name]):
            self.current_action_description['action_no'] = i
            self.current_action_description['action_name'] = action[1]
            print(f"[Step {self.task_progress['step']}/{self.task_progress['total_steps']}]: "
                    f"Action {self.current_action_description['action_no']}/{self.current_action_description['total_actions']}: "
                    f"{self.current_action_description['action_name']}")
            
            root, tree_name = self.task_builder.build_step_action_tree(action_name, action)
            if root is not None:
                if self.task_builder.execute_tree(root, tree_name) is False:
                    print(f"执行失败: {action_name}")
                    return False
            
            print("[Blackboard] ", self.task_builder.blackboard.get_all())
        return True
    
    def run(self, task_sequence):
        """
        运行任务
        Args:
            task_sequence: 任务序列
        Returns:
            bool: 是否成功
        """
        self.current_action_description['total_actions'] = len(task_sequence)
        
        self.task_progress['step'] = 0
        self.task_progress['total_steps'] = len(task_sequence)
        for step_no, (action_object, action_name) in enumerate(task_sequence):

            self.task_progress['step'] = step_no
            self.task_progress['step_name'] = action_name
            self.current_action_description['total_actions'] = len(self.task_builder.action_configs[action_object][action_name])

            print(self.task_progress)

            if not self.run_step(action_object, action_name):
                return False
        return True