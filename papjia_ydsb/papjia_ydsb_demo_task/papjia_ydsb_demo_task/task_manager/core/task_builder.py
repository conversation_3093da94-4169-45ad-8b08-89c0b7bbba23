import json
import sys
import asyncio
import rclpy
from rclpy.node import Node
import threading
from rclpy.executors import MultiThreadedExecutor

from papjia_skill.btree import BehaviorTree, BehaviorRoot
from papjia_skill.executor import PapjiaSkillAsyncExecutor

from papjia_ydsb_demo_task.task_manager.actions import *
from papjia_ydsb_demo_task.task_manager.actions.action_node import ActionNodeFactory
from papjia_ydsb_demo_task.task_manager.core.blackboard import Blackboard
from papjia_ydsb_demo_task.task_manager.core.element_parsers import parser_registry

# 导入内置解析器以注册它们
from papjia_ydsb_demo_task.task_manager.core.builtin_parsers import *


class TaskBuilder(object):
    def __init__(self, debug=False):
        self.debug = debug
        self.action_configs = {}
        self.waypoint_configs = {}
        self.blackboard = Blackboard()  # 使用Blackboard类
        self.blackborad_bt_keys = {}
        self.node_factory = None  # 延迟初始化
        self.task_node = Node('task_node')
        self.bt_executor = PapjiaSkillAsyncExecutor()
        
        # 启动executor线程
        executor = MultiThreadedExecutor()
        def spin_executor(executor):
            """在单独线程中运行executor"""
            while rclpy.ok():
                try:
                    executor.spin_once(timeout_sec=0.01)
                except Exception as e:
                    print(f"Executor spin error: {e}")
                    break
        executor.add_node(self.bt_executor)
        executor.add_node(self.task_node)
        executor_thread = threading.Thread(target=spin_executor, args=(executor,))
        executor_thread.start()
    
    def register_object_waypoint_configs(self, object_name, waypoint_configs):
        """
        注册对象waypoint配置
        
        Args:
            object_name: 对象名称
            waypoint_configs: 对象waypoint配置，格式为 {waypoint_name: config}
        """
        if object_name in self.waypoint_configs.keys():
            raise KeyError(f"对象: {object_name} 已注册路径点配置")
        self.waypoint_configs[object_name] = waypoint_configs
        # 更新工厂
        self.node_factory = ActionNodeFactory(self.waypoint_configs, self.blackboard)

    def register_object_action_configs(self, action_object_name, configs):
        """
        注册动作配置
        
        Args:
            action_object_name: 动作对象名称
            configs: 动作配置
        """
        if action_object_name in self.action_configs.keys():
            raise KeyError(f"对象: {action_object_name} 已注册动作配置")
        self.action_configs[action_object_name] = configs
        # 确保工厂已初始化
        if self.node_factory is None:
            self.node_factory = ActionNodeFactory(self.waypoint_configs, self.blackboard)
    
    def _parse_element(self, element):
        """解析任务元素并构建行为树节点
        
        Args:
            element: 任务元素，可以是以下格式：
                - ('sequence', name, [actions])  # 序列结构
                - ('parallel', name, [[actions]])  # 并行结构
                - ('setup', name, [actions])  # 设置结构
                - (action_type, action_params)  # 原子动作
                
        Returns:
            BehaviorNode: 构建的行为树节点
        """
        # 使用注册表查找合适的解析器
        parser = parser_registry.get_parser(element)
        
        if parser is not None:
            if hasattr(parser, 'parse'):  # 类解析器
                return parser.parse(element, self)
            else:  # 函数解析器
                result = parser(element, self)
                if result is not None:
                    return result
        
        # 如果没有找到合适的解析器，抛出错误
        raise ValueError(f"无效的任务元素格式: {element}")

    def build_step_action_tree(self, action_name, action):
        """
        构建任务树
        
        Args:
            action_name: 动作名称
            action: 动作
        
        Returns:
            root: 任务树根节点
        """
        self.blackborad_bt_keys = {}
        node = self._parse_element(action)
        if node is None:
            return None, None
        
        tree_name = action_name + "-" + action[1]
        tree = BehaviorTree(tree_name)
        tree.add_child(node)
        root = BehaviorRoot()
        root.add_child(tree)
        if self.debug:
            tree.add_child(self.node_factory.create_node('pause', {}))
        return root, tree_name
    
    def execute_tree(self, root, tree_name):
        """
        执行任务树
        
        Args:
            task_name: 任务名称
            root: 任务树根节点
            keys: 任务树执行时需要传递的键值对

        Returns:
            execute_result: 任务树执行结果
        """
        # 使用asyncio.run()来运行异步执行器
        print(self.blackborad_bt_keys.keys())
        execute_result = asyncio.run(self.bt_executor.execute_tree(root.to_str(), tree_name, keys=self.blackborad_bt_keys.keys()))
        if not execute_result.success:
            return False
        
        # 获取执行结果，并保存到黑板
        if execute_result.result:
            result = json.loads(execute_result.result)
            for key, value in self.blackborad_bt_keys.items():
                if key in result:
                    self.blackboard.set(value, result[key])
            # 已经使用完了，清空黑板键值对
            self.blackborad_bt_keys = {}
        return True
    
    def list_available_parsers(self):
        """列出所有可用的解析器"""
        return parser_registry.list_parsers()
    
    def register_custom_parser(self, name, parser):
        """注册自定义解析器
        
        Args:
            name: 解析器名称
            parser: 解析器实例或函数
        """
        parser_registry.register(name, parser)
    
    def execute_task_sequence(self, task_sequence, step_run=True):
        """
        执行任务序列
        
        Args:
            task_sequence: 任务序列列表，每个元素为(object_name, action_name)元组
            step_run: 是否启用单步运行模式，默认为True
            
        Returns:
            None
        """
        for action_object, action_name in task_sequence:
            for i, action in enumerate(self.action_configs[action_object][action_name]):
                root, tree_name = self.build_step_action_tree(action_name, action)
                print(f"{action_name}: {i} - {action}")
                print("blackboard before execute", self.blackboard.get_all())
                if root is not None:
                    success = self.execute_tree(root, tree_name)
                    if not success:
                        print(f"执行{action_name}失败")
                        rclpy.shutdown()
                        sys.exit(1)
                
                print("blackboard after execute", self.blackboard.get_all())
                if step_run:
                    input("任务序列单步运行中，回车继续...")
