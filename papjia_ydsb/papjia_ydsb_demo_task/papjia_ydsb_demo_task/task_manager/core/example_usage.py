"""
TaskBuilder注册机制使用示例

这个文件展示了如何使用新的注册机制来扩展TaskBuilder的功能
"""

from papjia_skill.btree import Sequence
from papjia_skill.atom.papjia_delay_async import PapjiaDelayAsync

from .element_parsers import ElementParser, register_parser_function, register_parser_class
from .task_builder import TaskBuilder


# 示例1：使用类解析器
@register_parser_class("custom_sequence")
class CustomSequenceParser(ElementParser):
    """自定义序列解析器"""
    
    def can_parse(self, element) -> bool:
        return isinstance(element, tuple) and element[0] == 'custom_sequence'
    
    def parse(self, element, task_builder):
        _, name, actions = element
        seq_node = Sequence(f"custom_{name}")
        
        # 添加自定义逻辑
        for action in actions:
            child_node = task_builder._parse_element(action)
            if child_node:
                seq_node.add_child(child_node)
        
        # 在序列末尾添加延迟
        seq_node.add_child(PapjiaDelayAsync(delay_duration=1.0))
        return seq_node


# 示例2：使用函数解析器
@register_parser_function("simple_delay")
def simple_delay_parser(element, task_builder):
    """简单延迟解析器"""
    if isinstance(element, tuple) and element[0] == 'simple_delay':
        duration = element[1] if len(element) > 1 else 1.0
        return PapjiaDelayAsync(delay_duration=float(duration))
    return None


# 示例3：条件解析器
@register_parser_function("conditional_action")
def conditional_action_parser(element, task_builder):
    """条件动作解析器"""
    if isinstance(element, tuple) and element[0] == 'conditional':
        condition = element[1]
        true_action = element[2]
        false_action = element[3] if len(element) > 3 else None
        
        # 这里可以实现条件逻辑
        # 简化示例：总是执行true_action
        return task_builder._parse_element(true_action)
    return None

def demonstrate_usage():
    """演示如何使用新的注册机制"""
    
    # 创建TaskBuilder实例
    task_builder = TaskBuilder(executor=None, db_client=None)
    
    # 查看所有可用的解析器
    print("可用的解析器:", task_builder.list_available_parsers())
    
    # 使用新的元素类型
    custom_sequence = ('custom_sequence', 'test', [
        ('simple_delay', 2.0),
        ('arm', {'waypoints': ['point1', 'point2']})
    ])
    
    # 使用条件动作
    conditional_action = ('conditional', True, 
                         ('arm', {'waypoints': ['point1']}),
                         ('simple_delay', 1.0))
    
    # 使用循环动作
    loop_action = ('loop', 3, ('simple_delay', 0.5))
    
    # 这些新的元素类型现在可以被TaskBuilder解析
    print("新的元素类型已注册并可以使用")


if __name__ == "__main__":
    demonstrate_usage() 