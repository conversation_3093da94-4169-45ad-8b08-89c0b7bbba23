# 设置命令策略系统使用文档

## 概述

设置命令策略系统采用策略模式 + 注册表的设计，用于处理任务构建过程中的各种设置命令。系统提供了统一的接口和灵活的扩展机制。

## 核心组件

### 1. SetupCommandStrategy (抽象基类)
所有设置命令处理器都必须继承此基类，并实现以下方法：
- `can_handle(command: str) -> bool`: 判断是否能处理指定命令
- `execute(params: dict, task_builder) -> None`: 执行具体的设置命令

### 2. SetupCommandRegistry (注册表)
管理所有可用的设置命令处理器，提供以下功能：
- 注册新的命令处理器
- 获取指定命令的处理器
- 列出所有支持的命令
- 移除命令处理器

### 3. 装饰器
`@register_setup_command(command: str)` 装饰器用于自动注册命令处理器。

## 内置命令

### set_waypoint_config
设置路径点配置的偏移量。

**参数格式：**
```python
{
    'waypoint_names': ['waypoint1', 'waypoint2'],  # 路径点名称列表
    'offset': {'x': 0.1, 'y': -0.05, 'z': 0.02},  # 偏移量
    'object_name': 'glass_tube'  # 对象名称
}
```

**使用示例：**
```python
# 在任务配置中使用
('setup', '调整路径点', [
    ('set_waypoint_config', {
        'waypoint_names': ['左臂-玻璃试管-抓取-准备'],
        'offset': {'x': 0.1, 'y': 0, 'z': 0.02},
        'object_name': 'glass_tube'
    })
])
```

### set_blackboard_value (扩展示例)
设置黑板值。

**参数格式：**
```python
{
    'key': 'target_position',  # 黑板键名
    'value': [0.1, 0.2, 0.3]  # 要设置的值
}
```

### set_action_config (扩展示例)
设置动作配置。

**参数格式：**
```python
{
    'action_object': 'glass_tube',  # 动作对象名称
    'action_name': '抓取',          # 动作名称
    'config': {'timeout': 30}       # 配置字典
}
```

## 扩展新的设置命令

### 方法1：使用装饰器（推荐）

```python
from papjia_ydsb_demo_task.task_manager.core.setup_strategies import (
    SetupCommandStrategy, register_setup_command
)

@register_setup_command("set_custom_config")
class SetCustomConfigStrategy(SetupCommandStrategy):
    """自定义配置设置策略"""
    
    def can_handle(self, command: str) -> bool:
        return command == "set_custom_config"
    
    def execute(self, params: dict, task_builder) -> None:
        # 参数验证
        if 'custom_param' not in params:
            raise ValueError("缺少必需参数: custom_param")
        
        # 执行自定义逻辑
        custom_param = params['custom_param']
        # ... 执行具体的设置逻辑
        
        print(f"设置自定义配置: {custom_param}")
```

### 方法2：手动注册

```python
from papjia_ydsb_demo_task.task_manager.core.setup_strategies import (
    SetupCommandStrategy, setup_command_registry
)

class ManualCustomStrategy(SetupCommandStrategy):
    def can_handle(self, command: str) -> bool:
        return command == "manual_custom"
    
    def execute(self, params: dict, task_builder) -> None:
        # 实现逻辑
        pass

# 手动注册
setup_command_registry.register("manual_custom", ManualCustomStrategy())
```

## 错误处理

所有策略类都应该包含适当的错误处理：

```python
def execute(self, params: dict, task_builder) -> None:
    try:
        # 参数验证
        if 'required_param' not in params:
            raise ValueError("缺少必需参数: required_param")
        
        # 执行逻辑
        # ...
        
    except Exception as e:
        logger.error(f"设置命令执行失败: {str(e)}")
        raise
```

## 最佳实践

1. **参数验证**：始终验证输入参数的完整性和有效性
2. **错误处理**：使用 try-catch 包装执行逻辑，提供有意义的错误信息
3. **日志记录**：记录重要的操作和错误信息
4. **文档注释**：为每个策略类提供清晰的文档说明
5. **类型提示**：使用类型提示提高代码可读性

## 调试和测试

### 查看已注册的命令
```python
from papjia_ydsb_demo_task.task_manager.core.setup_strategies import setup_command_registry

# 列出所有支持的命令
print("支持的设置命令:", setup_command_registry.list_commands())

# 检查特定命令是否存在
if setup_command_registry.has_strategy("set_waypoint_config"):
    print("set_waypoint_config 命令已注册")
```

### 测试新策略
```python
# 创建测试参数
test_params = {
    'waypoint_names': ['test_waypoint'],
    'offset': {'x': 0.1},
    'object_name': 'test_object'
}

# 获取策略并测试
strategy = setup_command_registry.get_strategy("set_waypoint_config")
if strategy:
    strategy.execute(test_params, mock_task_builder)
```

## 注意事项

1. 策略类应该是无状态的，避免在策略中保存状态
2. 所有策略都会在模块导入时自动注册
3. 命令名称应该是唯一的，重复注册会覆盖之前的处理器
4. 策略执行失败会抛出异常，需要在上层处理 