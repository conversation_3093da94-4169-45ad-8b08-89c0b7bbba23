import time

class Blackboard:
    """黑板类，用于存储和管理任务执行过程中的中间结果"""
    def __init__(self):
        self._data = {}
        self._history = []  # 用于记录数据变更历史
    
    def set(self, key, value):
        """
        设置黑板值
        
        Args:
            key: 键
            value: 值
        """
        old_value = self._data.get(key)
        self._data[key] = value
        self._history.append({
            'operation': 'set',
            'key': key,
            'old_value': old_value,
            'new_value': value,
            'timestamp': time.time()
        })
    
    def get(self, key, default=None):
        """
        获取黑板值
        
        Args:
            key: 键
            default: 默认值，如果键不存在则返回此值
            
        Returns:
            黑板值
        """
        return self._data.get(key, default)
    
    def clear(self):
        """清空黑板"""
        self._history.append({
            'operation': 'clear',
            'old_data': self._data.copy(),
            'timestamp': time.time()
        })
        self._data.clear()
    
    def update(self, data_dict):
        """
        批量更新黑板值
        
        Args:
            data_dict: 要更新的键值对字典
        """
        old_data = {k: self._data.get(k) for k in data_dict.keys()}
        self._data.update(data_dict)
        self._history.append({
            'operation': 'update',
            'old_data': old_data,
            'new_data': data_dict,
            'timestamp': time.time()
        })
    
    def get_history(self):
        """
        获取数据变更历史
        
        Returns:
            list: 数据变更历史记录
        """
        return self._history
    
    def get_all(self):
        """
        获取所有数据
        
        Returns:
            dict: 所有黑板数据
        """
        return self._data.copy()
    
    def has_key(self, key):
        """
        检查键是否存在
        
        Args:
            key: 键
            
        Returns:
            bool: 键是否存在
        """
        return key in self._data
    
    def remove(self, key):
        """
        删除指定键
        
        Args:
            key: 要删除的键
        """
        if key in self._data:
            old_value = self._data[key]
            del self._data[key]
            self._history.append({
                'operation': 'remove',
                'key': key,
                'old_value': old_value,
                'timestamp': time.time()
            })