from papjia_skill.btree import Sequence, Parallel

from .element_parsers import <PERSON><PERSON><PERSON>ars<PERSON>, register_parser_class
from .setup_strategies import setup_command_registry


@register_parser_class("sequence")
class SequenceParser(ElementParser):
    """序列结构解析器"""
    
    def can_parse(self, element) -> bool:
        return isinstance(element, tuple) and element[0] == 'sequence'
    
    def parse(self, element, task_builder):
        _, name, actions = element
        seq_node = Sequence(name)
        for action in actions:
            # 递归解析子元素
            child_node = task_builder._parse_element(action)
            if child_node:
                seq_node.add_child(child_node)
        return seq_node


@register_parser_class("parallel")
class ParallelParser(ElementParser):
    """并行结构解析器"""
    
    def can_parse(self, element) -> bool:
        return isinstance(element, tuple) and element[0] == 'parallel'
    
    def parse(self, element, task_builder):
        _, name, branches = element
        parallel_node = Parallel(name)
        for i, branch in enumerate(branches):
            seq_node = Sequence(f"{name}_branch{i+1}")
            for action in branch:
                # 递归解析子元素
                child_node = task_builder._parse_element(action)
                if child_node:
                    seq_node.add_child(child_node)
            parallel_node.add_child(seq_node)
        return parallel_node


@register_parser_class("atomic_action")
class AtomicActionParser(ElementParser):
    """原子动作解析器"""
    
    def can_parse(self, element) -> bool:
        return isinstance(element, tuple) and len(element) == 2
    
    def parse(self, element, task_builder):
        action_type, action_params = element
        
        # 检查动作类型是否已注册
        if (not task_builder.node_factory.check_has_action_type(action_type) and 
            action_type not in task_builder.action_configs.keys()):
            raise KeyError(f"未注册的动作类型: {action_type}")
        
        # 获取动作配置
        action_config = task_builder.action_configs.get(action_type, {})
        
        # 检查是否有action参数，如果有说明是嵌套动作
        if 'action' in action_params:
            action_name = action_params['action']
            if action_name not in action_config:
                raise KeyError(f"未定义的动作: {action_type}/{action_name}")
            
            # 递归解析嵌套的动作
            if isinstance(action_config[action_name], list):
                seq_node = Sequence(action_name)
                for action in action_config[action_name]:
                    # 递归解析子元素
                    child_node = task_builder._parse_element(action)
                    if child_node:
                        seq_node.add_child(child_node)
                return seq_node
            else:
                return task_builder._parse_element(action_config[action_name])
        else:
            # 记录需要保存的黑板键值对
            print("!!!!!!!!!", action_params)
            if 'keys' in action_params:
                for key, value in action_params['keys'].items():
                    task_builder.blackborad_bt_keys[key] = value
            
            # 创建节点
            node = task_builder.node_factory.create_node(action_type, action_params)
            return node


@register_parser_class("setup")
class SetupParser(ElementParser):
    """设置结构解析器"""
    
    def can_parse(self, element) -> bool:
        return isinstance(element, tuple) and element[0] == 'setup'
    
    def parse(self, element, task_builder):
        _, name, actions = element
        for action in actions:
            cmd, params = action
            
            # 使用策略模式处理设置命令
            strategy = setup_command_registry.get_strategy(cmd)
            if strategy:
                strategy.execute(params, task_builder)
            else:
                raise ValueError(f"未支持的设置命令: {cmd}")
        
        return None