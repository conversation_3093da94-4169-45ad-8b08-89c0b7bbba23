from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Callable, Union
from functools import wraps


class ElementParser(ABC):
    """元素解析器基类"""
    
    @abstractmethod
    def can_parse(self, element: Any) -> bool:
        """判断是否可以解析该元素"""
        pass
    
    @abstractmethod
    def parse(self, element: Any, task_builder: 'TaskBuilder') -> Any:
        """解析元素并返回行为树节点"""
        pass


class ParserRegistry:
    """解析器注册表"""
    
    def __init__(self):
        self._parsers: Dict[str, Union[ElementParser, Callable]] = {}
        self._parser_names: Dict[str, str] = {}  # 存储解析器名称
    
    def register(self, name: str, parser: Union[ElementParser, Callable]) -> None:
        """注册解析器
        
        Args:
            name: 解析器名称
            parser: 解析器实例或函数
        """
        if name in self._parsers:
            raise KeyError(f"解析器 {name} 已注册")
        
        self._parsers[name] = parser
        self._parser_names[name] = name
    
    def register_class(self, name: str):
        """类装饰器，用于注册解析器类"""
        def decorator(cls):
            if not issubclass(cls, ElementParser):
                raise TypeError(f"类 {cls.__name__} 必须继承自 ElementParser")
            self.register(name, cls())
            return cls
        return decorator
    
    def register_function(self, name: str):
        """函数装饰器，用于注册解析函数"""
        def decorator(func):
            @wraps(func)
            def wrapper(element, task_builder):
                return func(element, task_builder)
            self.register(name, wrapper)
            return func
        return decorator
    
    def get_parser(self, element: Any) -> Optional[Union[ElementParser, Callable]]:
        """根据元素获取合适的解析器
        
        Args:
            element: 要解析的元素
            
        Returns:
            解析器实例或函数，如果没有找到则返回None
        """
        for name, parser in self._parsers.items():
            if isinstance(parser, ElementParser):
                if parser.can_parse(element):
                    return parser
            else:  # 函数解析器
                # 对于函数解析器，我们假设它们能处理所有元素
                # 具体的判断逻辑在函数内部实现
                return parser
        return None
    
    def list_parsers(self) -> list:
        """列出所有注册的解析器名称"""
        return list(self._parser_names.keys())
    
    def remove_parser(self, name: str) -> bool:
        """移除解析器
        
        Args:
            name: 解析器名称
            
        Returns:
            是否成功移除
        """
        if name in self._parsers:
            del self._parsers[name]
            del self._parser_names[name]
            return True
        return False


# 全局解析器注册表实例
parser_registry = ParserRegistry()


def register_parser_function(name: str):
    """便捷的解析器注册装饰器"""
    return parser_registry.register_function(name)


def register_parser_class(name: str):
    """便捷的解析器类注册装饰器"""
    return parser_registry.register_class(name) 