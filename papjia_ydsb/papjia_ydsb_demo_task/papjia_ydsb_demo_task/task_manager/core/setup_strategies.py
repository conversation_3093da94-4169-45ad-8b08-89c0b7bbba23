from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from functools import wraps
import logging

logger = logging.getLogger(__name__)


class SetupCommandStrategy(ABC):
    """设置命令处理策略抽象基类"""
    
    @abstractmethod
    def can_handle(self, command: str) -> bool:
        """判断是否能处理该命令
        
        Args:
            command: 命令名称
            
        Returns:
            bool: 是否能处理该命令
        """
        pass
    
    @abstractmethod
    def execute(self, params: dict, task_builder) -> None:
        """执行设置命令
        
        Args:
            params: 命令参数
            task_builder: 任务构建器实例
        """
        pass


class SetupCommandRegistry:
    """设置命令策略注册表"""
    
    def __init__(self):
        self._strategies: Dict[str, SetupCommandStrategy] = {}
        self._strategy_names: Dict[str, str] = {}
    
    def register(self, command: str, strategy: SetupCommandStrategy) -> None:
        """注册命令处理器
        
        Args:
            command: 命令名称
            strategy: 命令处理策略实例
        """
        if command in self._strategies:
            logger.warning(f"命令 {command} 的处理器已存在，将被覆盖")
        
        self._strategies[command] = strategy
        self._strategy_names[command] = command
        logger.info(f"注册设置命令处理器: {command}")
    
    def get_strategy(self, command: str) -> Optional[SetupCommandStrategy]:
        """获取命令处理器
        
        Args:
            command: 命令名称
            
        Returns:
            命令处理策略实例，如果未找到则返回None
        """
        return self._strategies.get(command)
    
    def list_commands(self) -> List[str]:
        """列出所有支持的命令
        
        Returns:
            支持的命令列表
        """
        return list(self._strategy_names.keys())
    
    def remove_strategy(self, command: str) -> bool:
        """移除命令处理器
        
        Args:
            command: 命令名称
            
        Returns:
            是否成功移除
        """
        if command in self._strategies:
            del self._strategies[command]
            del self._strategy_names[command]
            logger.info(f"移除设置命令处理器: {command}")
            return True
        return False
    
    def has_strategy(self, command: str) -> bool:
        """检查是否有指定命令的处理器
        
        Args:
            command: 命令名称
            
        Returns:
            是否存在处理器
        """
        return command in self._strategies


# 全局注册表实例
setup_command_registry = SetupCommandRegistry()


def register_setup_command(command: str):
    """装饰器：注册设置命令处理器
    
    Args:
        command: 命令名称
        
    Returns:
        装饰器函数
    """
    print(f"register_setup_command: {command}")
    def decorator(strategy_class):
        if not issubclass(strategy_class, SetupCommandStrategy):
            raise TypeError(f"类 {strategy_class.__name__} 必须继承自 SetupCommandStrategy")
        
        # 创建策略实例并注册
        strategy_instance = strategy_class()
        setup_command_registry.register(command, strategy_instance)
        
        return strategy_class
    return decorator


# ==================== 具体策略实现 ====================

@register_setup_command("set_waypoint_config")
class SetWaypointConfigStrategy(SetupCommandStrategy):
    """设置路径点配置策略"""
    
    def can_handle(self, command: str) -> bool:
        return command == "set_waypoint_config"
    
    def execute(self, params: dict, task_builder) -> None:
        """执行路径点配置设置
        
        Args:
            params: 包含以下键的参数字典：
                - waypoint_names: 路径点名称列表
                - offset: 偏移量字典 {'x': float, 'y': float, 'z': float}
                - object_name: 对象名称
            task_builder: 任务构建器实例
        """
        try:
            # 参数验证
            required_keys = ['waypoint_names', 'offset', 'object_name']
            for key in required_keys:
                if key not in params:
                    raise ValueError(f"缺少必需参数: {key}")
            
            waypoint_names = params['waypoint_names']
            offset = params['offset']
            object_name = params['object_name']
            
            # 验证对象是否存在
            if object_name not in task_builder.waypoint_configs:
                raise KeyError(f"未找到对象配置: {object_name}")
            
            # 验证偏移量格式
            valid_offset_keys = ['x', 'y', 'z']
            for key in offset.keys():
                if key not in valid_offset_keys:
                    logger.warning(f"忽略无效的偏移量键: {key}")
            
            # 执行路径点配置更新
            for waypoint_name in waypoint_names:
                if waypoint_name not in task_builder.waypoint_configs[object_name]:
                    logger.warning(f"未找到路径点配置: {waypoint_name} 在对象 {object_name} 中")
                    continue
                
                waypoint_config = task_builder.waypoint_configs[object_name][waypoint_name]
                
                # 验证路径点配置格式
                if 'pose' not in waypoint_config or len(waypoint_config['pose']) < 3:
                    logger.warning(f"路径点 {waypoint_name} 的pose格式无效")
                    continue
                
                # 应用偏移量
                for key, value in offset.items():
                    if key == 'x':
                        waypoint_config['pose'][0] = float(waypoint_config['pose'][0]) + float(value)
                    elif key == 'y':
                        waypoint_config['pose'][1] = float(waypoint_config['pose'][1]) + float(value)
                    elif key == 'z':
                        waypoint_config['pose'][2] = float(waypoint_config['pose'][2]) + float(value)
                
                logger.info(f"更新路径点配置: {object_name}/{waypoint_name}, 偏移量: {offset}")
                
        except Exception as e:
            logger.error(f"设置路径点配置失败: {str(e)}")
            raise


@register_setup_command("set_blackboard_value")
class SetBlackboardValueStrategy(SetupCommandStrategy):
    """设置黑板值策略（扩展示例）"""
    
    def can_handle(self, command: str) -> bool:
        return command == "set_blackboard_value"
    
    def execute(self, params: dict, task_builder) -> None:
        """执行黑板值设置
        
        Args:
            params: 包含以下键的参数字典：
                - key: 黑板键名
                - value: 要设置的值
            task_builder: 任务构建器实例
        """
        try:
            if 'key' not in params or 'value' not in params:
                raise ValueError("缺少必需参数: key 或 value")
            
            key = params['key']
            value = params['value']
            
            # 设置黑板值
            task_builder.blackboard.set(key, value)
            logger.info(f"设置黑板值: {key} = {value}")
            
        except Exception as e:
            logger.error(f"设置黑板值失败: {str(e)}")
            raise

import time
@register_setup_command("delay")
class DelayStrategy(SetupCommandStrategy):
    def can_handle(self, command: str) -> bool:
        return command == "delay"
    
    def execute(self, params: dict, task_builder) -> None:
        time.sleep(params['seconds'])

from papjia_ydsb_demo_task.task_manager.actions.world_manager import WorldModelManager
@register_setup_command("update_world_model")
class UpdateWorldModelStrategy(SetupCommandStrategy):
    def __init__(self) -> None:
        super().__init__()
        self.manager = WorldModelManager()

    def can_handle(self, command: str) -> bool:
        return command == "update_world_model"
    
    def execute(self, params: dict, task_builder) -> None:
        """执行更新世界模型
    
        Args:
            params: 包含以下键的参数字典：
                - vision_data: 视觉数据的黑板键名
                - world_model: 世界模型的黑板键名
            task_builder: 任务构建器实例
        """
        try:
            vision_key = params['vision_data']
            world_model_key = params['world_model']
            
            # 从黑板获取视觉数据
            vision_data = task_builder.blackboard.get(vision_key)
            if vision_data is None:
                raise ValueError(f"黑板中未找到键: {vision_key}")
            
            print ("vision_data:", vision_data)
            # 更新世界模型
            self.manager.update_model_from_vision(vision_data)
            print ("world model data:", self.manager.world_model_data)
            # 将更新后的世界模型存储到黑板
            task_builder.blackboard.set(world_model_key, self.manager.world_model_data)
            
            logger.info(f"成功更新世界模型，包含 {len(self.manager.world_model_data['objects'])} 个物体")
            
        except Exception as e:
            logger.error(f"更新世界模型失败: {str(e)}")
            raise

from papjia_ydsb_demo_task.task_manager.actions.grasp_object import select_target, update_pick_waypoint
@register_setup_command("update_target")
class SelectTargetStrategy(SetupCommandStrategy):
    def can_handle(self, command: str) -> bool:
        return command == "update_target"
    
    def execute(self, params: Dict, task_builder) -> None:
        """执行选择目标并更新抓取点
        
        Args:
            params: 包含以下键的参数字典：
                - world_model: 世界模型数据的黑板键名
                - waypoint_name: 需要更新的路径点名称
                - object_name: 路径点存储对象名称
                - prefer: 抓取倾向
                - frame_id: 坐标系ID
            task_builder: 任务构建器实例
        """
        try:
            # 从黑板获取世界模型数据
            world_model_data = task_builder.blackboard.get(params['world_model'])
            
            # 选择目标
            target = select_target(world_model_data, prefer=params["prefer"])
            if target is None:
                raise ValueError("未找到合适的目标")
                
            target_obj = world_model_data['objects'][target]
            object_type = target_obj['category']
            # 根据物体类型和使用的手获取对应的功能配置
            if object_type not in params["functionality"]:
                raise ValueError(f"未找到物体类型 {object_type} 的功能配置")
            
            func_config = params["functionality"][object_type]
            
            # 根据物体类型选择对应的手臂、offset和gripper参数
            if object_type in ['gloves', 'usb', 'bolts']:  # 左手
                offset = func_config['left_grasp_offset']
                gripper_open = func_config['left_gripper_open'] 
                gripper_close = func_config['left_gripper_close']
                arm_prefix = '左臂-'
            elif object_type in ['tape', 'glue']:  # 右手
                offset = func_config['right_grasp_offset']
                gripper_open = func_config['right_gripper_open']
                gripper_close = func_config['right_gripper_close']
                arm_prefix = '右臂-'
            else:
                # 默认使用右手
                offset = func_config['right_grasp_offset']
                gripper_open = func_config['right_gripper_open']
                gripper_close = func_config['right_gripper_close']
                arm_prefix = '右臂-'
            # 将gripper参数写入黑板
            task_builder.blackboard.set('gripper_open', gripper_open)
            task_builder.blackboard.set('gripper_close', gripper_close)
            
            # 如果是圆柱体,则将物体姿态设为竖直
            if func_config.get('is_cylinder', False):
                world_model_data['objects'][target]['pose'][3:] = [0, 0, 0, 1]

            # 更新抓取路径点
            update_pick_waypoint(
                world_model_data,
                target,
                offset,
                arm_prefix + params['waypoint_name'],
                task_builder.waypoint_configs[params["object_name"]],
                params["frame_id"],
            )
            # 将arm_prefix保存到黑板
            task_builder.blackboard.set('arm_prefix', arm_prefix)
            logger.info(f"已选择目标并更新路径点: {arm_prefix + params['waypoint_name']}")
            
        except Exception as e:
            logger.error(f"选择目标或更新路径点失败: {str(e)}")
            raise



@register_setup_command("waypoint_from_prefix")
class UpdateWaypointStrategy(SetupCommandStrategy):
    """根据arm_prefix更新路径点配置策略"""
    
    def can_handle(self, command: str) -> bool:
        return command == "waypoint_from_prefix"
    
    def execute(self, params: Dict, task_builder) -> None:
        """根据arm_prefix更新路径点配置
        
        Args:
            params: 包含以下键的参数字典：
                - arm_prefix: 机械臂前缀的黑板键名
                - waypoint_name: 需要更新的路径点名称
                - object_name: 路径点存储对象名称
            task_builder: 任务构建器实例
        """
        try:
            # 参数验证
            required_keys = ['arm_prefix', 'waypoint_name', 'object_name']
            for key in required_keys:
                if key not in params:
                    raise ValueError(f"缺少必需参数: {key}")
            
            # 从黑板获取arm_prefix
            arm_prefix = task_builder.blackboard.get(params['arm_prefix'])
            if not arm_prefix:
                raise ValueError(f"未找到arm_prefix: {params['arm_prefix']}")
            
            # 构建源路径点名称
            source_waypoint = arm_prefix + params['waypoint_name']
            
            # 验证源路径点是否存在
            if params['object_name'] not in task_builder.waypoint_configs:
                raise KeyError(f"未找到对象配置: {params['object_name']}")
            
            if source_waypoint not in task_builder.waypoint_configs[params['object_name']]:
                raise KeyError(f"未找到源路径点配置: {source_waypoint}")
            
            # 复制源路径点配置到目标路径点
            source_config = task_builder.waypoint_configs[params['object_name']][source_waypoint]
            task_builder.waypoint_configs[params['object_name']][params['waypoint_name']] = source_config.copy()
            
            logger.info(f"已更新路径点配置: {params['waypoint_name']} (从 {source_waypoint})")
            
        except Exception as e:
            logger.error(f"更新路径点配置失败: {str(e)}")
            raise


    
