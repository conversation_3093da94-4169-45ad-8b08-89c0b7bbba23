#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "请使用sudo运行此脚本"
    exit 1
fi

# 复制服务文件到systemd目录
cp "$SCRIPT_DIR/can-setup.service" /etc/systemd/system/

# 重新加载systemd配置
systemctl daemon-reload

# 启用服务（开机自启）
systemctl enable can-setup.service

# 立即启动服务进行测试
systemctl start can-setup.service

# 检查服务状态
echo "服务状态："
systemctl status can-setup.service --no-pager

echo ""
echo "CAN服务已安装并启用！"
echo "服务将在下次开机时自动运行。"
echo ""
echo "常用命令："
echo "  查看服务状态: sudo systemctl status can-setup.service"
echo "  手动启动服务: sudo systemctl start can-setup.service"
echo "  停止服务: sudo systemctl stop can-setup.service"
echo "  禁用开机自启: sudo systemctl disable can-setup.service" 