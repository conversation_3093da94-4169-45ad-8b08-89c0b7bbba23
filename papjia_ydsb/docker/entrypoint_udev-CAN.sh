#!/bin/bash
set -e

# 启动udev守护进程
/lib/systemd/systemd-udevd --daemon
# 重新加载udev规则
udevadm control --reload-rules
# 触发设备事件
udevadm trigger

# 检查 can0 是否存在
if ip link show can0 > /dev/null 2>&1; then
    echo "Found CAN interface can0, checking status..."

    # 获取 can0 当前状态
    CAN_STATE=$(ip -details link show can0)
    IS_UP=$(echo "$CAN_STATE" | grep -q "state UP" && echo true || echo false)
    BITRATE=$(echo "$CAN_STATE" | grep -Po "bitrate \K[0-9]+" || echo "")

    if [[ "$IS_UP" == "true" && "$BITRATE" == "1000000" ]]; then
        echo "CAN interface can0 is already UP with bitrate 1000000, skipping configuration."
    else
        echo "Configuring CAN interface can0..."

        ip link set can0 down || true
        ip link set can0 up type can bitrate 1000000
        ip link set can0 txqueuelen 1000
        ifconfig can0 up

        echo "CAN interface can0 configured successfully."
    fi
else
    echo "WARNING: CAN interface can0 not found. Skipping configuration."
fi

# 执行CMD命令
exec "$@"