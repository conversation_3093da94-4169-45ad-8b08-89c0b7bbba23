# 使用 papjia_core 作为基础镜像
FROM papjia-docker.pkg.coding.net/weishengwujiance/papjia_szyj/papjia_vision_core:jazzy

# 设置工作目录
WORKDIR /workspace


# 设置 ROS 2 工作空间路径
RUN mkdir -p /workspace/src
# 构建 ROS 2 工作空间
SHELL ["/bin/bash", "-c"]

COPY papjia_driver/rm_ros2/nlopt /workspace/nlopt
WORKDIR /workspace/nlopt
RUN mkdir -p build && \
    cd build && \
    echo "构建目录初始内容：" && ls -al && \
    cmake .. -DCMAKE_INSTALL_PREFIX=/usr/local && \
    make -j$(nproc) && \
    make install
WORKDIR /workspace

RUN pip install --no-cache-dir --ignore-installed pymongo flask flask_cors open3d rosdepc numpy==1.26.4

RUN rm -rf /etc/apt/sources.list.d/ros2* && \
    curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://mirrors.ustc.edu.cn/ros2/ubuntu $(lsb_release -sc) main" | sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null 

RUN apt-get update && apt-get install -y --no-install-recommends \
    udev \
    iproute2 \
    net-tools \
    can-utils \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*



RUN apt-get update && apt-get install -y --no-install-recommends \
    ros-${ROS_DISTRO}-apriltag* \
    ros-${ROS_DISTRO}-urdf \  
    ros-${ROS_DISTRO}-kdl-parser \ 
    ros-${ROS_DISTRO}-spacenav \
    ros-${ROS_DISTRO}-rmw-cyclonedds-cpp \
    ros-${ROS_DISTRO}-rosbridge-suite \
    ros-${ROS_DISTRO}-pinocchio \
    ros-${ROS_DISTRO}-usb-cam \
    ros-${ROS_DISTRO}-opennav-docking* \
    ros-${ROS_DISTRO}-twist-mux \
    libyaml-cpp-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装pip依赖
COPY papjia_ydsb/docker/requirements.txt /workspace/requirements.txt
RUN pip install --no-cache-dir --ignore-installed -r /workspace/requirements.txt

# 安装udev规则
RUN mkdir -p /etc/udev/rules.d/
    # UDEV 规则
COPY papjia_ydsb/docker/entrypoint_udev.sh /entrypoint_udev.sh
RUN chmod +x /entrypoint_udev.sh
# 版本信息
COPY papjia_ydsb/docker/.version_information_my.sh /.version_information.sh
RUN chmod +x /.version_information.sh

# papjia_msgs
COPY papjia_core/papjia_msgs /workspace/src/papjia_msgs
RUN source /opt/ros/${ROS_DISTRO}/setup.bash && \
    source /papjia_core_ws/install/setup.bash && \
    colcon build && \
    rm -rf /workspace/src/*

# papjia_bt
COPY papjia_core/papjia_bt /workspace/src/papjia_bt
RUN source /opt/ros/${ROS_DISTRO}/setup.bash && \
    source /papjia_core_ws/install/setup.bash && \
    source /workspace/install/setup.bash && \
    colcon build && \
    rm -rf /workspace/src/*

# papjia_mtc
COPY papjia_core/papjia_mtc /workspace/src/papjia_mtc
RUN source /opt/ros/${ROS_DISTRO}/setup.bash && \
    source /papjia_core_ws/install/setup.bash && \
    source /workspace/install/setup.bash && \
    colcon build && \
    rm -rf /workspace/src/*

# papjia_skill 
COPY papjia_core/papjia_skill /workspace/src/papjia_skill
RUN source /opt/ros/${ROS_DISTRO}/setup.bash && \
    source /papjia_core_ws/install/setup.bash && \
    source /workspace/install/setup.bash && \
    colcon build && \
    rm -rf /workspace/src/*


# 视觉包
COPY papjia_core/papjia_vision /workspace/src/papjia_vision
RUN source /opt/ros/${ROS_DISTRO}/setup.bash && \
    source /papjia_core_ws/install/setup.bash && \
    source /workspace/install/setup.bash && \
    colcon build && \
    rm -rf /workspace/src/*

# 外设驱动接口
COPY papjia_core/papjia_control_msgs /workspace/src/papjia_control_msgs
# 工具

COPY papjia_core/papjia_config_manage /workspace/src/papjia_config_manage
COPY papjia_core/papjia_type_trans /workspace/src/papjia_type_trans
RUN source /opt/ros/${ROS_DISTRO}/setup.bash && \
    source /papjia_core_ws/install/setup.bash && \
    source /workspace/install/setup.bash && \
    colcon build && \
    rm -rf /workspace/src/*  

# 外部驱动
COPY papjia_driver /workspace/src/papjia_driver

RUN source /opt/ros/${ROS_DISTRO}/setup.bash && \
    source /papjia_core_ws/install/setup.bash && \
    source /workspace/install/setup.bash && \
    colcon build && \
    rm -rf /workspace/src/*
    
# 导航与底盘
COPY papjia_core/papjia_navigation /workspace/src/papjia_navigation
COPY papjia_core/papjia_car_driver /workspace/src/papjia_car_driver
COPY papjia_core/papjia_joy_control /workspace/src/papjia_joy_control
RUN source /opt/ros/${ROS_DISTRO}/setup.bash && \
    source /papjia_core_ws/install/setup.bash && \
    source /workspace/install/setup.bash && \
    colcon build && \
    rm -rf /workspace/src/*
    
# project
COPY papjia_ydsb /workspace/src/papjia_ydsb
RUN source /opt/ros/${ROS_DISTRO}/setup.bash && \
    source /papjia_core_ws/install/setup.bash && \
    source /workspace/install/setup.bash && \
    colcon build && \
    rm -rf /workspace/src/*

# 使工作空间的 setup 文件对每个 shell 会话有效
RUN echo "source /workspace/install/setup.bash" >> ~/.bashrc
# # 设置任务别名
RUN echo "alias sim='bash /workspace/src/papjia_ydsb/scripts/run.sh --sim'" >> ~/.bashrc && \
    echo "alias real='bash /workspace/src/papjia_ydsb/scripts/run.sh --real'" >> ~/.bashrc && \
    echo "alias camera_orbbec='ros2 launch orbbec_camera gemini_330_series.launch.py enable_point_cloud:=true enable_colored_point_cloud:=true'" >> ~/.bashrc && \
    echo "alias vision='ros2 launch hand_demo_config pose.launch.py'" >> ~/.bashrc && \
    echo "alias navigation='ros2 launch papjia_navigation_core bringup_launch.py'" >> ~/.bashrc && \
    echo "alias base='ros2 launch papjia_car_bringup papjia_car.launch.py'">> ~/.bashrc && \
    echo "alias adjust='ros2 run papjia_odom_controller odom_controller_node.py'"

# 启动 bash 终端并加载 ROS 2 环境
ENTRYPOINT ["/entrypoint_udev.sh"]
CMD ["/bin/bash", "-c", "source /workspace/install/setup.bash && exec bash"]
