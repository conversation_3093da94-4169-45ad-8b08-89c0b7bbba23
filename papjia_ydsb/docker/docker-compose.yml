services:

  papjia_ydsb:
    image: papjia-docker.pkg.coding.net/weishengwujiance/papjia_szyj/papjia_ydsb:jazzy
    mem_limit: 16g
    build:
      context: ../../
      dockerfile: papjia_ydsb/docker/Dockerfile
    container_name: papjia_ydsb_container
    volumes:
     # - ./ros2_shm.xml:/ros2_shm.xml
      - ../../papjia_ydsb:/workspace/src/papjia_ydsb
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - ${XAUTHORITY:-$HOME/.Xauthority}:/root/.Xauthority

      - /etc/asound.conf:/etc/asound.conf:ro
      - /etc/pulse:/etc/pulse:ro
      - /run/user/1000/pulse:/run/user/1000/pulse
    network_mode: host
    ipc: host
    stdin_open: true  # 允许交互
    tty: true  # 保持终端运行
    privileged: true
    devices:
      - /dev/snd:/dev/snd
    environment:
      - ROS_DOMAIN_ID=17
      - RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
      - TZ=Asia/Shanghai
      - DISPLAY=${DISPLAY}
      - QT_X11_NO_MITSHM=1
      - NVIDIA_DRIVER_CAPABILITIES=all
      - WAYPOINT_CONFIG_FILEPATH=/workspace/src/papjia_ydsb/papjia_ydsb_config/config/waypoint_configs.json
      - TASK_CONFIG_FILEPATH=/workspace/src/papjia_ydsb/papjia_ydsb_config/config/task_configs.json
      - ACTION_MODEL_CONFIG_FILEPATH=/workspace/src/papjia_ydsb/papjia_ydsb_config/config/action_model.yaml
      - PULSE_SERVER=unix:/run/user/1000/pulse/native
      - ALSA_CARD=0
    working_dir: /workspace
    command: bash -c "source /workspace/install/setup.bash && colcon build && source /workspace/install/setup.bash && exec bash"  # 启动 bash 并加载 ROS 2 环境
