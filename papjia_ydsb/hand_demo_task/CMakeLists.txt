cmake_minimum_required(VERSION 3.8)
project(hand_demo_task)

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(ament_cmake_python REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclpy REQUIRED)

# install(
#   DIRECTORY
#     config 
#     description
#     launch
#   DESTINATION 
#     share/${PROJECT_NAME}
# )

ament_python_install_package(${PROJECT_NAME})

# Install Python executables
install(PROGRAMS
  hand_demo_task/mock_server/mock_vision_server.py
  hand_demo_task/mock_server/mock_driver_server.py
  DESTINATION lib/${PROJECT_NAME}
)

# Install additional files
install(FILES
  hand_demo_task/mock_server/README.md
  DESTINATION lib/${PROJECT_NAME}/mock_server
)

ament_package()
