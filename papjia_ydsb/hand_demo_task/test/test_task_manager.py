import rclpy
from papjia_skill.action_models import ActionModels
from papjia_szyj_config.task_manager.szyj_task_manager import SZYJTaskManager

def main(args=None):
    rclpy.init(args=args)

    task_manager = SZYJTaskManager()
    try:
        task_manager.prepare_task_datas()
        result = task_manager.create_task()
        print(result)
        
    except Exception as e:
        print(e, flush=True)

if __name__ == "__main__":
    models = ActionModels(
        "/workspace/src/papjia_skill/config/action_model.yaml"
    )  # 需要显式加载原子动作模型以检查字段
    main()