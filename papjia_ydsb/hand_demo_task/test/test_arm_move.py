import json

from papjia_szyj_config.task_manager.core.arm_move import get_plan_and_execute_waypoints_sequence, JointWaypoint, CartWaypoint, get_waypoint_from_config

from papjia_skill.action_models import ActionModels

ActionModels("/workspace/src/papjia_skill/config/action_model.yaml")

waypoint_file_path = "/workspace/src/papjia_szyj/papjia_szyj_config/config/szyj_waypoint_configs.json"
with open(waypoint_file_path, "r") as f:
    all_waypoint_configs = json.load(f)
waypoint_configs = all_waypoint_configs['test_tube']

w1 = JointWaypoint(
    waypoint_name="w1",
    joint_names=waypoint_configs['home_left']['joint_names'],
    joint_values=waypoint_configs['home_left']['joint_values'],
    group=waypoint_configs['home_left']['group'],
    planner=waypoint_configs['home_left']['planner'],
    max_velocity_scaling_factor=0.5,
    max_acceleration_scaling_factor=0.5,
)

w2 = CartWaypoint(
    waypoint_name="w2",
    position=waypoint_configs['左臂-药液试管-初始点']['pose'][:3],
    orientation=waypoint_configs['左臂-药液试管-初始点']['pose'][3:],
    group=waypoint_configs['左臂-药液试管-初始点']['group'],
    frame_id=waypoint_configs['左臂-药液试管-初始点']['frame_id'],
    ik_frame=waypoint_configs['左臂-药液试管-初始点']['ik_frame'],
    planner=waypoint_configs['左臂-药液试管-初始点']['planner'],
    max_velocity_scaling_factor=0.5,
    max_acceleration_scaling_factor=0.5,
)

waypoint_names = ['home_left', '左臂-药液试管-初始点', '左臂-药液试管库-抓取-准备', '左臂-药液试管库-抓取-就绪-理论值', '左臂-药液试管库-抓取-撤退'] 
waypoints = []
for waypoint_name in waypoint_names:
    waypoints.append(get_waypoint_from_config(waypoint_name, waypoint_configs[waypoint_name]))

sequence = get_plan_and_execute_waypoints_sequence(waypoints)

print(sequence)