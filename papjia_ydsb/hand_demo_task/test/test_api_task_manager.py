import rclpy
from flask import Flask
from flask_cors import CORS
from papjia_skill.action_models import ActionModels
from papjia_szyj_config.task_manager.api.routes import task_manager_api


def main(args=None):
    rclpy.init(args=None)

    models = ActionModels("/workspace/src/papjia_skill/config/action_model.yaml")  # 需要显式加载原子动作模型以检查字段

    # ------------------ Flask App ------------------ #
    app = Flask(__name__)
    app.json.ensure_ascii = False
    CORS(app)
    
    # 注册Blueprint，使用更具描述性的名称
    app.register_blueprint(task_manager_api)

    app.run(debug=True, host="0.0.0.0", port=9799)


if __name__ == "__main__":
    main()
