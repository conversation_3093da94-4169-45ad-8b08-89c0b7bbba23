import json
import rclpy
import math
from papjia_behavior_interface.action import ExecuteTree
from papjia_skill.executor import PapjiaSkillExecutor
from papjia_skill.btree import BehaviorRoot, BehaviorTree, Sequence, Parallel, SubTree
from papjia_skill.action_models import ActionModels
from papjia_skill.utils import swap_quotes, add_escape_character
from papjia_skill.atom.object_detect import ObjectDetect
from papjia_skill.atom.object_to_json import ObjectToJson
from papjia_skill.atom.remove_escape_character import RemoveEscapeCharacter
from papjia_skill.atom.update_worldmodel import UpdateWorldmodel
from tf_transformations import euler_from_quaternion, quaternion_from_euler, quaternion_matrix

class WorldModelManager:
    """
    一个处理与槽管理相关的数据操作的类。

    属性:
        db_srv_topic (str): 数据库管理服务的主题。
        executor (PapjiaSkillExecutor): 用于运行行为树的执行器。
        slot_types (dict): 一个将槽类型映射到其各自槽名称的字典。
    """

    def __init__(
        self,
        vision_srv_topic="/papjia_vision/service_object_detect",
        executor=None
    ):
        self.vision_srv_topic = vision_srv_topic

        if executor == None:
            self.executor = PapjiaSkillExecutor(node_name="world_model")
        else:
            self.executor = executor
        self.obj_vision_map = {
            "Bridge": "bridge_tool",
            "Cylinder": "cylinder_tool",
            "Plane": "plane_tool",
            "Stand": "stand_tool",
        }
        self.default_height = {
            "Bridge": 0.04,
            "Cylinder": 0.0,
            "Plane": 0.04,
            "Stand": 0.0,
        }

        # self.default_height = {
        #     "Bridge": 0.04,
        #     "Cylinder": 0.0,
        #     "Plane": 0.075,
        #     "Stand": 0.0,
        # }
        # 初始化处理模块
        self.mapper = ObjectMapper(self.obj_vision_map)
        self.height_adjuster = HeightAdjuster(self.default_height)
        self.orientation_filter = OrientationFilter()

        # 世界模型存储
        self.world_model_data = {
            "objects": {},
            "containers": {}  # 容器状态暂不实现
        }


    def exec_tree(self, seq, tree_name, keys=[]):
        """
        执行一个包含给定序列和树名称的行为树。

        参数:
            seq (Sequence): 要添加为行为树子节点的序列。
            tree_name (str): 行为树的名称。
            keys (list, optional): 需要从执行后的黑板导出的数据的变量名列表，默认为空。

        返回:
            Result: 执行行为树的结果，一般为字典。
        """
        tree = BehaviorTree(tree_name)
        tree.add_child(seq)
        root = BehaviorRoot()
        root.add_child(tree)
        tree_str = root.to_str()
        # tree_str = swap_quotes(root.to_str())
        # print(tree_str, flush=True)
        result = self.executor.execute_tree(tree_string=tree_str, tree_name=tree_name, keys=keys)
        return result.result

    def generate_sequence_update_pose_by_vision(self, seq_name="UpdatePoseByVision"):
        object_detect = ObjectDetect(
            service_name=self.vision_srv_topic,
            max_num=5,
            min_score=0.8,
            objects="{objs}",
        )
        object_to_json = ObjectToJson(
            objects="{objs}",
            json_result="{objs_json}",
        )
        seq = Sequence(seq_name)
        seq.add_child(object_detect)
        seq.add_child(object_to_json)
        return seq

    def generate_show_worldmodel_tree(self, seq_name="ShowWorldmodel"):
            # world_json = json.dumps(
            #     self.world_model_data,
            #     indent=2,          # 缩进2空格
            #     ensure_ascii=False, # 支持中文显示
            #     sort_keys=True     # 键名排序
            # )
            # print ("world_json:", self.world_model_data)
            update = UpdateWorldmodel(
                service_name="update_world_model", 
                world_model_json=swap_quotes(add_escape_character(self.world_model_data.__str__())),
                success="{success}",
                message="{message}")
            seq = Sequence(seq_name)
            seq.add_child(update)
            return seq
   
    def get_pose_by_vision(self, tree_name="UpdatePoseByVision"):
        seq = self.generate_sequence_update_pose_by_vision(f"Seq{tree_name}")
        result = self.exec_tree(seq=seq, tree_name=tree_name, keys=["objs_json"])
        objs_json = json.loads(result)
        objs = json.loads(objs_json["objs_json"])
        return objs
        # # 假设这里获得的是视觉识别结果
        # res = [{"category": "bridge_tool", \
        #         "pose": [0.28463615371309886, 0.3954611513298507, 0.061500437557697296, 0.0, 0.0, -0.026784122980726913, 0.9996414850970434], 'scale': [0.2063557505607605, 0.092050701379776, 0.07996837794780731], \
        #         "score": 90}, 
        #         {"category": "cylinder_tool", \
        #                         "pose": [0.6440666561518479, 0.40379683237182595, 0.01723315566778183, 0.0, 0.0, 0.0014875523689836293, 0.9999988083604949], \
        #                         "score": 90},                
        #         {"category": "cylinder_tool", \
        #                         "pose": [0.6903295980726817, -0.4062457300512257, 0.023007480427622795, 0.0, 0.0, -0.33634137523371643, 0.9417401926310657], \
        #                         "score": 90},
        #         {"category": "plane_tool", \
        #                         "pose": [0.3579988714739424, -0.4122642645880017, 0.04600575193762779, 0.0, 0.0, -0.7072262274658014, 0.706987104670402], \
        #                         "score": 90},
        #         {"category": "stand_tool", \
        #                         "pose": [0.551723933227935, -0.429785097814684, 0.11338715255260468, 0.0, 0.0, -0.0004927070696361732, 0.9999999106366408], \
        #                         "score": 90}, 
        #         ]
        # return res

    def update_model_from_vision(self, vision_data):
        """更新世界模型的视觉入口"""
        # 第一步：重新映射物体
        objects = self.mapper.remap_objects(vision_data)
        
        # 第二步：调整高度
        self.height_adjuster.adjust(objects)
        
        # 第三步：过滤角度
        for obj_info in objects.values():
            obj_info["pose"] = self.orientation_filter.adjust_orientation(obj_info["pose"], obj_info["category"])
        
        # 更新世界模型
        self.world_model_data["objects"] = objects

    def show_model(self):
        # print ("CURRENT WORLD MODEL:", self.world_model_data)
        seq = self.generate_show_worldmodel_tree()
        result = self.exec_tree(seq=seq, tree_name="ShowWorldmodel", keys=['success', 'message'])     
        result_dict = json.loads(result)
        return result_dict
    
    def update_model(self):
        """更新世界模型的总入口"""
        vision_data = self.get_pose_by_vision()
        self.update_model_from_vision(vision_data)
        result = self.show_model()
        return result
    
class ObjectMapper:
    def __init__(self, obj_vision_map):
        self.reverse_map = {v: k for k, v in obj_vision_map.items()}
    
    def remap_objects(self, vision_results):
        """维护方式1：物品种类/ID重映射和排序"""
        categorized = {}
        
        # 按视觉类别分组并收集y坐标
        for item in vision_results:
            if (obj_type := self.reverse_map.get(item["category"])) is None:
                continue
            y = item["pose"][1]
            categorized.setdefault(obj_type, []).append((y, item))

        # 按y降序排序并生成ID
        objects = {}
        for obj_type, items in categorized.items():
            sorted_items = sorted(items, key=lambda x: -x[0])
            for idx, (y, item) in enumerate(sorted_items):
                obj_id = f"{obj_type}_{idx}"
                objects[obj_id] = {
                    "pose": item["pose"].copy(),
                    "category": obj_type,
                    "raw_data": item  # 保留原始数据
                }
        return objects

class HeightAdjuster:
    def __init__(self, default_heights):
        self.default_heights = default_heights
    
    def adjust(self, objects):
        """维护方式2：调整物品高度"""
        for obj_info in objects.values():
            obj_type = obj_info["category"]
            obj_info["pose"][2] = self.default_heights.get(obj_type, 0)


class OrientationFilter:
    @staticmethod
    def adjust_orientation(pose, category=None):
        """过滤roll/pitch，仅保留yaw（绕Z轴旋转）。对于Cylinder类别，yaw强制设为0"""
        x, y, z = pose[0], pose[1], pose[2]
        qx, qy, qz, qw = pose[3], pose[4], pose[5], pose[6]
        
        # 如果是Cylinder，直接返回yaw为0的四元数
        if category == "Cylinder":
            new_quat = quaternion_from_euler(0, 0, 0)
            return [x, y, z, new_quat[0], new_quat[1], new_quat[2], new_quat[3]]
        
        # 将四元数转换为旋转矩阵（注意参数顺序为[w, x, y, z]）
        rotation_matrix = quaternion_matrix([qx, qy, qz, qw])
        R = rotation_matrix[:3, :3]  # 提取3x3旋转矩阵
        
        # 直接从旋转矩阵计算yaw角
        yaw = math.atan2(R[1, 0], R[0, 0])
        
        # 生成仅绕Z轴旋转的新四元数
        new_quat = quaternion_from_euler(0, 0, yaw)
        return [x, y, z, new_quat[0], new_quat[1], new_quat[2], new_quat[3]]
    

if __name__ == "__main__":
    manager = WorldModelManager()
    
    # 模拟视觉输入
    vision_input = [{
        "category": "bridge_tool",
        "pose": [0.1, 0.4, 0.02, 0, 0, 0, 1],  # x, y, z, qx, qy, qz, qw
        "score": 90
    }]
    
    manager.update_model()
    print("更新后的世界模型:", manager.world_model_data)
    


