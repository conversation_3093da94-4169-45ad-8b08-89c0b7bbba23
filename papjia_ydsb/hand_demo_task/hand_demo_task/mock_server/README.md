# Mock Vision Server

## 简介
Mock Vision Server 是一个用于测试和开发的视觉服务模拟器。它提供了一个交互式的命令行界面，可以模拟物体检测服务，支持场景管理、单物体调试和批量测试等功能。

## 功能特点
- 🎯 预定义场景管理
- 🔧 单物体精确调试
- 🔄 批量测试支持
- 📊 基于配置的姿态约束
- 🎲 随机姿态生成
- 💾 场景保存和加载
- ⌨️ 命令自动补全

## 快速开始

### 启动服务器
```bash
ros2 run hand_demo_task mock_vision_server
```

### 基本操作
1. 查看帮助信息：
```
help                # 显示所有命令
help <command>      # 显示特定命令的详细说明
```

2. 查看当前状态：
```
status              # 显示服务器当前状态，包括当前模式和场景信息
scene               # 显示可用场景
```

## 主要功能

### 1. 调试模式
调试模式允许你精确控制单个物体的姿态。

```bash
# 进入调试模式
debug Bridge 0      # 调试 Bridge_0 物体
debug off          # 退出调试模式

# 查看和设置姿态
pose                # 显示当前姿态
pose 0.3 0.4 0.5   # 设置 x=0.3 y=0.4 yaw=0.5
pose x 0.3         # 只设置 x 坐标
pose y 0.4         # 只设置 y 坐标
pose yaw 0.5       # 只设置 yaw 角度

# 随机姿态
pose random        # 随机生成所有参数
pose random x      # 只随机生成 x 坐标
pose random y      # 只随机生成 y 坐标
pose random yaw    # 只随机生成 yaw 角度

# 查看限制范围
limits              # 显示当前物体的有效范围
```

### 2. 批量测试
批量测试模式需要先进入批处理模式，然后才能执行相关命令。

```bash
# 进入批处理模式
batch               # 进入批处理模式

# 生成测试
batch generate 20   # 生成20个测试场景

# 从文件加载/保存
batch load tests.json   # 从文件加载测试场景
batch save tests.json   # 保存当前测试场景到文件

# 控制测试流程
batch start            # 从头开始运行测试
batch start 5         # 从第5个测试开始
batch single 3        # 只运行第3个测试
batch status         # 查看测试进度
batch stop           # 停止当前测试
batch off            # 退出批处理模式
```

### 3. 场景管理
可以在不同的预定义场景之间切换。

```bash
# 切换场景
scene               # 显示所有可用场景
scene default      # 切换到默认场景
```

## 命令自动补全
服务器支持命令自动补全功能，提高输入效率：

1. 主命令补全：
   - 输入命令前缀后按 Tab 键
   - 支持的主命令：debug, batch, pose, scene, mode, help, status, limits, quit

2. 子命令补全：
   - debug：补全对象类型（Bridge, Cylinder, Plane, Stand）和ID
   - batch：补全子命令（generate, load, save, start, single, status, stop, off）
   - pose：补全参数类型（x, y, yaw, random）
   - scene：补全可用场景名称
   - mode：补全可用模式（predefined, constrained, debug, batch）

使用方法：
```bash
de<Tab>             # 补全为 "debug"
debug B<Tab>        # 补全为 "debug Bridge"
debug Bridge <Tab>  # 显示可用的对象ID
ba<Tab>             # 补全为 "batch"
batch ge<Tab>       # 补全为 "batch generate"
```

## 配置文件
服务器使用 `worldmodel.yaml` 配置文件来定义物体的姿态约束：

```yaml
validation:
  required_counts:
    Bridge: 1
    Stand: 1
    Plane: 1
    Cylinder: 2
  pose_limits:
    Bridge_0:
      x: [-0.5, 0.5]
      y: [-0.3, 0.3]
      yaw: [-0.785, 0.785]
```

## 注意事项
1. 所有姿态值必须在 `worldmodel.yaml` 定义的范围内
2. 必须先进入对应模式才能执行相关命令：
   - 使用 `debug` 进入调试模式
   - 使用 `batch` 进入批处理模式
3. 调试模式和批处理模式可以通过以下方式退出：
   - `debug off` 退出调试模式
   - `batch off` 退出批处理模式
4. 命令提示符会显示当前模式：
   - (normal) - 普通模式
   - (debug:Object_ID) - 调试模式
   - (batch:ready) - 批处理就绪
   - (batch:running) - 批处理运行中

## 常见问题
1. Q: 为什么无法执行批处理命令？
   A: 请先使用 `batch` 命令进入批处理模式。

2. Q: 如何知道当前在哪个模式？
   A: 查看命令提示符或使用 `status` 命令。

3. Q: 如何使用自动补全？
   A: 输入命令的前几个字母后按 Tab 键，按两次 Tab 显示所有可能的选项。

## 贡献指南
欢迎提交问题报告和改进建议。如需贡献代码，请确保：
1. 遵循现有的代码风格
2. 添加适当的注释和文档
3. 确保所有测试通过

## 许可证
[License 信息] 