#!/usr/bin/env python3
"""
Vision Mock Server
This module provides a mock implementation of the vision service for testing and development.
It simulates object detection with configurable scenes and supports various testing modes.
"""

import rclpy
from rclpy.node import Node
from papjia_vision_interface.srv import DetectObjs
from papjia_vision_interface.msg import Object
from geometry_msgs.msg import Vector3, Pose, Point, Quaternion
import yaml
import json
import numpy as np
from pathlib import Path
import random
import threading
import cmd
import readline
from rclpy.callback_groups import ReentrantCallbackGroup
from rclpy.service import Service
from tf_transformations import euler_from_quaternion, quaternion_from_euler

class VisionMockServer(Node):
    """
    A mock server that simulates the vision system's object detection service.
    
    Features:
    - Uses predefined scenes
    - Generates valid poses based on worldmodel.yaml constraints
    - Supports dynamic position adjustment for debugging
    - Supports batch testing
    """

    def __init__(self):
        super().__init__('vision_mock_server')
        
        # 物体类型映射（与world_manager一致）
        self.obj_vision_map = {
            "Tape": "tape",
            # "Coffee": "coffee",
            # "Gloves": "gloves",
            # "Usb": "usb",
            # "Milk": "milk",
            # "Glue": "glue",
            # "Bolts": "bolts",
        }
        
        # 初始化场景和模式
        self._init_scenes()
        self.current_scene = "default"
        self.current_mode = "predefined"  # predefined, constrained, debug, batch
        
        # 用于调试模式的当前调试对象
        self.debug_object = None
        self.debug_pose = None
        
        # 批量测试参数
        self.batch_test_params = {
            "num_tests": 10,
            "current_test": 0,
            "results": []
        }
        
        # 加载配置文件
        self.config_path = "/workspace/src/papjia_ydsb/papjia_ydsb_config/config/worldmodel.yaml"
        try:
            self.load_config()
        except Exception as e:
            self.get_logger().error(f"Failed to load config: {e}")
            self.get_logger().warn("Using default configuration")
            # 设置默认配置
            self.config = {
                'validation': {
                    'required_counts': {
                        "Tape": 1,
                    },
                    'pose_limits': {
                        'Tape': {
                            'x': [-0.5, 0.5],
                            'y': [0.0, 0.3],
                            'yaw': [-0.2, 0.2]
                        }
                    }
                },
                'visual': {
                    'categories': {},
                    'instances': {},
                    'background': {}
                }
            }
            self.model_config = {
                'categories': {},
                'background': {}
            }
        
        # 创建服务
        self.srv = self.create_service(
            DetectObjs,
            '/papjia_vision/service_object_detect',
            self.object_detect_callback,
            callback_group=ReentrantCallbackGroup()
        )
        
        self.get_logger().info('Vision Mock Server is ready')
        self.get_logger().info(f'Current scene: {self.current_scene} (contains {len(self.scenes[self.current_scene])} objects)')

    def load_config(self):
        """Load and parse worldmodel.yaml configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            # self.get_logger().info("Successfully loaded configuration file")
            # self.get_logger().info(f"Loaded required counts: {self.config['validation']['required_counts']}")
            # self.get_logger().info(f"Loaded pose limits: {self.config['validation']['pose_limits']}")
            # self.get_logger().info(f"Loaded visual config: {self.config['visual']}")
        except Exception as e:
            self.get_logger().error(f"Failed to load config: {e}")
            self.get_logger().warn("Using default configuration")
            # 设置默认配置
            self.config = {
                'validation': {
                    'required_counts': {
                        "Tape": 1,
                    },
                    'pose_limits': {
                        'Tape': {
                            'x': [-0.5, 0.5],
                            'y': [0.0, 0.3],
                            'yaw': [-0.2, 0.2]
                        }
                    }
                },
                'visual': {
                    'categories': {},
                    'instances': {},
                    'background': {}
                }
            }

    def _init_scenes(self):
        """Initialize predefined scenes for object detection"""
        # 根据world model的要求初始化场景
        self.world_model = {
            "default": {
                "Tape_0": {
                    "category": "Tape",
                    "pose": [0.353, 0.406, 0.0, 0.0, 0.0, 0.0, 1.0],
                    "scale": [0.05, 0.05, 0.05]
                # },
                # "Coffee_0": {
                #     "category": "Coffee",
                #     "pose": [0.30, -0.1, 0.0, 0.0, 0.0, 0.0, 1.0],
                #     "scale": [0.05, 0.05, 0.05]
                # },
                # "Gloves_0": {  # 添加第二个Cylinder
                #     "category": "Gloves",
                #     "pose": [0.30, 0.1, 0.0, 0.0, 0.0, 0.0, 1.0],
                #     "scale": [0.05, 0.05, 0.05]
                # },
                # "Usb_0": {
                #     "category": "Usb",
                #     "pose": [0.05, 0.3, 0.0, 0.0, 0.0, 0.0, 1.0],
                #     "scale": [0.05, 0.05, 0.05]
                # },
                # "Milk_0": {
                #     "category": "Milk",
                #     "pose": [0.30, -0.1, 0.0, 0.0, 0.0, 0.0, 1.0],
                #     "scale": [0.05, 0.05, 0.05]
                # },
                # "Glue_0": {
                #     "category": "Glue",
                #     "pose": [0.30, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
                #     "scale": [0.05, 0.05, 0.05]
                # },
                # "Bolts_0": {
                #     "category": "Bolts",
                #     "pose": [0.30, 0.1, 0.0, 0.0, 0.0, 0.0, 1.0],
                #     "scale": [0.05, 0.05, 0.05]
                }
            },
            "empty": {},
            "single_object": {}  # 将在debug模式中动态填充
        }
        
        # 转换为视觉识别结果格式的场景
        self.scenes = {}
        for scene_name, world_objects in self.world_model.items():
            vision_objects = []
            for obj_id, obj_data in world_objects.items():
                vision_obj = {
                    "category": self.obj_vision_map[obj_data["category"]],
                    "pose": obj_data["pose"].copy(),
                }
                if "scale" in obj_data:
                    vision_obj["scale"] = obj_data["scale"].copy()
                vision_objects.append(vision_obj)
            self.scenes[scene_name] = vision_objects
        
        # 验证预设场景中的姿态是否在有效范围内
        self.get_logger().info('Initializing scenes...')

    def _generate_random_pose(self, category):
        """生成随机位姿"""
        # 获取物体的尺寸和位置限制
        limits = self.config['validation']['pose_limits'].get(f"{category}_0", {})
        x_limits = limits.get('x', [-1.0, 1.0])
        y_limits = limits.get('y', [-1.0, 1.0])
        yaw_limits = limits.get('yaw', [-np.pi, np.pi])
        
        # 生成随机位置和朝向
        x = np.random.uniform(x_limits[0], x_limits[1])
        y = np.random.uniform(y_limits[0], y_limits[1])
        yaw = np.random.uniform(yaw_limits[0], yaw_limits[1])
        
        # 生成四元数
        qx, qy, qz, qw = self._euler_to_quaternion(0, 0, yaw)
        
        return [x, y, 0.0, qx, qy, qz, qw]

    def _euler_to_quaternion(self, roll, pitch, yaw):
        """欧拉角转四元数"""
        cy = np.cos(yaw * 0.5)
        sy = np.sin(yaw * 0.5)
        cp = np.cos(pitch * 0.5)
        sp = np.sin(pitch * 0.5)
        cr = np.cos(roll * 0.5)
        sr = np.sin(roll * 0.5)
        
        qw = cr * cp * cy + sr * sp * sy
        qx = sr * cp * cy - cr * sp * sy
        qy = cr * sp * cy + sr * cp * sy
        qz = cr * cp * sy - sr * sp * cy
        
        return qx, qy, qz, qw

    def generate_valid_pose(self, category, max_attempts=100):
        """生成一个有效的位姿，确保footprint不与其他物体重叠"""
        for _ in range(max_attempts):
            pose = self._generate_random_pose(category)
            # 检查是否与现有物体重叠
            if not self._check_footprint_collision(category, pose):
                return pose
        return None

    def _check_footprint_collision(self, category, pose, current_objects=None, obj_id=None):
        """检查给定位姿是否与现有物体重叠"""
        # 获取当前物体的footprint
        # 优先查找实例级footprint
        instance_footprint = self.config['visual']['instances'].get(obj_id, {}).get('footprint')
        if instance_footprint:
            footprint = instance_footprint
        else:
            category_footprint = self.config['visual']['categories'].get(category, {}).get('footprint')
            if category_footprint:
                footprint = category_footprint
            else:
                self.get_logger().warn(f"No footprint found for {category} in instances or categories")
                return False
            
        transformed_footprint = self._transform_footprint(footprint, pose)
        
        # 检查与已生成物体的footprint是否重叠
        if current_objects:
            for existing_id, obj_data in current_objects.items():
                obj_category = obj_data['category']
                # 优先查找实例级footprint
                obj_instance_footprint = self.config['visual']['instances'].get(existing_id, {}).get('footprint')
                if obj_instance_footprint:
                    obj_footprint = obj_instance_footprint
                else:
                    obj_footprint = self.config['visual']['categories'].get(obj_category, {}).get('footprint')
                if not obj_footprint:
                    continue
                obj_transformed_footprint = self._transform_footprint(obj_footprint, obj_data['pose'])
                if self._check_polygon_intersection(transformed_footprint, obj_transformed_footprint):
                    return True

        # 检查与背景物体的footprint是否重叠
        for bg_id, bg_cfg in self.config['visual']['background'].items():
            bg_footprint = bg_cfg.get('footprint')
            if not bg_footprint:
                continue
            bg_pose = bg_cfg['position'] + bg_cfg['orientation']  # [x, y, z, qx, qy, qz, qw]
            bg_transformed_footprint = self._transform_footprint(bg_footprint, bg_pose)
            if self._check_polygon_intersection(transformed_footprint, bg_transformed_footprint):
                return True

        return False

    def _check_polygon_intersection(self, poly1, poly2):
        """检查两个多边形是否相交（使用分离轴定理）"""
        def get_axes(polygon):
            axes = []
            for i in range(len(polygon)):
                p1 = np.array(polygon[i])
                p2 = np.array(polygon[(i + 1) % len(polygon)])
                edge = p2 - p1
                normal = np.array([-edge[1], edge[0]])
                axes.append(normal / np.linalg.norm(normal))
            return axes

        def project_polygon(polygon, axis):
            dots = [np.dot(np.array(vertex), axis) for vertex in polygon]
            return min(dots), max(dots)

        def overlap(proj1, proj2):
            return not (proj1[1] < proj2[0] or proj2[1] < proj1[0])

        # 获取两个多边形的所有投影轴
        axes = get_axes(poly1) + get_axes(poly2)

        # 对每个轴进行投影检测
        for axis in axes:
            proj1 = project_polygon(poly1, axis)
            proj2 = project_polygon(poly2, axis)
            if not overlap(proj1, proj2):
                return False
        return True

    def _transform_footprint(self, footprint, pose):
        """将footprint根据pose进行变换"""
        x, y, _, _, _, _, w = pose
        yaw = self._quaternion_to_yaw(pose[3], pose[4], pose[5], pose[6])
        cos_yaw = np.cos(yaw)
        sin_yaw = np.sin(yaw)
        transformed = []
        for point in footprint:
            px, py = point
            # 旋转
            rx = px * cos_yaw - py * sin_yaw
            ry = px * sin_yaw + py * cos_yaw
            # 平移
            tx = rx + x
            ty = ry + y
            transformed.append([tx, ty])
        return transformed

    def _quaternion_to_yaw(self, x, y, z, w):
        """从四元数计算yaw角"""
        import math
        return math.atan2(2*(w*z + x*y), 1 - 2*(y*y + z*z))

    def generate_constrained_scene(self):
        """Generate a complete scene with poses satisfying worldmodel.yaml constraints"""
        world_objects = {}
        required_counts = self.config['validation']['required_counts']
        
        # 生成世界模型格式的场景
        for obj_type, count in required_counts.items():
            for i in range(count):
                obj_id = f"{obj_type}_{i}"
                # 获取该物体的位姿限制
                limits = self.config['validation']['pose_limits'].get(obj_id)
                if not limits:
                    self.get_logger().warn(f"No pose limits found for {obj_id}")
                    return None
                
                # 生成随机位姿
                x = np.random.uniform(limits['x'][0], limits['x'][1])
                y = np.random.uniform(limits['y'][0], limits['y'][1])
                
                # 对于Cylinder，yaw始终为0
                if obj_type == "Cylinder":
                    yaw = 0.0
                else:
                    yaw = np.random.uniform(limits.get('yaw', [-np.pi, np.pi])[0], 
                                         limits.get('yaw', [-np.pi, np.pi])[1])
                
                # 生成四元数
                qx, qy, qz, qw = self._euler_to_quaternion(0, 0, yaw)
                pose = [x, y, 0.0, qx, qy, qz, qw]
                
                # 检查是否与已生成的物体重叠
                if self._check_footprint_collision(obj_type, pose, world_objects, obj_id):
                    return None
                
                world_objects[obj_id] = {
                    "category": obj_type,
                    "pose": pose,
                    "scale": self.get_default_scale(obj_type)
                }
        
        # 转换为视觉识别格式
        vision_objects = []
        for obj_id, obj_data in world_objects.items():
            vision_obj = {
                "category": self.obj_vision_map[obj_data["category"]],
                "pose": obj_data["pose"].copy(),
                "scale": obj_data["scale"].copy()
            }
            vision_objects.append(vision_obj)
        
        return vision_objects

    def get_default_scale(self, obj_type):
        """Get default scale for different object types"""
        scales = {
            "Tape": [0.05, 0.05, 0.05],
            # "Coffee": [0.05, 0.05, 0.05],
            # "Gloves": [0.05, 0.05, 0.05],
            # "Usb": [0.05, 0.05, 0.05],
            # "Milk": [0.05, 0.05, 0.05],
            # "Glue": [0.05, 0.05, 0.05],
            # "Bolts": [0.05, 0.05, 0.05],
        }
        return scales.get(obj_type, [0.1, 0.1, 0.1])

    def set_debug_object(self, obj_type, obj_id, pose=None):
        """Set up debug mode for a specific object"""
        self.debug_object = (obj_type, obj_id)
        obj_id_str = f"{obj_type}_{obj_id}"
        
        # 从默认场景获取当前姿态
        if pose is None and obj_id_str in self.world_model["default"]:
            pose = self.world_model["default"][obj_id_str]["pose"]
        elif pose is None:
            pose = self.generate_valid_pose(obj_type, obj_id)
            
        if pose:
            # 确保姿态数组是列表类型且长度正确
            self.debug_pose = list(pose)  # 转换为列表
            # 确保四元数部分是列表
            quat = list(quaternion_from_euler(0, 0, euler_from_quaternion(pose[3:])[2]))
            self.debug_pose = self.debug_pose[:3] + quat
            
        self.current_mode = "debug"
        
        # 更新world model和场景
        self.world_model["single_object"] = {
            obj_id: {
                "category": obj_type,
                "pose": self.debug_pose.copy(),  # 使用copy确保不会共享引用
                "scale": self.get_default_scale(obj_type)
            }
        }
        
        # 转换为视觉识别格式
        self.scenes["single_object"] = [{
            "category": self.obj_vision_map[obj_type],
            "pose": self.debug_pose.copy(),  # 使用copy确保不会共享引用
            "scale": self.get_default_scale(obj_type)
        }]
        self.current_scene = "single_object"

    def update_debug_pose(self, x=None, y=None, yaw=None):
        """Update the pose of the debug object"""
        if not self.debug_pose:
            return False
            
        if x is not None:
            self.debug_pose[0] = x
        if y is not None:
            self.debug_pose[1] = y
        if yaw is not None:
            quat = quaternion_from_euler(0, 0, yaw)
            self.debug_pose[3:] = quat
            
        # 更新世界模型
        obj_type, obj_id = self.debug_object
        obj_id_str = f"{obj_type}_{obj_id}"
        self.world_model["single_object"][obj_id_str]["pose"] = self.debug_pose
        
        # 更新视觉场景
        self.scenes["single_object"][0]["pose"] = self.debug_pose
        return True

    def start_batch_test(self, num_tests=10, test_file=None, start_index=0, single_index=None):
        """Start a batch testing session with more flexible options
        
        Args:
            num_tests (int): Number of tests to run if generating new data
            test_file (str): Path to test file if loading from file
            start_index (int): Starting test index
            single_index (int): If set, only run this specific test
        """
        self.current_mode = "batch"  # Set mode before doing anything else
        self.batch_test_params = {
            "num_tests": num_tests,
            "current_test": 0,  # 当前测试的实际序号
            "start_index": start_index,  # 开始输出的序号
            "results": [],
            "test_file": test_file,
            "single_index": single_index,
            "test_data": [],
            "is_ready": False,  # 标记是否已经准备好数据
            "total_tests": 0,   # 总共执行的测试数量
            "complete_cycles": 0  # 完整的循环次数
        }
        
        if test_file:
            try:
                with open(test_file, 'r') as f:
                    self.batch_test_params["test_data"] = json.load(f)
                self.batch_test_params["num_tests"] = len(self.batch_test_params["test_data"])
                print(f"Loaded {self.batch_test_params['num_tests']} tests from {test_file}")
                print("Use 'batch start' to begin testing")
                return True
            except Exception as e:
                print(f"Error loading test file: {e}")
                self.current_mode = "predefined"
                return False
        else:
            # 生成测试数据但不立即开始
            test_data = []
            attempts = 0
            
            while len(test_data) < num_tests:
                attempts += 1
                print(f"Generating test case {len(test_data)+1}/{num_tests} (attempt {attempts})")
                scene_data = self.generate_constrained_scene()
                if scene_data:
                    test_data.append(scene_data)
                    print(f"Generated test case {len(test_data)} with {len(scene_data)} objects")
                else:
                    print(f"Failed to generate test case {len(test_data)+1}, retrying...")
            
            self.batch_test_params["test_data"] = test_data
            self.batch_test_params["num_tests"] = len(test_data)
            print(f"Generated {len(test_data)} valid test cases")
            print("Use 'batch start' to begin testing (will loop until 'batch stop' is called)")
            return True

    def get_next_batch_scene(self):
        """Get the next scene for batch testing"""
        if self.current_mode != "batch":
            return None
            
        params = self.batch_test_params
        
        # 如果还没有开始输出，返回None
        if not params.get("is_ready", False):
            return None
            
        current = params["current_test"]
        start_index = params.get("start_index", 0)
        
        # 检查是否应该停止测试
        if params["single_index"] is not None:
            if current > params["single_index"]:
                self.current_mode = "predefined"
                print("\nBatch test completed")
                return None
        elif current >= params["num_tests"]:
            # 到达末尾时重新开始，而不是停止
            params["current_test"] = 0
            current = 0
            params["complete_cycles"] += 1
            print(f"\nCompleted cycle #{params['complete_cycles']}")
            
        # 获取场景数据
        scene_data = params["test_data"][current]
        
        # 显示当前测试序号（从start_index开始计数）
        print(f"\nExecuting test #{start_index + current}")
        
        params["current_test"] += 1
        params["total_tests"] += 1
        return scene_data

    # def add_noise_to_pose(self, pose_array, position_noise=0.005, orientation_noise=0.01):
    def add_noise_to_pose(self, pose_array, position_noise=0.00000005, orientation_noise=0.0000001):

        """Add random noise to pose data and convert to Pose message"""
        noisy_pose = np.array(pose_array, dtype=np.float64)
        noisy_pose[:3] += np.random.normal(0, position_noise, 3)
        noisy_pose[3:] += np.random.normal(0, orientation_noise, 4)
        quat_norm = np.linalg.norm(noisy_pose[3:])
        noisy_pose[3:] /= quat_norm
        
        # 创建Pose消息
        pose_msg = Pose()
        pose_msg.position = Point(x=float(noisy_pose[0]), 
                                y=float(noisy_pose[1]), 
                                z=float(noisy_pose[2]))
        pose_msg.orientation = Quaternion(x=float(noisy_pose[3]),
                                        y=float(noisy_pose[4]),
                                        z=float(noisy_pose[5]),
                                        w=float(noisy_pose[6]))
        return pose_msg

    def object_detect_callback(self, request, response):
        """Service callback for object detection requests"""
        if self.current_mode == "constrained":
            scene_data = self.generate_constrained_scene()
        elif self.current_mode == "batch":
            scene_data = self.get_next_batch_scene()
            if not scene_data:  # Batch testing finished
                scene_data = self.scenes[self.current_scene]
        else:
            if self.current_mode == "debug":
                # 在调试模式下，基于默认场景创建当前场景
                world_objects = self.world_model["default"].copy()
                
                # 更新被调试物体的姿态
                obj_type, obj_id = self.debug_object
                obj_id_str = f"{obj_type}_{obj_id}"
                world_objects[obj_id_str]["pose"] = self.debug_pose
                
                # 转换为视觉识别格式
                scene_data = []
                for obj_id, obj_data in world_objects.items():
                    vision_obj = {
                        "category": self.obj_vision_map[obj_data["category"]],
                        "pose": obj_data["pose"].copy(),
                    }
                    if "scale" in obj_data:
                        vision_obj["scale"] = obj_data["scale"].copy()
                    scene_data.append(vision_obj)
            else:
                scene_data = self.scenes[self.current_scene]

        response.success = True
        response.objects = []
        
        # 根据request.max_num限制返回的物体数量
        for obj in scene_data[:request.max_num]:
            obj_msg = Object()
            obj_msg.category = obj["category"]
            obj_msg.pose = self.add_noise_to_pose(obj["pose"])
            if "scale" in obj:
                scale = Vector3()
                scale.x = float(obj["scale"][0])
                scale.y = float(obj["scale"][1])
                scale.z = float(obj["scale"][2])
                obj_msg.scale = scale
            response.objects.append(obj_msg)
            
        return response

class MockServerShell(cmd.Cmd):
    """Interactive shell for controlling the mock vision server"""
    intro = 'Welcome to the mock vision server shell. Type help or ? to list commands.\n'
    prompt = '(mock_vision) '

    def __init__(self, server):
        super().__init__()
        self.server = server
        self._last_completions = []
        
        # 定义所有可用的命令和子命令
        self._commands = {
            'debug': ['off'] + list(self.server.obj_vision_map.keys()),
            'batch': ['generate', 'load', 'save', 'start', 'single', 'status', 'stop', 'off'],
            'pose': ['x', 'y', 'yaw', 'random'],
            'scene': list(self.server.scenes.keys()),
            'mode': ['predefined', 'constrained', 'debug', 'batch'],
            'help': [],
            'status': [],
            'limits': [],
            'quit': []
        }
        
    def completedefault(self, text, line, begidx, endidx):
        """Default completion handler"""
        return []
        
    def completenames(self, text, *ignored):
        """Complete primary command names"""
        return [name for name in self._commands.keys() if name.startswith(text)]
        
    def _get_command_from_line(self, line):
        """Extract the main command from a line"""
        return line.strip().split()[0] if line.strip() else ''
        
    def _get_subcommand_from_line(self, line):
        """Extract the subcommand from a line"""
        parts = line.strip().split()
        return parts[1] if len(parts) > 1 else ''
        
    def complete_debug(self, text, line, begidx, endidx):
        """Complete debug command arguments"""
        args = line[begidx:endidx].split()
        current_word = args[-1] if args else ''
        words = line.split()
        
        if len(words) <= 2:  # completing object type
            return [cmd for cmd in self._commands['debug'] if cmd.startswith(text)]
        elif len(words) == 3 and words[1] not in ['off']:  # completing object id
            # 返回可用的对象ID（0-2）
            return [str(i) for i in range(3) if str(i).startswith(text)]
        return []
        
    def complete_batch(self, text, line, begidx, endidx):
        """Complete batch command arguments"""
        words = line.split()
        if len(words) <= 2:
            return [cmd for cmd in self._commands['batch'] if cmd.startswith(text)]
        elif len(words) == 3 and words[1] == 'load':
            # 可以添加文件补全功能
            return []
        return []
        
    def complete_pose(self, text, line, begidx, endidx):
        """Complete pose command arguments"""
        words = line.split()
        if len(words) <= 2:
            return [cmd for cmd in self._commands['pose'] if cmd.startswith(text)]
        elif len(words) == 3 and words[1] == 'random':
            return [cmd for cmd in ['x', 'y', 'yaw'] if cmd.startswith(text)]
        return []
        
    def complete_scene(self, text, line, begidx, endidx):
        """Complete scene command arguments"""
        return [scene for scene in self._commands['scene'] if scene.startswith(text)]
        
    def complete_mode(self, text, line, begidx, endidx):
        """Complete mode command arguments"""
        return [mode for mode in self._commands['mode'] if mode.startswith(text)]
        
    def get_prompt(self):
        """Generate the prompt based on current mode"""
        mode = self.server.current_mode
        if mode == "debug":
            obj_type, obj_id = self.server.debug_object
            return f"(debug:{obj_type}_{obj_id}) "
        elif mode == "batch":
            if self.server.batch_test_params.get("is_ready", False):
                return "(batch:running) "
            else:
                return "(batch:ready) "
        else:
            return "(normal) "
            
    def precmd(self, line):
        """Update prompt before executing command"""
        self.prompt = self.get_prompt()
        return line

    def do_mode(self, arg):
        """
        Change the current mode of the server
        Usage: mode [predefined|constrained|debug|batch]
        """
        modes = ['predefined', 'constrained', 'debug', 'batch']
        if not arg or arg not in modes:
            print(f"Current mode: {self.server.current_mode}")
            print(f"Available modes: {', '.join(modes)}")
            return
        
        self.server.current_mode = arg
        print(f"Changed to {arg} mode")

    def do_scene(self, arg):
        """
        List available scenes or switch to a specific scene
        Usage: scene [scene_name]
        """
        if not arg:
            print(f"Current scene: {self.server.current_scene}")
            print(f"Available scenes: {', '.join(self.server.scenes.keys())}")
            return
            
        if arg in self.server.scenes:
            self.server.current_scene = arg
            print(f"Switched to scene: {arg}")
        else:
            print(f"Scene {arg} not found")

    def do_debug(self, arg):
        """
        Enter debug mode for a specific object
        Usage: 
            debug <object_type> <object_id>  - Start debugging an object
            debug off                        - Exit debug mode
        Example: debug Bridge 0
        
        Note: You can switch to debug another object directly without exiting debug mode
        """
        args = arg.split()
        if not args:
            print(self.do_debug.__doc__)
            return
            
        if args[0].lower() == 'off':
            if self.server.current_mode == "debug":
                self.server.current_mode = "predefined"
                self.server.debug_object = None
                self.server.debug_pose = None
                print("Exited debug mode")
                self.prompt = self.get_prompt()  # 立即更新提示符
            else:
                print("Not in debug mode")
            return
            
        try:
            obj_type, obj_id = args[0], int(args[1])
            obj_id_str = f"{obj_type}_{obj_id}"
            
            # 检查物体是否存在于world model中
            if obj_id_str not in self.server.world_model["default"]:
                print(f"Error: {obj_id_str} not found in world model")
                print("Available objects:")
                for obj_id in self.server.world_model["default"].keys():
                    print(f"  {obj_id}")
                return
                
            # 如果已经在调试这个物体，不需要重新设置
            if self.server.debug_object == (obj_type, obj_id):
                print(f"Already debugging {obj_id_str}")
                return
                
            # 如果之前在调试其他物体，提示切换
            if self.server.current_mode == "debug":
                prev_type, prev_id = self.server.debug_object
                print(f"Switching from {prev_type}_{prev_id} to {obj_id_str}")
            
            # 从默认场景复制当前物体的姿态
            current_pose = self.server.world_model["default"][obj_id_str]["pose"]
            self.server.debug_object = (obj_type, obj_id)
            self.server.debug_pose = current_pose.copy()
            self.server.current_mode = "debug"
            print(f"Debug mode set for {obj_type}_{obj_id}")
            self._print_debug_limits(obj_type, obj_id)
            self.prompt = self.get_prompt()  # 立即更新提示符
        except (ValueError, IndexError):
            print("Invalid format. Usage: debug <object_type> <object_id>")
            print("Example: debug Bridge 0")

    def _print_debug_limits(self, obj_type, obj_id):
        """Print the valid ranges for the object being debugged"""
        limits = self.server.config['validation']['pose_limits'].get(f"{obj_type}_{obj_id}")
        if limits:
            print("\nValid ranges:")
            print(f"x: {limits['x']}")
            print(f"y: {limits['y']}")
            if 'yaw' in limits:
                print(f"yaw: {limits['yaw']}")

    def do_pose(self, arg):
        """
        Update pose in debug mode
        Usage: 
            pose                    - Show current pose
            pose <x> <y> <yaw>     - Set all components at once
            pose x <value>         - Set x component
            pose y <value>         - Set y component
            pose yaw <value>       - Set yaw component
            pose random            - Randomize all components within limits
            pose random x          - Randomize only x component
            pose random y          - Randomize only y component
            pose random yaw        - Randomize only yaw component
        """
        if self.server.current_mode != "debug":
            print("Must be in debug mode to update pose")
            return
            
        if not arg:
            if self.server.debug_pose:
                yaw = euler_from_quaternion(self.server.debug_pose[3:])[2]
                print(f"Current pose: x={self.server.debug_pose[0]:.3f} y={self.server.debug_pose[1]:.3f} yaw={yaw:.3f}")
            return
            
        args = arg.split()
        try:
            # Handle random commands
            if args[0] == "random":
                if len(args) == 1:
                    # Randomize all components
                    obj_type, obj_id = self.server.debug_object
                    new_pose = self.server.generate_valid_pose(obj_type, obj_id)
                    if new_pose:
                        # 确保保持原始z值和四元数结构
                        z = self.server.debug_pose[2]
                        self.server.debug_pose = [new_pose[0], new_pose[1], z] + new_pose[3:]
                        print(f"Randomized all components within limits")
                elif len(args) == 2:
                    # Randomize specific component
                    obj_type, obj_id = self.server.debug_object
                    limits = self.server.config['validation']['pose_limits'].get(f"{obj_type}_{obj_id}")
                    if not limits:
                        print(f"No limits found for {obj_type}_{obj_id}")
                        return
                        
                    comp = args[1].lower()
                    if comp == 'x' and 'x' in limits:
                        self.server.debug_pose[0] = random.uniform(limits['x'][0], limits['x'][1])
                        print(f"Randomized x to {self.server.debug_pose[0]:.3f}")
                    elif comp == 'y' and 'y' in limits:
                        self.server.debug_pose[1] = random.uniform(limits['y'][0], limits['y'][1])
                        print(f"Randomized y to {self.server.debug_pose[1]:.3f}")
                    elif comp == 'yaw' and 'yaw' in limits:
                        yaw = random.uniform(limits['yaw'][0], limits['yaw'][1])
                        quat = quaternion_from_euler(0, 0, yaw)
                        self.server.debug_pose[3:] = list(quat)  # 确保转换为列表
                        print(f"Randomized yaw to {yaw:.3f}")
                    else:
                        print(f"Invalid or unsupported component: {comp}")
                else:
                    print("Invalid random command format")
                    
            # Handle direct value assignments
            elif len(args) == 2:
                # Single component update
                comp, value = args[0].lower(), float(args[1])
                if comp == 'x':
                    self.server.debug_pose[0] = value
                elif comp == 'y':
                    self.server.debug_pose[1] = value
                elif comp == 'yaw':
                    quat = quaternion_from_euler(0, 0, value)
                    self.server.debug_pose[3:] = list(quat)  # 确保转换为列表
                else:
                    print(f"Invalid component: {comp}")
                    return
                print(f"Updated {comp} to {value}")
                
            elif len(args) == 3:
                # Update all components at once
                x, y, yaw = map(float, args)
                quat = quaternion_from_euler(0, 0, yaw)
                self.server.debug_pose[:3] = [x, y, self.server.debug_pose[2]]
                self.server.debug_pose[3:] = list(quat)  # 确保转换为列表
                print(f"Updated pose to x={x:.3f} y={y:.3f} yaw={yaw:.3f}")
                
            else:
                print("Invalid command format")
                print(self.do_pose.__doc__)
                return
                
            # 更新场景中的姿态
            obj_type, obj_id = self.server.debug_object
            obj_id_str = f"{obj_type}_{obj_id}"
            
            # 更新world model中的姿态
            self.server.world_model["default"][obj_id_str]["pose"] = self.server.debug_pose.copy()
            
            # 更新视觉场景中的姿态
            # 找到对应的物体并更新
            vision_type = self.server.obj_vision_map[obj_type]
            for obj in self.server.scenes["default"]:
                if obj["category"] == vision_type:
                    obj["pose"] = self.server.debug_pose.copy()
                    break
            
        except ValueError as e:
            print(f"Invalid value format: {e}")
        except Exception as e:
            print(f"Error updating pose: {e}")
            
    def do_limits(self, arg):
        """
        Show current pose limits in debug mode
        Usage: limits
        """
        if self.server.current_mode != "debug":
            print("Must be in debug mode to show limits")
            return
            
        obj_type, obj_id = self.server.debug_object
        limits = self.server.config['validation']['pose_limits'].get(f"{obj_type}_{obj_id}")
        if limits:
            print(f"\nPose limits for {obj_type}_{obj_id}:")
            print(f"x: [{limits['x'][0]:.3f}, {limits['x'][1]:.3f}]")
            print(f"y: [{limits['y'][0]:.3f}, {limits['y'][1]:.3f}]")
            if 'yaw' in limits:
                print(f"yaw: [{limits['yaw'][0]:.3f}, {limits['yaw'][1]:.3f}]")
        else:
            print(f"No limits found for {obj_type}_{obj_id}")

    def do_batch(self, arg):
        """
        Batch testing control
        Usage: 
            batch                   - Enter batch mode
            batch generate <num>    - Generate number of tests
            batch load <filename>   - Load tests from json file
            batch save <filename>   - Save current batch tests to json file
            batch start [index]     - Start from specific test index
            batch single <index>    - Run single test at index
            batch status           - Show current batch test status
            batch stop             - Stop current batch execution
            batch off              - Exit batch mode
            
        Note: You must enter batch mode first before using other batch commands
        """
        args = arg.split()
        
        # If no arguments, treat as mode switch
        if not args:
            if self.server.current_mode != "batch":
                self.server.current_mode = "batch"
                print("Entered batch mode")
                self.prompt = self.get_prompt()
            else:
                print("Already in batch mode")
            return

        # Ensure we're in batch mode for all other commands
        if self.server.current_mode != "batch":
            print("Must enter batch mode first. Use 'batch' command without arguments.")
            return

        cmd = args[0].lower()
        
        if cmd == 'generate':
            try:
                num = int(args[1]) if len(args) > 1 else 10
                if self.server.start_batch_test(num_tests=num):
                    print(f"Generated {num} tests")
                    self.prompt = self.get_prompt()
            except ValueError:
                print("Number of tests must be a number")
                
        elif cmd == 'load':
            if len(args) < 2:
                print("Please specify a json file")
                return
            filename = args[1]
            if not filename.endswith('.json'):
                print("Test file must be a .json file")
                return
            if self.server.start_batch_test(test_file=filename):
                print(f"Loaded tests from {filename}")
                
        elif cmd == 'save':
            if len(args) < 2:
                print("Please specify a filename")
                return
                
            filename = args[1]
            if not filename.endswith('.json'):
                filename += '.json'
                
            try:
                if self.server.current_mode != "batch":
                    print("No batch test data available to save")
                    return
                    
                data = self.server.batch_test_params["test_data"]
                if not data:
                    print("No test data available to save")
                    return
                    
                with open(filename, 'w') as f:
                    json.dump(data, f, indent=2)
                print(f"Saved {len(data)} test cases to {filename}")
            except Exception as e:
                print(f"Error saving file: {e}")
                
        elif cmd == 'start':
            if self.server.current_mode != "batch":
                print("No batch test data available. Use 'generate' or 'load' first.")
                return
                
            try:
                start_index = int(args[1]) if len(args) > 1 else 0
                if start_index < 0:
                    print("Start index must be non-negative")
                    return
                    
                if start_index >= self.server.batch_test_params["num_tests"]:
                    print(f"Start index {start_index} exceeds number of tests {self.server.batch_test_params['num_tests']}")
                    return
                    
                self.server.batch_test_params["start_index"] = start_index
                self.server.batch_test_params["current_test"] = 0
                self.server.batch_test_params["is_ready"] = True
                self.server.batch_test_params["total_tests"] = 0
                self.server.batch_test_params["complete_cycles"] = 0
                print(f"\nStarting batch test from index {start_index}")
                print("Tests will loop continuously. Use 'batch stop' to stop testing.")
                self.prompt = self.get_prompt()  # 更新提示符
            except ValueError:
                print("Start index must be a number")
                
        elif cmd == 'single':
            try:
                index = int(args[1]) if len(args) > 1 else 0
                if index < 0 or index >= self.server.batch_test_params.get("num_tests", 0):
                    print(f"Invalid test index: {index}")
                    return
                if self.server.start_batch_test(single_index=index):
                    self.server.batch_test_params["is_ready"] = True
                    print(f"\nRunning single test at index {index}")
                    self.prompt = self.get_prompt()  # 更新提示符
            except ValueError:
                print("Test index must be a number")
                
        elif cmd == 'status':
            params = self.server.batch_test_params
            if self.server.current_mode == "batch":
                print(f"\nBatch test status:")
                print(f"Total tests: {params['num_tests']}")
                if params.get("is_ready", False):
                    current = params["current_test"]
                    start_index = params.get("start_index", 0)
                    print(f"Current test: #{start_index + (current % params['num_tests'])}")
                    print(f"Total tests executed: {params['total_tests']}")
                    print(f"Completed cycles: {params['complete_cycles']}")
                    if params['single_index'] is None:
                        print("Mode: Continuous loop (use 'batch stop' to stop)")
                    else:
                        print("Mode: Single test")
                else:
                    print("Test data ready but not started")
                if params['test_file']:
                    print(f"Test file: {params['test_file']}")
            else:
                print("No batch test running")
                
        elif cmd == 'stop':
            if self.server.current_mode == "batch":
                if not self.server.batch_test_params.get("is_ready", False):
                    print("No batch test running")
                    return
                    
                params = self.server.batch_test_params
                total_tests = params["total_tests"]
                cycles = params["complete_cycles"]
                params["is_ready"] = False  # 停止执行但保持在批处理模式
                print(f"\nBatch test stopped after {total_tests} tests ({cycles} complete cycles)")
                print("Use 'batch start' to resume testing or 'batch off' to exit batch mode")
                self.prompt = self.get_prompt()  # 更新提示符
            else:
                print("No batch test running")
                
        elif cmd == 'off':
            if self.server.current_mode == "batch":
                self.server.current_mode = "predefined"
                print("\nExited batch mode")
                self.prompt = self.get_prompt()  # 更新提示符
            else:
                print("Not in batch mode")
                
        else:
            print("Unknown batch command")
            print(self.do_batch.__doc__)

    def do_status(self, arg):
        """Show current server status"""
        print(f"Current mode: {self.server.current_mode}")
        print(f"Current scene: {self.server.current_scene}")
        
        # 显示 worldmodel 配置信息
        print("\nWorldmodel Configuration:")
        print("------------------------")
        if 'validation' in self.server.config:
            validation = self.server.config['validation']
            
            # 显示必需的物体数量
            if 'required_counts' in validation:
                print("\nRequired Object Counts:")
                for obj_type, count in validation['required_counts'].items():
                    print(f"  {obj_type}: {count}")
            
            # 显示姿态限制
            if 'pose_limits' in validation:
                print("\nPose Limits:")
                for obj_name, limits in validation['pose_limits'].items():
                    print(f"\n  {obj_name}:")
                    for axis, range_val in limits.items():
                        print(f"    {axis}: [{range_val[0]:.3f}, {range_val[1]:.3f}]")
        
        # 显示当前场景中的物体
        print("\nCurrent World Model Objects:")
        print("---------------------------")
        if self.server.current_mode == "debug":
            # 在调试模式下显示所有物体，并标记被调试的物体
            obj_type, obj_id = self.server.debug_object
            debug_obj_id = f"{obj_type}_{obj_id}"
            
            for obj_id, obj_data in self.server.world_model["default"].items():
                print(f"\n{obj_id}{'  [DEBUG]' if obj_id == debug_obj_id else ''}:")
                print(f"  Category: {obj_data['category']}")
                if obj_id == debug_obj_id:
                    # 显示当前被调试物体的实际姿态
                    x, y, z = self.server.debug_pose[:3]
                    yaw = euler_from_quaternion(self.server.debug_pose[3:])[2]
                else:
                    x, y, z = obj_data['pose'][:3]
                    yaw = euler_from_quaternion(obj_data['pose'][3:])[2]
                print(f"  Pose: x={x:.3f}, y={y:.3f}, z={z:.3f}, yaw={yaw:.3f}")
                if "scale" in obj_data:
                    scale = obj_data['scale']
                    print(f"  Scale: [{scale[0]:.3f}, {scale[1]:.3f}, {scale[2]:.3f}]")
        else:
            current_objects = self.server.world_model[self.server.current_scene]
            for obj_id, obj_data in current_objects.items():
                print(f"\n{obj_id}:")
                print(f"  Category: {obj_data['category']}")
                x, y, z = obj_data['pose'][:3]
                yaw = euler_from_quaternion(obj_data['pose'][3:])[2]
                print(f"  Pose: x={x:.3f}, y={y:.3f}, z={z:.3f}, yaw={yaw:.3f}")
                if "scale" in obj_data:
                    scale = obj_data['scale']
                    print(f"  Scale: [{scale[0]:.3f}, {scale[1]:.3f}, {scale[2]:.3f}]")
        
        # 如果在批量测试模式，显示测试进度
        if self.server.current_mode == "batch":
            params = self.server.batch_test_params
            print("\nBatch Test Status:")
            print(f"Current test: {params['current_test']}/{params['num_tests']}")
            if params['test_file']:
                print(f"Test file: {params['test_file']}")
            if params['single_index'] is not None:
                print(f"Running single test: {params['single_index']}")
        
        # 如果在调试模式，显示当前调试对象
        elif self.server.current_mode == "debug":
            print("\nDebug Mode Status:")
            print(f"Debug object: {self.server.debug_object}")
            if self.server.debug_pose:
                x, y = self.server.debug_pose[:2]
                yaw = euler_from_quaternion(self.server.debug_pose[3:])[2]
                print(f"Current pose: x={x:.3f} y={y:.3f} yaw={yaw:.3f}")

    def do_help(self, arg):
        """
        Show help information about commands
        Usage: help [command]
        """
        if not arg:
            print("""
Mock Vision Server Help
======================

Available Commands:
------------------
1. debug     - Enter debug mode for a specific object
2. pose      - Control object pose in debug mode
3. limits    - Show pose limits in debug mode
4. batch     - Control batch testing and data management
5. scene     - Manage scenes
6. status    - Show current server status
7. quit      - Exit the server

For detailed help on any command, type: help <command>
""")
            return

        cmd = arg.lower()
        if cmd == "batch":
            print("""
Batch Testing and Data Management
===============================
Control batch testing operations and manage test data.

Commands:
    batch generate <num>    - Generate number of tests
    batch load <filename>   - Load tests from json file
    batch save <filename>   - Save current batch tests to json file
    batch start [index]     - Start from specific test index
    batch single <index>    - Run single test
    batch status           - Show batch test status
    batch stop             - Stop current batch test

Examples:
    batch generate 20           # Generate 20 tests
    batch load tests.json       # Load tests from file
    batch save tests.json       # Save current tests to file
    batch start 5              # Start from test #5
    batch single 3             # Run only test #3
    batch status               # Check current status
    batch stop                # Stop the test
""")
        else:
            super().do_help(arg)

def main(args=None):
    rclpy.init(args=args)
    server = VisionMockServer()
    
    # 在单独的线程中运行ROS节点
    ros_thread = threading.Thread(target=rclpy.spin, args=(server,), daemon=True)
    ros_thread.start()
    
    # 启动交互式shell
    try:
        shell = MockServerShell(server)
        shell.cmdloop()
    except KeyboardInterrupt:
        print("\nShutting down...")
    finally:
        server.destroy_node()
        rclpy.shutdown()
        ros_thread.join(timeout=1.0)

if __name__ == '__main__':
    main() 
