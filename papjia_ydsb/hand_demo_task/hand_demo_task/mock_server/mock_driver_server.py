#!/usr/bin/python3

import rclpy
from rclpy.node import Node
from rclpy.action import ActionServer
from rclpy.callback_groups import ReentrantCallbackGroup
import time
import threading

from papjia_control_msgs.action import GripperCommand



class MockActionServer(Node):
    """模拟Action和Service服务器"""
    
    def __init__(self, node_name="mock_action_server"):
        super().__init__(node_name)
        
        # 使用ReentrantCallbackGroup允许并发处理请求
        self.callback_group = ReentrantCallbackGroup()
        
        # 初始化Gripper Action服务器
        self.gripper_servers = {
            "left_p_gripper": ActionServer(
                self,
                GripperCommand,
                "/szyj_driver/left_hand_p_controller/gripper_cmd",
                self.execute_gripper_callback,
                callback_group=self.callback_group
            ),
            "left_r_gripper": ActionServer(
                self,
                GripperCommand,
                "/szyj_driver/left_hand_r_controller/gripper_cmd",
                self.execute_gripper_callback,
                callback_group=self.callback_group
            ),
            "right_gripper": ActionServer(
                self,
                GripperCommand,
                "/szyj_driver/right_hand_controller/gripper_cmd",
                self.execute_gripper_callback,
                callback_group=self.callback_group
            ),
        }

        
        self.get_logger().info('模拟服务器已启动，等待客户端请求...')

    def execute_gripper_callback(self, goal_handle):
        """处理夹爪命令"""
        goal = goal_handle.request
        
        # 简化方法：直接使用字典映射来识别服务器
        server_id = id(goal_handle)
        server_name = "未知夹爪"
        
        # 记录服务器ID和名称的映射
        if not hasattr(self, '_gripper_server_map'):
            self._gripper_server_map = {}
        
        # 如果这是一个新的服务器ID，记录它
        if server_id not in self._gripper_server_map:
            # 轮流分配名称
            names = list(self.gripper_servers.keys())
            idx = len(self._gripper_server_map) % len(names)
            self._gripper_server_map[server_id] = names[idx]
        
        server_name = self._gripper_server_map.get(server_id, "未知夹爪")
        
        # 打印目标对象的所有属性，帮助调试
        self.get_logger().info(f'目标对象属性: {dir(goal)}')
        
        # 尝试获取属性，如果不存在则使用默认值
        position = getattr(goal, 'goal_position', 0)
        force = getattr(goal, 'force', 0)
        velocity = getattr(goal, 'velocity', 0)
        
        self.get_logger().info(
            f'收到{server_name}夹爪命令: '
            f'位置={position}, 力度={force}, 速度={velocity}'
        )
        
        # 创建结果和反馈消息
        feedback_msg = GripperCommand.Feedback()
        
        # 打印反馈消息对象的所有属性，帮助调试
        self.get_logger().info(f'反馈消息对象属性: {dir(feedback_msg)}')
        
        result = GripperCommand.Result()
        
        # 模拟执行过程
        total_steps = 1
        for i in range(total_steps + 1):
            if goal_handle.is_cancel_requested:
                goal_handle.canceled()
                self.get_logger().info(f'{server_name}夹爪命令被取消')
                return
                
            # 计算当前位置 - 确保是整数
            current_position = int((position * i) / total_steps)
            
            # 发送反馈 - 使用正确的属性名称和类型
            try:
                feedback_msg.current_position = current_position
            except (AttributeError, AssertionError) as e:
                self.get_logger().warn(f'设置反馈消息属性失败: {e}')
                # 如果设置失败，跳过本次反馈但继续执行
                continue
                
            goal_handle.publish_feedback(feedback_msg)
            
            self.get_logger().info(f'{server_name}夹爪执行进度: {i*10}%, 当前位置={current_position}')
            time.sleep(0.2)  # 模拟执行时间
        
        # 设置结果
        self.get_logger().info(f'结果对象属性: {dir(result)}')
        
        # 尝试设置正确的结果属性
        try:
            result.result_position = int(position)
        except (AttributeError, AssertionError) as e:
            try:
                result.position = int(position)
            except (AttributeError, AssertionError) as e2:
                try:
                    result.current_position = int(position)
                except (AttributeError, AssertionError) as e3:
                    self.get_logger().warn(f'无法设置结果消息的位置属性: {e3}')
        
        # 设置reached_goal属性（如果存在）
        try:
            result.reached_goal = True
        except (AttributeError, AssertionError):
            pass
        
        # 设置stalled属性（如果存在）
        try:
            result.stalled = False
        except (AttributeError, AssertionError):
            pass
        
        goal_handle.succeed()
        
        self.get_logger().info(f'{server_name}夹爪命令执行完成')
        return result


def main(args=None):
    rclpy.init(args=args)
    
    server = MockActionServer()
    
    # 使用多线程执行，以便处理多个并发请求
    executor = rclpy.executors.MultiThreadedExecutor()
    executor.add_node(server)
    
    executor_thread = threading.Thread(target=executor.spin, daemon=True)
    executor_thread.start()
    
    try:
        server.get_logger().info('模拟服务器运行中...')
        while rclpy.ok():
            time.sleep(1.0)
    except KeyboardInterrupt:
        pass
    finally:
        executor.shutdown()
        server.destroy_node()
        rclpy.shutdown()
        executor_thread.join()


if __name__ == '__main__':
    main() 