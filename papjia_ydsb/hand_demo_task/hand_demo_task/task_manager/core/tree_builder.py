import sys
from collections import defaultdict

from papjia_skill.executor import <PERSON><PERSON><PERSON><PERSON><PERSON>killExecutor
from papjia_skill.btree import BehaviorTree, BehaviorRoot, Sequence, Parallel
from papjia_skill.atom.papjia_delay_async import PapjiaDelayAsync
from papjia_skill.atom.pause_until_signal import PauseUntilSignal
from papjia_skill.atom.update_worldmodel import UpdateWorldmodel
# from hand_demo_task.data_manager.data_manipulation import DataManipulation
from hand_demo_task.driver_manager.driver_command import (
    gripper_command,
)
from hand_demo_task.task_manager.core.operation import Operation


class TaskTreeBuilder(object):
    def __init__(self, executor, debug=False):
        self.atom_trees = {}
        self.executor = executor
        self.debug = debug

    def _parse_element(self, element):
        """全能解析函数（处理并行/顺序/原子动作）"""
        # 处理并行结构
        if isinstance(element, tuple) and element[0] == 'parallel':
            parallel_node = Parallel("PARALLEL")
            for id, branch in enumerate(element[1]):
                seq_node = Sequence(f"BRANCH_{id+1}")
                for sub_element in branch:
                    # 递归解析子元素（支持嵌套并行）
                    child_node = self._parse_element(sub_element)
                    seq_node.add_child(child_node)
                parallel_node.add_child(seq_node)
            return parallel_node

        if isinstance(element, tuple) and element[0] == 'delay':
            seq_node = Sequence("DELAY")
            seq_node.add_child(PapjiaDelayAsync(delay_duration=float(element[1])))
            return seq_node

        # 处理原子动作
        elif isinstance(element, tuple) and len(element) == 2:
            action_type, action_name = element
            if action_type not in self.atom_trees.keys():
                raise KeyError(f"动作类型: {action_type} 未注册")
            if action_name not in self.atom_trees[action_type].keys():
                raise KeyError(f"未定义的动作: {action_type}/{action_name}")
            return self.atom_trees[action_type][action_name]
        
        # 错误处理
        raise ValueError(f"无效的任务元素格式: {element}")
    
    def execute_task_tree(self, action_name, task_tree):
        if self.debug:
            input("继续执行? ")
        
        # 构建完整的树结构
        tree = BehaviorTree(action_name)
        tree.add_child(task_tree)
        root = BehaviorRoot()
        root.add_child(tree)
        execute_result = self.executor.execute_tree(root.to_str(), str(action_name))
        if not execute_result.success:
            return False
        return True
    
    def generate_task_trees(self, task_sequence):
        task_trees = []
        for element in task_sequence:
            node = self._parse_element(element)
            if node:
                task_trees.append(node)
        return task_trees
    
    def run_step(self, task_sequence):
        task_trees = self.generate_task_trees(task_sequence)
        for action_no, task_tree in enumerate(task_trees):
            action_type, action_name = task_sequence[action_no]
            if self.execute_task_tree(action_name, task_tree) is False:
                return False
        return True


class ActionTreeBuilder(Operation):
    def __init__(self, object_name):
        super().__init__(object_name)
        self.task_type = object_name
        self.object_name = None
        self.atom_trees = None
        self.actions = self.register_actions()
        # self.data_manipulation = DataManipulation()

    
    def update(self, worldmodel):
        self.update_pick_place_waypoint(worldmodel)
        self.update_atom_trees()
        # print(f"✅ update {self.object_name} {self.idx_x} {self.idx_y}")

    def update_atom_trees(self):
        atom_trees = {}
        for action_name, _ in self.actions.items():
            tree = self._build_action_tree(action_name)
            atom_trees[action_name] = tree
        self.atom_trees = atom_trees

    def _build_action_tree(self, action_name):
        action_list = self.actions[action_name]
        tree = Sequence(action_name)
        i = 0
        while i < len(action_list):
            element = action_list[i]
            
            if len(element) == 3 and element[0] == 'parallel':
                # 处理并行声明
                markers = element[1]
                parallel_name = element[2]
                parallel_node = Parallel(parallel_name)
                marker_instructions = defaultdict(list)
                j = i + 1
                while j < len(action_list):
                    next_element = action_list[j]
                    if len(next_element) == 3 and next_element[0] != 'parallel':
                        marker, cmd, arg = next_element
                        if marker not in markers:
                            raise ValueError(f"标记 '{marker}' 未在并行声明 '{parallel_name}' 的标记列表 {markers} 中")
                        marker_instructions[marker].append((cmd, arg))
                        j += 1
                    else:
                        break
                # 为每个标记创建Sequence并添加到Parallel
                for marker in markers:
                    cmds_args = marker_instructions.get(marker, [])
                    seq = Sequence(f"{parallel_name}_{marker}")
                    
                    # 新增连续指令合并逻辑
                    cmd_index = 0
                    while cmd_index < len(cmds_args):
                        cmd, arg = cmds_args[cmd_index]
                        if cmd == 'arm':
                            # 收集连续的arm指令
                            arm_args = [arg]
                            cmd_index += 1
                            while cmd_index < len(cmds_args):
                                next_cmd, next_arg = cmds_args[cmd_index]
                                if next_cmd == 'arm':
                                    arm_args.append(next_arg)
                                    cmd_index += 1
                                else:
                                    break
                            # 生成合并后的移动序列
                            arm_node = self.create_arm_move_sequence(
                                f"{marker}_arm_sequence", 
                                arm_args
                            )
                            seq.add_child(arm_node)
                        else:
                            # 非arm指令直接添加
                            node = self._create_command_node(cmd, arg)
                            seq.add_child(node)
                            cmd_index += 1
                    
                    parallel_node.add_child(seq)
                tree.add_child(parallel_node)
                i = j
            else:
                # 处理独立指令
                if len(element) != 2:
                    raise ValueError(f"无效的指令元组: {element}，应为两元素元组")
                cmd, arg = element
                if cmd == 'arm':
                    # 收集连续的arm指令
                    arm_args = [arg]
                    j = i + 1
                    while j < len(action_list):
                        next_element = action_list[j]
                        if len(next_element) == 2 and next_element[0] == 'arm':
                            arm_args.append(next_element[1])
                            j += 1
                        else:
                            break
                    # 生成arm移动序列
                    name = "_".join(arm_args)
                    arm_node = self.create_arm_move_sequence(name, arm_args)
                    tree.add_child(arm_node)
                    i = j
                else:
                    node = self._create_command_node(cmd, arg)
                    tree.add_child(node)
                    i += 1
        return tree

    def _create_command_node(self, cmd, arg):
        """根据指令类型创建对应的行为节点"""
        if cmd == 'arm':
            return self.create_arm_move_sequence(cmd, [arg])
        elif cmd == 'gripper':
            return gripper_command(arg)
        elif cmd == 'delay':
            return PapjiaDelayAsync(delay_duration=float(arg))
        elif cmd == 'pause':
            return PauseUntilSignal(service_name="pause_until_signal", signal_id='', timeout=0.0)
         
        # elif cmd == 'update_slot':
        #     name = self.object_name + "_" + str(self.idx_x+1) + "_" + str(self.idx_y+1)
        #     assert arg in ["有_未使用", "有_已使用", "有_使用中"]
        #     record = {"status": arg}
        #     return self.data_manipulation.generate_sequence_update_slot_by_name(name, record)
        else:
            raise ValueError(f"未知指令类型: {cmd}")

    def register_actions(self):
        raise NotImplementedError("子类必须实现 register_actions 方法")

    def update_pick_place_waypoint(self):
        raise NotImplementedError("子类必须实现 update_pick_place_waypoint 方法")
