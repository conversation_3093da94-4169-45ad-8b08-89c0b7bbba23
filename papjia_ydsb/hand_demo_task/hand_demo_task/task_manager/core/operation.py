#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from hand_demo_task.task_manager.core.arm_move import get_waypoint_from_config, get_plan_and_execute_waypoints_sequence

class Operation(object):
    def __init__(self, object_name):
        self.waypoint_file_path = os.getenv('WAYPOINT_CONFIG_FILEPATH')
        with open(self.waypoint_file_path, "r") as f:
            all_waypoint_configs = json.load(f)
        self.waypoint_configs = all_waypoint_configs[object_name]

    def update_waypoints(self, waypoints):
        for waypoint in waypoints:
            self.waypoint_configs[waypoint] = waypoints[waypoint]
    
    def get_waypoint_config(self, waypoint_name):
        return self.waypoint_configs[waypoint_name]
    
    # TODO action_name不需要了
    def create_arm_move_sequence(self, action_name,  waypoint_names: list[str]):
        waypoints = []
        for waypoint_name in waypoint_names:
            waypoint = get_waypoint_from_config(waypoint_name, self.waypoint_configs[waypoint_name])
            waypoints.append(waypoint)
        
        sequence = get_plan_and_execute_waypoints_sequence(waypoints)
        return sequence