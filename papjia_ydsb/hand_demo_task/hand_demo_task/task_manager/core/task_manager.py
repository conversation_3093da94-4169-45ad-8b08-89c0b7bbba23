import threading

from hand_demo_task.task_manager.models.task import Task, TaskStatus, InvalidStateError


class TaskManager:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if not cls._instance:
                cls._instance = super().__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self._current_task = None
        self._task_thread = None
        self._pause_flag = threading.Event()
        self._stop_flag = threading.Event()
        self._pause_flag.clear()
        self._stop_flag.clear()
        
    # 核心控制方法
    def _create_task(self, task : Task):
        """创建新任务（状态机重置）"""
        if self._current_task and self._current_task.status not in [
            TaskStatus.COMPLETED, TaskStatus.STOPPED, TaskStatus.FAILED
        ]:
            raise RuntimeError("存在未完成的任务")
            
        self._current_task = task
        return self._current_task
    
    def _get_current_task(self):
        """获取当前任务"""
        return self._current_task
    
    def _configure_task(self, params):
        """配置参数"""
        self._current_task.configure(params)