from papjia_skill.action_models import ActionModels

from hand_demo_task.task_manager.core.tree_builder import ActionTreeBuilder
import math

"""
"""

class CylinderOperation(ActionTreeBuilder):
    def __init__(self, obj_name):
        super().__init__(obj_name)
        self.object_name = "Cylinder"
        self.left_mani_offset = [0.03814, 0.05498, 0.18000, 173.74696, 62.02156, 70.19274]
        self.right_mani_offset = [-0.037833, 0.0905, 0.16700, 178.89852, 64.23435, 90.07378]
        # self.173.630, pitch=58.296, yaw=79.101
    def register_actions(self):
        actions = {
            '临时测试':
            [
                ('arm', '右臂-Cylinder-装配放置-撤退'),   
                ('arm', '右臂-初始点'), 
            ],
            '等待视觉结果确认':
            [
                ('pause', None),
            ],

            '左臂初始化':
            [
                ('arm', '左臂-初始点'),
                ('arm', '左臂-初始翻转点'),
                ('gripper', '左圆柱初始化'),
                # ('pause', None),
            ],
            '左臂去翻转':
            [
                ('arm', '左臂-初始翻转点'),
                ('gripper', '左圆柱初始化'),
                # ('pause', None),
            ],
            '左臂还原':
            [
                ('arm', '左臂-初始点'),
                # ('pause', None),
            ],
            '左臂从平面取件':
            [
                ('gripper', '左圆柱适当闭合'),
                ('arm', '左臂-Cylinder-平面抓取-预备'),
                ('arm', '左臂-Cylinder-平面抓取-就绪'),

                # ('gripper', '左圆柱适当闭合'),
                # ('gripper', '左圆柱抓取闭合'),
                ('pause', None),
                ('arm', '左臂-Cylinder-平面抓取-撤退'),
                # ('parallel', ['1','2'], "离开与狠狠的抓"),
                # ('1', 'arm', '左臂-Cylinder-平面抓取-撤退'),
                # ('2', 'gripper', '左圆柱抓取闭合'),
                # ('2', 'delay', 3.0),
                # ('2', 'gripper', '左圆柱抓取闭合'),
            ],
            '左臂装配':
            [
                ('arm', '左臂-Cylinder-装配放置-预备'),
                ('arm', '左臂-Cylinder-装配放置-就绪'),
                ('gripper', '左圆柱抓取放松'),
                # ('pause', None),
                ('arm', '左臂-Cylinder-装配放置-撤退'),
            ],
            '左臂从装配台取件':
            [
                ('gripper', '左圆柱适当闭合'),
                ('arm', '左臂-Cylinder-装配抓取-预备'),
                ('arm', '左臂-Cylinder-装配抓取-就绪'),
                ('gripper', '左圆柱抓取闭合'),
                # ('pause', None),
                ('arm', '左臂-Cylinder-装配抓取-撤退'),
            ],
            '左臂摆放':
            [
                ('arm', '左臂-Cylinder-平面放置-预备'),
                ('arm', '左臂-Cylinder-平面放置-就绪'),
                ('gripper', '左圆柱抓取放松'),
                # ('pause', None),
                ('arm', '左臂-Cylinder-平面放置-撤退'),
            ],
            '手爪测试':
            [
                # ('gripper', '右圆柱初始化'),
                # # ("pause", 2),
                # ('gripper', '右圆柱稍微闭合'),
                # # ("pause", 2),
                # ('gripper', '右圆柱适当闭合'),
                # # ("pause", 2),
                # ('gripper', '右圆柱抓取闭合'),
                # # ("pause", 2),
                # ('gripper', '右圆柱抓取放松'),
                # # ("pause", 2),
                # ('gripper', '右圆柱初始化'),
                # # ("pause", 2),
            ],

            '右臂初始化':
            [
                ('arm', '右臂-初始点'),
                ('gripper', '右圆柱初始化'),
                # ('pause', None)
            ],
            '右臂从平面取件':
            [
                ('arm', '右臂-Cylinder-平面抓取-预备'),
                ('gripper', '右圆柱稍微闭合'),
                ('arm', '右臂-Cylinder-平面抓取-就绪'),
                # ('gripper', '右圆柱适当闭合'),
                # ('gripper', '右圆柱抓取闭合'),
                ('pause', None),
                ('arm', '右臂-Cylinder-平面抓取-撤退'),
            ],
            '右臂装配':
            [
                ('arm', '右臂-Cylinder-装配放置-预备'),
                ('arm', '右臂-Cylinder-装配放置-就绪'),
                # ('pause', None),
                ('gripper', '右圆柱抓取放松'),
                ('arm', '右臂-Cylinder-装配放置-撤退'),
                ('gripper', '右圆柱初始化'),
            ],
            '右臂从装配台取件':
            [
                ('arm', '右臂-Cylinder-装配抓取-预备'),
                ('gripper', '右圆柱适当闭合'),
                ('arm', '右臂-Cylinder-装配抓取-就绪'),
                ('gripper', '右圆柱抓取闭合'),
                # ('pause', None),
                ('arm', '右臂-Cylinder-装配抓取-撤退'),
            ],
            '右臂摆放':
            [
                ('arm', '右臂-Cylinder-平面放置-预备'),
                ('arm', '右臂-Cylinder-平面放置-就绪'),
                ('gripper', '右圆柱抓取放松'),
                # ('pause', None),
                ('arm', '右臂-Cylinder-平面放置-撤退'),
                ('gripper', '右圆柱初始化'),
            ],
        }
        return actions

    def select_target(self, worldmodel):
        # for key in worldmodel['objects'].keys():
        #     if key.startswith('Cylinder'):
        #         return key
        return "Cylinder_0", "Cylinder_1"

    def update_pick_place_waypoint(self, worldmodel):
        print ("WORLD MODEL: ", worldmodel)
        id_l, id_r = self.select_target(worldmodel)

        try:
            target = worldmodel['objects'][id_l]
        except Exception as e:
            print ("ERROR: cannot find any Cylinder_0")
        
        # target["pose"][2] = 0
        target["pose"][3:] = [0,0,0,1]
        waypoint = self.get_waypoint_config("左臂-Cylinder-平面抓取-预备")
        combined_pose = apply_pose_offset(target["pose"][:], self.left_mani_offset)
        waypoint["pose"] = combined_pose[:]
        waypoint["pose"][2] = 0.4
        waypoint["frame_id"] = "arm_base"
        self.update_waypoints(waypoint)

        waypoint = self.get_waypoint_config("左臂-Cylinder-平面抓取-就绪")
        combined_pose = apply_pose_offset(target["pose"][:], self.left_mani_offset)
        waypoint["pose"] = combined_pose[:]
        waypoint["frame_id"] = "arm_base"
        self.update_waypoints(waypoint)

        waypoint = self.get_waypoint_config("左臂-Cylinder-平面抓取-撤退")
        combined_pose = apply_pose_offset(target["pose"][:], self.left_mani_offset)
        waypoint["pose"] = combined_pose[:]
        waypoint["pose"][2] = 0.4
        waypoint["frame_id"] = "arm_base"
        self.update_waypoints(waypoint)

        try:
            target = worldmodel['objects'][id_r]
        except Exception as e:
            print ("ERROR: cannot find any Cylinder_1")
        # target["pose"][2] = 0.1
        target["pose"][3:] = [0,0,0,1]

        waypoint = self.get_waypoint_config("右臂-Cylinder-平面抓取-预备")
        combined_pose = apply_pose_offset(target["pose"][:], self.right_mani_offset)
        waypoint["pose"] = combined_pose[:]
        waypoint["pose"][2] = 0.4
        waypoint["frame_id"] = "arm_base"
        self.update_waypoints(waypoint)

        waypoint = self.get_waypoint_config("右臂-Cylinder-平面抓取-就绪")
        combined_pose = apply_pose_offset(target["pose"][:], self.right_mani_offset)
        waypoint["pose"] = combined_pose[:]
        waypoint["frame_id"] = "arm_base"
        self.update_waypoints(waypoint)

        waypoint = self.get_waypoint_config("右臂-Cylinder-平面抓取-撤退")
        combined_pose = apply_pose_offset(target["pose"][:], self.right_mani_offset)
        waypoint["pose"] = combined_pose[:]
        waypoint["pose"][2] = 0.4
        waypoint["frame_id"] = "arm_base"
        self.update_waypoints(waypoint)

from tf_transformations import quaternion_matrix, translation_matrix, translation_from_matrix, quaternion_from_matrix, euler_matrix
def apply_pose_offset(target_pose, offset_pose):
    """应用位姿偏移量计算
    参数:
        target_pose (list): 基础位姿 [x, y, z, qx, qy, qz, qw]
        offset_pose (list): 相对偏移 [dx, dy, dz, roll, pitch, yaw](degree)
    返回:
        list: 组合后的新位姿 [x', y', z', qx', qy', qz', qw']
    """
    # 解包基础位姿
    base_trans = target_pose[0:3]
    base_quat = target_pose[3:7]
    
    # 解包偏移量
    offset_trans = offset_pose[0:3]
    offset_eular = [angle * math.pi / 180.0 for angle in offset_pose[3:]]  # 列表推导式逐个转换

    
    # 构建变换矩阵
    base_matrix = translation_matrix(base_trans) @ quaternion_matrix(base_quat)
    offset_matrix = translation_matrix(offset_trans) @ euler_matrix(offset_eular[0], offset_eular[1], offset_eular[2], axes='sxyz')
    
    # 组合变换 (注意矩阵相乘顺序)
    combined_matrix = base_matrix @ offset_matrix
    
    # 提取结果
    new_trans = translation_from_matrix(combined_matrix)
    new_quat = quaternion_from_matrix(combined_matrix)
    
    return [
        new_trans[0], new_trans[1], new_trans[2],
        new_quat[0], new_quat[1], new_quat[2], new_quat[3]
    ]

if __name__ == "__main__":
    ActionModels("/workspace/src/papjia_skill/config/action_model.yaml")
    action = CylinderOperation("test_tube")
    # print (action.atom_trees)
    print (action.get_waypoint_config("左臂-Cylinder-平面抓取-预备"))
    print (action.get_waypoint_config("右臂-Cylinder-平面抓取-预备"))