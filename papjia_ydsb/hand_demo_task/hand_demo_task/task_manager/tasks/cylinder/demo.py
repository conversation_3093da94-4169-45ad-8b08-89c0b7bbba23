import rclpy
import sys
import copy
from papjia_skill.action_models import ActionModels

from hand_demo_task.task_manager.models.task import Task
from hand_demo_task.task_manager.tasks.cylinder.cylinder_operation import CylinderOperation
from hand_demo_task.world_manager.data_manipulation import WorldModelManager

class CylinderDemo(Task):
    def __init__(self, task_name, debug=False, params=None):
        super().__init__(task_name, debug, params)
        self.operations["Cylinder"] = CylinderOperation("test_tube")
    
def main():
    task_name = "药液试管测试"
    task_sequence = [
        ("Cylinder", '等待视觉结果确认'),
        # ("Cylinder", '左臂还原'),
        ("Cylinder", '左臂初始化'),
        # ("Cylinder", '左臂从平面取件'),
        # ("Cylinder", '左臂装配'),
        ("Cylinder", '左臂从装配台取件'),
        ("Cylinder", '左臂摆放'),
        ("<PERSON>linder", '左臂去翻转'),
        ("<PERSON>lind<PERSON>", '左臂还原'),
        # ("Cylinder", '临时测试'),
        # ("Cylinder", '右臂初始化'),
        # ("Cylinder", '右臂从平面取件'),
        # ("Cylinder", '右臂装配'),
        # ("Cylinder", '右臂初始化'),
        # ("Cylinder", '右臂从装配台取件'),
        # ("Cylinder", '右臂摆放'),
        # ("Cylinder", '右臂初始化'),
    ]
    task = CylinderDemo(task_name, debug=True)

    world_demo_manager = WorldModelManager()
    result = world_demo_manager.update_model()
    if eval(result["success"])== False:
        raise Exception(result["message"])
    world_model = copy.deepcopy(world_demo_manager.world_model_data)
    task.update_worldmodel("Cylinder", world_model)
    result = task.run_step(task_sequence)
    if result is False:
        sys.exit(1)


if __name__ == "__main__":
    ActionModels("/workspace/src/papjia_skill/config/action_model.yaml")
    rclpy.init()
    main()
    rclpy.shutdown()
