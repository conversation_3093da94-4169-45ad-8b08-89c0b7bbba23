from papjia_skill.action_models import ActionModels

from hand_demo_task.task_manager.core.tree_builder import ActionTreeBuilder
import math


class BridgeOperation(ActionTreeBuilder):
    def __init__(self, obj_name):
        super().__init__(obj_name)
        self.object_name = "Bridge"          
        self.left_mani_offset = [0.05124, -0.06120, 0.14800, -173.50961, 59.42989, -83.81322]
        self.right_mani_offset = [0,0,0,0,0,0]

    def register_actions(self):
        actions = {
            '临时测试':
            [
                # ('arm', '左臂-Bridge-装配抓取-就绪'),
                # ('arm', '左臂-Bridge-装配抓取-撤退'),
                # ('arm', '左臂-Bridge-平面放置-预备'),
                # ('arm', '左臂-Bridge-平面放置-就绪'),
                # ('arm', '左臂-Bridge-平面放置-撤退'),
                ('arm', '左臂-Bridge-装配抓取-预备'),
                # ('arm', '左臂-Bridge-装配抓取-就绪'),
                # ('arm', '左臂-Bridge-装配抓取-撤退'),
                # ('arm', '左臂-Bridge-装配放置-预备'),
                # ('arm', '左臂-Bridge-装配放置-就绪'),
                # ('arm', '左臂-初始点')
            ],
            '等待视觉结果确认':
            [
                ('pause', None),
            ],
            '左臂初始化':
            [
                ('arm', '左臂-初始点'),
                ('gripper', '左桥初始化'),
                # ('pause', None),
            ],
            '左臂从平面取件':
            [
                ('arm', '左臂-Bridge-平面抓取-预备'),
                ('arm', '左臂-Bridge-平面抓取-就绪'),
                # ('gripper', '左桥适当闭合'),
                # ('gripper', '左桥抓取闭合'),
                ('pause', None),
                ('arm', '左臂-Bridge-平面抓取-撤退'),
            ],
            '左臂装配':
            [
                ('arm', '左臂-Bridge-装配放置-预备'),
                ('arm', '左臂-Bridge-装配放置-就绪'),
                ('gripper', '左桥抓取放松'),
                ('gripper', '左桥初始化'),
                # ('pause', None),
                ('arm', '左臂-Bridge-装配放置-撤退'),
            ],
            '左臂从装配台取件':
            [
                ('arm', '左臂-Bridge-装配抓取-预备'),
                ('arm', '左臂-Bridge-装配抓取-就绪'),
                ('gripper', '左桥适当闭合'),
                ('gripper', '左桥抓取闭合'),
                # ('pause', None),
                ('arm', '左臂-Bridge-装配抓取-撤退'),
            ],
            '左臂取整件':
            [
                ('arm', '左臂-整件-装配抓取-预备'),
                ('arm', '左臂-整件-装配抓取-就绪'),
                # ('pause', None),
                ('gripper', '左替身适当闭合'),
                # ('pause', None),
                ('gripper', '左替身抓取闭合'),
                # ('pause', None),
                # ('pause', None),
                ('arm', '左臂-整件-装配抓取-撤退'),
            ],
            '左臂摆放':
            [
                ('arm', '左臂-Bridge-平面放置-预备'),
                ('arm', '左臂-Bridge-平面放置-就绪'),
                ('gripper', '左桥抓取放松'),
                ('gripper', '左桥初始化'),
                # ('pause', None),
                ('arm', '左臂-Bridge-平面放置-撤退'),
            ],
            '左臂摆整件':
            [
                ('arm', '左臂-整件-平面放置-预备'),
                ('arm', '左臂-整件-平面放置-就绪'),
                ('gripper', "左替身适当闭合"),
                # ('pause', None),
                ('arm', '左臂-整件-平面放置-撤退'),
                ('gripper', "左替身初始化"),
            ],

            '右臂初始化':
            [
                ('arm', '右臂-初始点')
            ],
            '右臂从平面取件':
            [
                ('arm', '右臂-Bridge-平面抓取-预备'),
                # ('arm', '右臂-Bridge-平面抓取-就绪'),
                # ('arm', '右臂-Bridge-平面抓取-撤退'),
            ],
            '右臂装配':
            [
                ('arm', '右臂-Bridge-装配放置-预备'),
                # ('arm', '右臂-Bridge-装配放置-就绪'),
                # ('arm', '右臂-Bridge-装配放置-撤退'),
            ],
            '右臂从装配台取件':
            [
                ('arm', '右臂-Bridge-装配抓取-预备'),
                # ('arm', '右臂-Bridge-装配抓取-就绪'),
                # ('arm', '右臂-Bridge-装配抓取-撤退'),
            ],
            '右臂摆放':
            [
                ('arm', '右臂-Bridge-平面放置-预备'),
                # ('arm', '右臂-Bridge-平面放置-就绪'),
                # ('arm', '右臂-Bridge-平面放置-撤退'),
            ],
        }
        return actions

    def select_target(self, worldmodel):
        for key in worldmodel['objects'].keys():
            if key.startswith('Bridge'):
                return key

    def update_pick_place_waypoint(self, worldmodel):
        print ("WORLD MODEL: ", worldmodel)
        id = self.select_target(worldmodel)
        try:
            target = worldmodel['objects'][id]
        except Exception as e:
            print ("ERROR: cannot find any Bridge")

        waypoint = self.get_waypoint_config("左臂-Bridge-平面抓取-预备")
        combined_pose = apply_pose_offset(target["pose"][:], self.left_mani_offset)
        waypoint["pose"] = combined_pose[:]
        waypoint["pose"][2] = 0.4
        waypoint["frame_id"] = "arm_base"
        self.update_waypoints(waypoint)

        waypoint = self.get_waypoint_config("左臂-Bridge-平面抓取-就绪")
        combined_pose = apply_pose_offset(target["pose"][:], self.left_mani_offset)
        waypoint["pose"] = combined_pose[:]
        waypoint["frame_id"] = "arm_base"
        self.update_waypoints(waypoint)

        waypoint = self.get_waypoint_config("左臂-Bridge-平面抓取-撤退")
        combined_pose = apply_pose_offset(target["pose"][:], self.left_mani_offset)
        waypoint["pose"] = combined_pose[:]
        waypoint["pose"][2] = 0.4
        waypoint["frame_id"] = "arm_base"
        self.update_waypoints(waypoint)

        waypoint = self.get_waypoint_config("右臂-Bridge-平面抓取-预备")
        combined_pose = apply_pose_offset(target["pose"][:], self.right_mani_offset)
        waypoint["pose"] = combined_pose[:]
        waypoint["pose"][2] = 0.4
        waypoint["frame_id"] = "arm_base"
        self.update_waypoints(waypoint)

        waypoint = self.get_waypoint_config("右臂-Bridge-平面抓取-就绪")
        combined_pose = apply_pose_offset(target["pose"][:], self.right_mani_offset)
        waypoint["pose"] = combined_pose[:]
        waypoint["frame_id"] = "arm_base"
        self.update_waypoints(waypoint)

        waypoint = self.get_waypoint_config("右臂-Bridge-平面抓取-撤退")
        combined_pose = apply_pose_offset(target["pose"][:], self.right_mani_offset)
        waypoint["pose"] = combined_pose[:]
        waypoint["pose"][2] = 0.4
        waypoint["frame_id"] = "arm_base"
        self.update_waypoints(waypoint)




from tf_transformations import quaternion_matrix, translation_matrix, translation_from_matrix, quaternion_from_matrix, euler_matrix
def apply_pose_offset(target_pose, offset_pose):
    """应用位姿偏移量计算
    参数:
        target_pose (list): 基础位姿 [x, y, z, qx, qy, qz, qw]
        offset_pose (list): 相对偏移 [dx, dy, dz, dqx, dqy, dqz, dqw]
    返回:
        list: 组合后的新位姿 [x', y', z', qx', qy', qz', qw']
    """
    # 解包基础位姿
    base_trans = target_pose[0:3]
    base_quat = target_pose[3:7]
    
    # 解包偏移量
    offset_trans = offset_pose[0:3]
    offset_eular = [angle * math.pi / 180.0 for angle in offset_pose[3:]]  # 列表推导式逐个转换
    
    # 构建变换矩阵
    base_matrix = translation_matrix(base_trans) @ quaternion_matrix(base_quat)
    offset_matrix = translation_matrix(offset_trans) @ euler_matrix(offset_eular[0], offset_eular[1], offset_eular[2], axes='sxyz')
    
    # 组合变换 (注意矩阵相乘顺序)
    combined_matrix = base_matrix @ offset_matrix
    
    # 提取结果
    new_trans = translation_from_matrix(combined_matrix)
    new_quat = quaternion_from_matrix(combined_matrix)
    
    return [
        new_trans[0], new_trans[1], new_trans[2],
        new_quat[0], new_quat[1], new_quat[2], new_quat[3]
    ]

if __name__ == "__main__":
    ActionModels("/workspace/src/papjia_skill/config/action_model.yaml")
    action = BridgeOperation("test_tube")
    # print (action.atom_trees)
    print (action.get_waypoint_config("左臂-Bridge-平面抓取-预备"))
    print (action.get_waypoint_config("右臂-Bridge-平面抓取-预备"))