import rclpy
import sys
import copy
from papjia_skill.action_models import ActionModels

from hand_demo_task.task_manager.models.task import Task
from hand_demo_task.task_manager.tasks.bridge.bridge_operation import BridgeOperation
from hand_demo_task.world_manager.data_manipulation import WorldModelManager

class BridgeDemo(Task):
    def __init__(self, task_name, debug=False, params=None):
        super().__init__(task_name, debug, params)
        self.operations["Bridge"] = BridgeOperation("test_tube")
    
def main():
    task_name = "药液试管测试"
    task_sequence = [
        # ("Bridge", '临时测试')
        ("Bridge", '等待视觉结果确认'),
        ("Bridge", '左臂初始化'),
        ("Bridge", '左臂从平面取件'),
        ("Bridge", '左臂装配'),
        # ("Bridge", '左臂从装配台取件'),
        # ("Bridge", '左臂摆放'),
        ("Bridge", '左臂初始化'),
    ]
    task = BridgeDemo(task_name, debug=True)

    world_demo_manager = WorldModelManager()
    result = world_demo_manager.update_model()
    if eval(result["success"])== False:
        raise Exception(result["message"])
    world_model = copy.deepcopy(world_demo_manager.world_model_data)
    task.update_worldmodel("Bridge", world_model)
    result = task.run_step(task_sequence)
    if result is False:
        sys.exit(1)
    # task.update_object_info("test_tube", 0, 0)


if __name__ == "__main__":
    ActionModels("/workspace/src/papjia_skill/config/action_model.yaml")
    rclpy.init()
    main()
    rclpy.shutdown()
