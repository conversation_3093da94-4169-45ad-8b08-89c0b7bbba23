task_configs = {
    # 药液试管准备 ================================
    "测测爪子": [
        ("Stand", '等待视觉结果确认'),
    ],
    "我就看看": [
        ("Bridge", '等待视觉结果确认'),
    ],

    "测桥": [
        ("parallel", [
            [
                ("Plane", '右臂初始化'),
            ],
            [
                ("Bridge", '左臂初始化'),
            ]
        ]),
        ("Bridge", '左臂从平面取件'),
    ],
    "测板": [
        ("parallel", [
            [
                ("Plane", '右臂初始化'),
            ],
            [
                ("Bridge", '左臂初始化'),
            ]
        ]),
        ("Plane", '右臂从平面取件'),
    ],
    "测左圆": [
        ("parallel", [
            [
                ("Plane", '右臂初始化'),
            ],
            [
                ("Cylinder", '左臂初始化'),
            ]
        ]),
        ("Cylinder", '左臂从平面取件'),
    ],
    "测右圆": [
        ("parallel", [
            [
                ("Plane", '右臂初始化'),
            ],
            [
                ("Bridge", '左臂初始化'),
            ]
        ]),
        ("Cylinder", '右臂从平面取件'),
    ],
    "测站": [
        ("parallel", [
            [
                ("Plane", '右臂初始化'),
            ],
            [
                ("Bridge", '左臂初始化'),
            ]
        ]),
        ("Stand", '右臂从平面取件'),
    ],
    "拿放A件": [
        ("parallel", [
            [
                ("Plane", '右臂初始化'),
            ],
            [
                ("Bridge", '左臂初始化'),
            ]
        ]),
        ("Stand", '右臂从装配台取件'),
        ("Stand", "右臂摆整件"),
    ],
    "拿放A件2": [
        ("parallel", [
            [
                ("Plane", '右臂初始化'),
            ],
            [
                ("Bridge", '左臂初始化'),
            ]
        ]),
        ("Bridge", '左臂取整件'), 
        ("Bridge", '左臂摆整件'), 
    ],
    "拿放B件": [
        ("Plane", '右臂从装配台取件'), 
        ("Plane", '右臂摆放'), 
    ],
    
    "回到初始": [
        ("parallel", [
            [
                ("Plane", '右臂初始化'),
            ],
            [
                ("Bridge", '左臂初始化'),
            ]
        ]),
    ],
    "先放大件": [
        # ("Bridge", '等待视觉结果确认'),
        ("parallel", [
            [
                ("Plane", '右臂初始化'),
            ],
            [
                ("Bridge", '左臂初始化'),
            ]
        ]),
        ("parallel", [
            [
                ("Plane", '右臂从平面取件'),
                ("Plane", '右臂装配'),
            ],
            [
                ("Bridge", '左臂从平面取件'),
            ]
        ]),

        ("parallel", [
            [
                ("Cylinder", '右臂初始化'),
            ],
            [
                ('delay', 0.8),
                ("Bridge", '左臂装配'),
            ]
        ]),        
                
        ("parallel", [
            [
                ("Cylinder", '右臂从平面取件'),
                ("Cylinder", '右臂装配'),
                ("Cylinder", '右臂初始化'),
                
            ],
            [
                ("Cylinder", '左臂初始化'),
                ("Cylinder", '左臂从平面取件'),

            ]
        ]),

        ("parallel", [
            [
                ("Stand", '右臂从平面取件'),
            ],
            [
                ("Cylinder", '左臂装配'),
                ("Cylinder", '左臂去翻转'),
            ]
        ]),

        ("parallel", [
            [
                ("Stand", '右臂装配'),
            ],
            [
                ("Cylinder", '左臂还原'),
                
            ]
        ]),
        ("Cylinder", '右臂初始化'),
        
    ],
    "拆了拆了": [
        # ("Bridge", '等待视觉结果确认'),
        ("parallel", [
            [
                ("Plane", '右臂初始化'),
            ],
            [
                ("Bridge", '左臂初始化'),
            ]
        ]),
        ("parallel", [
            [
                ("Stand", '右臂从装配台取件'),
            ],
            [
                ("Cylinder", '左臂初始化'),
            ]
        ]),
        ("Stand", '右臂到摆放中间点'),
        ("parallel", [
            [
                ("Stand", '右臂摆放'),
            ],
            [
                ("Cylinder", '左臂从装配台取件'),
            ]
        ]),

        ("parallel", [
            [   
                ("delay", 2.0),
                ("Cylinder", '右臂从装配台取件'),
                ("Cylinder", '右臂摆放'),
                ("Cylinder", '右臂初始化')
            ],
            [
                ("Cylinder", '左臂摆放'),
                ("Cylinder", '左臂去翻转'),
                ("Cylinder", '左臂还原'),
                ("Bridge", '左臂从装配台取件'), 
            ]
        ]),
  
        ("parallel", [
            [
                ("delay", 2.0),
                ("Plane", '右臂从装配台取件'),
            ],
            [
                ("Bridge", '左臂摆放'),
            ]
        ]), 
        ("parallel", [
            [
                ("Plane", '右臂摆放'),   
            ],
            [
                ("Bridge", '左臂初始化'),
            ]
        ]),  
        ("Plane", '右臂初始化'),          
                      

    ],
    

    "先装二号": [

        ("parallel", [
            [
                ("Plane", '右臂初始化'),
            ],
            [
                ("Bridge", '左臂初始化'),
            ]
        ]),
        ("parallel", [
            [
                ("Plane", '右臂从平面取件'),
                ("Plane", '右臂装配'),
            ],
            [
                ("Bridge", '左臂从平面取件'),
            ]
        ]),

        ("parallel", [
            [
                ("Cylinder", '右臂初始化'),
            ],
            [
                ('delay', 0.8),
                ("Bridge", '左臂装配'),
            ]
        ]),
        ("parallel", [
            [
                ("Stand", '右臂从平面取件'),
            ],
            [
                ("Bridge", '左臂初始化'),
            ]
        ]),
        ("Stand", '右臂装配'),
        ("parallel", [
            [
                ("Cylinder", '右臂初始化'),
                ("Cylinder", '右臂从平面取件'),     
            ],
            [
                ("Cylinder", '左臂初始化'),
                ("Cylinder", '左臂从平面取件'),

            ]
        ]),
        ("Cylinder", '左臂装配'),
        ("parallel", [
            [
                ("Cylinder", '左臂去翻转'),
                ("Cylinder", '左臂还原'),
            ],
            [
                ("Cylinder", '右臂装配'),
                ("Cylinder", '右臂初始化'),                
            ]
        ]),

    ],
}