import rclpy
import sys
import copy
from papjia_skill.action_models import ActionModels

from hand_demo_task.task_manager.models.task import Task
from hand_demo_task.task_manager.tasks.stand.stand_operation import StandOperation
from hand_demo_task.world_manager.data_manipulation import WorldModelManager

class StandDemo(Task):
    def __init__(self, task_name, debug=False, params=None):
        super().__init__(task_name, debug, params)
        self.operations["Stand"] = StandOperation("test_tube")
    
def main():
    task_name = "药液试管测试"
    task_sequence = [
        ("Stand", '等待视觉结果确认'),
        # ("Stand", '临时测试'),
        ("Stand", '右臂初始化'),
        ("Stand", '右臂从平面取件'),
        ("Stand", '右臂装配'),
        ("Stand", '右臂从装配台取件'),
        ("Stand", '右臂到摆放中间点'),
        ("Stand", '右臂摆放'),
        ("Stand", '右臂初始化'),
    ]
    task = StandDemo(task_name, debug=True)
    
    world_demo_manager = WorldModelManager()
    result = world_demo_manager.update_model()
    if eval(result["success"])== False:
        raise Exception(result["message"])
    world_model = copy.deepcopy(world_demo_manager.world_model_data)
    task.update_worldmodel("Stand", world_model)
    result = task.run_step(task_sequence)
    if result is False:
        sys.exit(1)


if __name__ == "__main__":
    ActionModels("/workspace/src/papjia_skill/config/action_model.yaml")
    rclpy.init()
    main()
    rclpy.shutdown()
