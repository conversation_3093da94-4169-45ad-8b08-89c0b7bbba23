from datetime import datetime
from enum import Enum
import time

from papjia_skill.executor import Pa<PERSON>jiaSkillExecutor
from hand_demo_task.task_manager.core.tree_builder import TaskTreeBuilder

class TaskStatus(Enum):
    NONE = "none"
    INITIALIZED = "initialized"   # 已初始化
    CONFIGURED = "configured"     # 已配置
    RUNNING = "running"           # 运行中
    PAUSING = "pausing"           # 暂停中
    PAUSED = "paused"             # 已暂停
    STOPPING = "stopping"         # 停止中
    STOPPED = "stopped"           # 已停止
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"             # 已失败

class InvalidStateError(Exception):
    """非法状态转换异常"""
    pass

class Task:
    def __init__(self, task_name, available_objects, debug=False, params=None):
        self.task_name = task_name
        self.params = params or {}
        self.status = TaskStatus.INITIALIZED
        self.created_at = datetime.now()
        self.started_at = None
        self.finished_at = None
        self.paused_at = None
        self.resumed_at = None
        self.progress = 0
        self.result = None
        self.error = None

        self.tree_builder = TaskTreeBuilder(PapjiaSkillExecutor(), debug)
        self.operations = {}
        self.available_objects = available_objects
        
        self.current_action_description = {
            'action_no': 0,
            'total_actions': 0,
            'object_type': None,
            'action_name': None,
        }
        self.task_progress = {
            'step': 0,
            'total_steps': 0,
            'step_name': None,
            'current_action': self.current_action_description
        }
        
    def to_dict(self):
        """序列化为字典"""
        return {
            "task_name": self.task_name,
            "params": self.params,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "finished_at": self.finished_at.isoformat() if self.finished_at else None,
            "paused_at": self.paused_at.isoformat() if self.paused_at else None,
            "resumed_at": self.resumed_at.isoformat() if self.resumed_at else None,
            "progress": self.progress,
            "result": self.result,
            "error": self.error
        }

    def configure(self, params):
        """完成参数配置"""
        if self.status != TaskStatus.INITIALIZED:
            raise InvalidStateError("只能在初始化状态进行配置")
        self.params.update(params)
        self.status = TaskStatus.CONFIGURED

    def start(self):
        """启动任务运行"""
        if self.status != TaskStatus.CONFIGURED:
            raise InvalidStateError(f"无法从 {self.status} 状态启动")
        
        self.status = TaskStatus.RUNNING
        if not self.started_at:
            self.started_at = datetime.now()

    def pause(self):
        """暂停任务"""
        if self.status != TaskStatus.RUNNING:
            raise InvalidStateError("只有运行中的任务可以暂停")
        self.status = TaskStatus.PAUSING

    def confirm_pause(self):
        """确认任务已暂停"""
        if self.status != TaskStatus.PAUSING:
            raise InvalidStateError("未在暂停中的任务无法确认")
        self.status = TaskStatus.PAUSED
        self.paused_at = datetime.now()
    
    def resume(self):
        """恢复暂停的任务"""
        if self.status != TaskStatus.PAUSED:
            raise InvalidStateError("只有暂停的任务可以恢复")
        self.status = TaskStatus.RUNNING
        self.resumed_at = datetime.now()

    def stop(self):
        """请求停止任务"""
        allowed = [TaskStatus.RUNNING, TaskStatus.PAUSED]
        if self.status not in allowed:
            raise InvalidStateError(f"无法从 {self.status} 状态停止")
        self.status = TaskStatus.STOPPING

    def confirm_stop(self):
        """确认任务已停止"""
        if self.status != TaskStatus.STOPPING:
            raise InvalidStateError("未在停止中的任务无法确认")
        self.status = TaskStatus.STOPPED
        self.finished_at = datetime.now()

    def confirm_complete(self, result=None):
        """标记任务完成"""
        if self.status not in [TaskStatus.RUNNING, TaskStatus.STOPPING]:
            raise InvalidStateError("只有运行中或停止中的任务可以完成")
        self.status = TaskStatus.COMPLETED
        self.finished_at = datetime.now()
        self.result = result
        self.progress = 100

    def confirm_fail(self, error=None):
        """标记任务失败"""
        self.status = TaskStatus.FAILED
        self.finished_at = datetime.now()
        self.error = str(error) if error else None

    def run_step(self, task_sequence):
        self.current_action_description['total_actions'] = len(task_sequence)
        
        for action_no, (object_type, action_name) in enumerate(task_sequence):            
            action_tree = self.tree_builder.generate_task_trees([(object_type, action_name)])[0]
            print (action_tree)
            self.current_action_description['action_no'] = action_no + 1
            self.current_action_description['object_type'] = object_type
            self.current_action_description['action_name'] = action_name
            print(f"  Action {self.current_action_description['action_no']}/{self.current_action_description['total_actions']}: {action_name}")
            
            if self.tree_builder.execute_task_tree(action_name, action_tree) is False:
                print(f"执行失败: {action_name}")
                return False
        return True
    
    def run(self, task_steps, task_configs):
        for i, step_name in enumerate(task_steps):
            self.task_progress['step'] = i + 1
            self.task_progress['total_steps'] = len(task_steps)
            self.task_progress['step_name'] = step_name
            print(f"=== Step {self.task_progress['step']}/{self.task_progress['total_steps']}: {step_name}")
            
            task_sequence = task_configs[step_name]
            success = self.run_step(task_sequence)
            
            if not success:
                self.confirm_fail(f"执行失败: {step_name}")
                return False
        return True
    
    def register_atom_trees(self, object_name, atom_trees):
        self.tree_builder.atom_trees[object_name] = atom_trees
    
    def update_object_info(self, object_name, idx_x, idx_y):
        self.operations[object_name].update(idx_x, idx_y)
        self.register_atom_trees(object_name, self.operations[object_name].atom_trees)

    def update_worldmodel(self, object_name, worldmodel):
        self.operations[object_name].update(worldmodel)
        self.register_atom_trees(object_name, self.operations[object_name].atom_trees)