import time
import threading
import copy
from hand_demo_task.task_manager.core.task_manager import <PERSON><PERSON>anager
from hand_demo_task.task_manager.handdemo_task import HanddemoTask
from hand_demo_task.task_manager.models.task import TaskStatus
from hand_demo_task.world_manager.data_manipulation import WorldModelManager

from hand_demo_task.task_manager.tasks.config_task import task_configs


class HanddemoTaskManager(TaskManager):
    def __init__(self, debug=False):
        super().__init__()
        self.debug = debug
        self.world_demo_manager = WorldModelManager()

        # 任务进度跟踪
        self.task_progress = {
            'task_progress': None
        }
        
        self.task_steps = []

        # 世界模型数据
        self.world_model_data = None
        
        # 添加任务启动信号事件
        self.task_started_event = threading.Event()
        self.task_start_result = None
        
        # 循环模式控制
        self._loop_mode = True  # 默认开启循环模式
        self._loop_mode_lock = threading.Lock()  # 用于线程安全的访问

    def set_loop_mode(self, enabled):
        """设置循环模式"""
        with self._loop_mode_lock:
            self._loop_mode = enabled

    def get_loop_mode(self):
        """获取循环模式状态"""
        with self._loop_mode_lock:
            return self._loop_mode

    def get_world_model(self):
        """获取世界模型数据"""
        if self.world_model_data is None:
            self.update_world_model_from_perception()
        return self.world_model_data

    def update_world_model_from_perception(self):
        """通过感知系统更新世界模型"""
        try:
            self.world_demo_manager.update_model()
            self.world_model_data = copy.deepcopy(self.world_demo_manager.world_model_data)
            #TODO: 当前环境状态，分为备件状态和安装件状态
            #备件状态为备件名，位姿
            #安装件状态为安装件名，位姿，安装情况（件1-3是否安装）
        except Exception as e:
            raise Exception(f"更新世界模型失败: {str(e)}")

    def create_task(self):
        try:
            task = HanddemoTask(f"Handdemo")
            super()._create_task(task)
            super()._configure_task({"world_model_data": self.get_world_model()})
            return True
        except Exception as e:
            raise Exception(f"任务创建失败: {str(e)}")
    
    def get_current_task_status(self):
        if self._current_task is None:
            raise Exception("没有正在运行的任务")
        return self._current_task.status

    def get_task_steps(self):
        return self.task_steps

    def get_current_task_info(self):
        if self._current_task is None:
            raise Exception("没有正在运行的任务")
        return self._current_task.to_dict()
    
    def get_current_task_progress(self):
        if self._current_task is None:
            raise Exception("没有正在运行的任务")
        if self._current_task.status in [TaskStatus.NONE, TaskStatus.INITIALIZED, TaskStatus.CONFIGURED]:
            raise Exception("任务未启动")
        return self.task_progress

    def start_task(self):
        # 重置启动信号
        self.task_started_event.clear()
        self.task_start_result = None
        
        run_task_thread = threading.Thread(target=self.run)
        run_task_thread.start()
        
        try:
            # 等待任务启动信号，最多等待10秒
            if self.task_started_event.wait(timeout=10):
                print(f"任务启动结果: {self.task_start_result}")
                if self.task_start_result['success'] is False:
                    raise Exception(self.task_start_result['message'])
                else:
                    return True
            else:
                raise Exception("任务启动超时")
        except Exception as e:
            raise Exception(str(e))

    def run(self):
        running_task = self._get_current_task()
        
        if running_task is None:
            self.task_start_result = {"success": False, "message": "没有正在运行的任务"}
            self.task_started_event.set()  # 发出信号，表示任务启动失败
            return False, "没有正在运行的任务"

        try:
            running_task.start()
            
            # 发出任务已成功启动的信号
            self.task_start_result = {"success": True, "message": "任务启动成功"}
            self.task_started_event.set()

            while True:
                # 在演示中这是一个循环，在每个循环开始的时候都需要获得一次最近的world_model
                # self.update_world_model_from_perception()
                # 然后根据这个world_model来规划动作序列
                
                # TODO: 这里的self.task_steps， running_task.task_progress应该是动作序列规划出来的，而不是固定的
                self.task_steps = running_task.generate_actions(self.get_world_model())
                self.task_progress['task_progress'] = running_task.task_progress
                running_task.task_progress['total_steps'] = len(self.task_steps)

                if self._stop_flag.is_set():
                    print("任务被停止")
                    running_task.confirm_stop()
                    self._stop_flag.clear()
                    return False, "任务被停止"

                for task_step_idx, step_name in enumerate(self.task_steps):
                    # 支持中途暂停，但不能中途停止。等待取消暂停信号
                    if self._pause_flag.is_set():
                        running_task.confirm_pause()
                        print(f"{time.strftime('%Y-%m-%d %H:%M:%S')}任务被暂停，等待恢复")
                        
                        while self._pause_flag.is_set():
                            time.sleep(1)
                        running_task.resume()
                        print(f"{time.strftime('%Y-%m-%d %H:%M:%S')}任务已恢复")
                        self._pause_flag.clear()
                    
                    # 执行任务
                    running_task.task_progress['step'] = task_step_idx + 1
                    running_task.task_progress['step_name'] = step_name
                    print(f"=== Step {running_task.task_progress['step']}/{running_task.task_progress['total_steps']}: {step_name}")
                    
                    task_sequence = task_configs[step_name]
                    result = running_task.run_step(task_sequence)
                    # time.sleep(0.1)
                    # result = True
                    if result is False:
                        running_task.confirm_fail(f"执行任务失败: {step_name}")
                        return False, "任务执行失败"
                
                # 检查是否继续循环
                with self._loop_mode_lock:
                    if not self._loop_mode:
                        print("循环模式已关闭，任务执行完成")
                        running_task.confirm_complete()
                        return True
                    else:
                        print("循环模式开启，开始下一轮执行")
                        # 重置任务进度
                        running_task.task_progress['step'] = 0
                        running_task.task_progress['step_name'] = ""
            
        except Exception as e:
            # 如果还没有发送启动信号，则发送失败信号
            if not self.task_started_event.is_set():
                self.task_start_result = {"success": False, "message": str(e)}
                self.task_started_event.set()
                
            print(f"任务执行出错: {str(e)}")
            running_task.confirm_fail(str(e))
            return False
    
    def pause_task(self):
        try:
            current_task = self._get_current_task()
            if not current_task:
                raise Exception("没有正在运行的任务")

            if current_task.status != TaskStatus.RUNNING:
                raise Exception("当前任务未处于运行状态")

            if self._pause_flag.is_set():
                raise Exception("任务已被暂停")
            
            self._pause_flag.set()
            current_task.pause()
        except Exception as e:
            raise Exception(str(e))
    
    def resume_task(self):
        try:
            current_task = self._get_current_task()
            if not current_task:
                raise Exception("没有正在运行的任务")

            if current_task.status != TaskStatus.PAUSED:
                raise Exception("当前任务未处于暂停状态")
            
            if not self._pause_flag.is_set():
                raise Exception("任务未被暂停")
            
            self._pause_flag.clear()
        except Exception as e:
            raise Exception(str(e))
    
    def stop_task(self):
        try:
            current_task = self._get_current_task()
            if not current_task:
                raise Exception("没有正在运行的任务")
            
            if self._stop_flag.is_set():
                raise Exception("任务已被停止")
            
            if self._pause_flag.is_set():
                raise Exception("任务被暂停，无法直接停止")
            
            self._stop_flag.set()
            current_task.stop()
        except Exception as e:
            raise Exception(str(e))
