#这个文件可以视作独立的任务执行入口

import rclpy
import sys
import copy
import threading

from papjia_skill.action_models import ActionModels

from hand_demo_task.task_manager.models.task import Task

# 所有对象的子动作都在这边定义
from hand_demo_task.task_manager.tasks.bridge.bridge_operation import BridgeOperation
from hand_demo_task.task_manager.tasks.cylinder.cylinder_operation import Cylinder<PERSON>peration
from hand_demo_task.task_manager.tasks.plane.plane_operation import PlaneOperation
from hand_demo_task.task_manager.tasks.stand.stand_operation import StandOperation

from hand_demo_task.task_manager.tasks.config_task import task_configs

from hand_demo_task.world_manager.data_manipulation import WorldModelManager


class HanddemoTask(Task):
    def __init__(self, task_name, debug=False, params=None):
        super().__init__(task_name, debug, params)
        
        self.operations = {
            "Bridge": BridgeOperation("test_tube"),
            "Cylinder": CylinderOperation("test_tube"),
            "Plane": PlaneOperation("test_tube"),
            "Stand": StandOperation("test_tube")
        }

    def generate_actions(self, world_model):
        """根据最新的世界模型更新任务动作序列"""
        # 更新各个操作对象的世界模型
        for operation_name in self.operations:
            self.update_worldmodel(operation_name, world_model)
        
        # TODO: 这里可以根据world_model的状态来动态规划任务序列
        return [
            # "先装二号",
            "先放大件",
            "拆了拆了",
            # "拿放A件2",
            # "测板",
        ]

def main():
    task_name = "HanddemoTask"
    task_steps = [
        # "测测爪子",
        # "我就看看",
        # "回到初始",
        "先放大件",
        # "先装二号",
        "拆了拆了",
        # "先装二号"
    ]
    task = HanddemoTask(task_name, debug=False)

    world_demo_manager = WorldModelManager()
    result = world_demo_manager.update_model()
    if eval(result["success"])== False:
        raise Exception(result["message"])
    world_model = copy.deepcopy(world_demo_manager.world_model_data)
    task.update_worldmodel("Plane", world_model)
    task.update_worldmodel("Stand", world_model)
    task.update_worldmodel("Bridge", world_model)
    task.update_worldmodel("Cylinder", world_model)
    
    success = task.run(task_steps, task_configs)
    if not success:
        print(f"Task failed with status: {task.status.value}")
        sys.exit(1)
    print(f"Task completed successfully.")

if __name__ == "__main__":
    ActionModels("/workspace/src/papjia_skill/config/action_model.yaml")
    rclpy.init()
    try:
        main()
    except KeyboardInterrupt:
        pass
    finally:
        rclpy.shutdown()
