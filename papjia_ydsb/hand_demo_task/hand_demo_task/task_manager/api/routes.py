import time
from flask import Blueprint, request, jsonify

from hand_demo_task.task_manager.handdemo_task_manager import HanddemoTaskManager
from hand_demo_task.task_manager.models.task import TaskStatus

# 创建Blueprint
task_manager_api = Blueprint('task_manager_api', __name__)

# 全局变量，用于懒加载
_manager = None

def get_manager():
    """懒加载方式获取TaskManager实例"""
    global _manager
    if _manager is None:
        _manager = HanddemoTaskManager()
    return _manager

@task_manager_api.route('/api/task/worldmodel', methods=['GET'])
def get_world_model():
    """获取世界模型"""
    try:
        manager = get_manager()
        
        # 检查当前任务状态
        try:
            current_task = manager._get_current_task()
            if current_task and (current_task.status == TaskStatus.RUNNING 
                              or current_task.status == TaskStatus.PAUSING
                              or current_task.status == TaskStatus.STOPPING
                              ):
                return jsonify({
                    "status": False, 
                    "message": "任务执行中，不能更新世界模型"
                }), 400
        except Exception:
            pass  # 如果获取任务状态失败，继续执行
            
        manager.update_world_model_from_perception()
        return jsonify({"status": True, "data": manager.get_world_model()})
    except Exception as e:
        return jsonify({"status": False, "message": f"获取世界模型失败: {str(e)}"}), 400

@task_manager_api.route('/api/task/steps', methods=['GET'])
def get_task_steps():
    """获取当前任务步骤"""
    try:
        manager = get_manager()
        steps = manager.get_task_steps()
        return jsonify({"status": True, "data": steps})
    except Exception as e:
        return jsonify({"status": False, "message": f"获取任务步骤失败: {str(e)}"}), 400

@task_manager_api.route('/api/task', methods=['GET'])
def get_current_task_info():
    """获取当前任务"""
    try:
        manager = get_manager()
        task_info = manager.get_current_task_info()
        task_progress = manager.get_current_task_progress()
        return jsonify({"status": True, "data": {
            "task_info": task_info,
            "task_progress": task_progress
        }})
    except Exception as e:
        return jsonify({"status": False, "message": f"获取当前任务失败: {str(e)}"}), 400

@task_manager_api.route('/api/task/config', methods=['PUT'])
def update_task_config():
    """更新任务配置"""
    try:
        data = request.get_json()
        if 'loop_mode' not in data:
            return jsonify({
                "status": False,
                "message": "缺少必要的参数: loop_mode"
            }), 400

        manager = get_manager()
        # 检查当前任务状态
        try:
            current_task = manager._get_current_task()
            if current_task and current_task.status == TaskStatus.STOPPING:
                return jsonify({
                    "status": False,
                    "message": "任务正在停止中，不能更新配置"
                }), 400
        except Exception:
            pass  # 如果获取任务状态失败，继续执行

        # 更新循环模式配置
        manager.set_loop_mode(data['loop_mode'])
        
        return jsonify({
            "status": True,
            "message": f"已{'开启' if data['loop_mode'] else '关闭'}循环模式"
        })
    except Exception as e:
        return jsonify({
            "status": False,
            "message": f"更新任务配置失败: {str(e)}"
        }), 400

@task_manager_api.route('/api/task/start', methods=['POST'])
def start_task():
    """创建并启动一个新任务"""
    try:
        data = request.get_json() or {}
        manager = get_manager()
        
        # 设置循环模式（如果提供）
        if 'loop_mode' in data:
            manager.set_loop_mode(data['loop_mode'])
            
        # 创建任务
        manager.create_task()
        
        # 立即启动任务，并等待任务真正开始运行的信号
        success = manager.start_task()
        
        if success:
            return jsonify({
                "status": True, 
                "message": "任务已创建并成功启动"
            }), 201
    except Exception as e:
        return jsonify({"status": False, "message": f"任务启动失败: {str(e)}"}), 400

@task_manager_api.route('/api/task/pause', methods=['POST'])
def pause_task():
    """暂停当前任务"""
    try:
        manager = get_manager()
        manager.pause_task()
        assert manager.get_current_task_status() == TaskStatus.PAUSING
        while manager.get_current_task_status() == TaskStatus.PAUSING:
            time.sleep(1)
        assert manager.get_current_task_status() == TaskStatus.PAUSED
        return jsonify({"status": True, "message": "任务已暂停"})
    except Exception as e:
        return jsonify({"status": False, "message": f"暂停任务失败: {str(e)}"}), 400

@task_manager_api.route('/api/task/resume', methods=['POST'])
def resume_task():
    """恢复当前任务"""
    try:
        manager = get_manager()
        manager.resume_task()
        while manager.get_current_task_status() != TaskStatus.RUNNING:
            time.sleep(1)
        return jsonify({"status": True, "message": "任务已恢复"})
    except Exception as e:
        return jsonify({"status": False, "message": f"恢复任务失败: {str(e)}"}), 400

@task_manager_api.route('/api/task/stop', methods=['POST'])
def stop_task():
    """停止当前任务"""
    try:
        manager = get_manager()
        manager.stop_task()
        assert manager.get_current_task_status() == TaskStatus.STOPPING
        while manager.get_current_task_status() == TaskStatus.STOPPING:
            time.sleep(1)
        assert manager.get_current_task_status() == TaskStatus.STOPPED
        return jsonify({"status": True, "message": "任务已停止"})
    except Exception as e:
        return jsonify({"status": False, "message": f"停止任务失败: {str(e)}"}), 400