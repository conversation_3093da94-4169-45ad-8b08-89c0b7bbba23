device_presets = {
    # 夹爪设备
    'gripper': {
        '右圆柱初始化': {
            'task_id': 'cylindery',
            'point_id': 'init', 
            'wait_time': 1.0
        },   
        '右圆柱稍微闭合': {
            'task_id': 'cylindery',
            'point_id': 'pre1', 
            'wait_time': 1.0
        },  
        '右圆柱适当闭合': {
            'task_id': 'cylindery',
            'point_id': 'pre2', 
            'wait_time': 1.0
        },  
        '右圆柱抓取闭合': {
            'task_id': 'cylindery',
            'point_id': 'ready1', 
            'wait_time': 1.0
        },  
        '右圆柱抓取放松': {
            'task_id': 'cylindery',
            'point_id': 'pre1', 
            'wait_time': 1.0
        },
        '右替身初始化': {
            'task_id': 'stand',
            'point_id': 'init', 
            'wait_time': 1.0
        }, 
        '右替身适当闭合': {
            'task_id': 'stand',
            'point_id': 'pre1', 
            'wait_time': 1.0
        },  
        '右替身抓取闭合': {
            'task_id': 'stand',
            'point_id': 'ready', 
            'wait_time': 1.0
        },  
        '右替身抓取放松': {
            'task_id': 'stand',
            'point_id': 'pre1', 
            'wait_time': 1.0
        },  
        '左替身初始化': {
            'task_id': 'stand_left',
            'point_id': 'init', 
            'wait_time': 1.0
        }, 
        '左替身适当闭合': {
            'task_id': 'stand_left',
            'point_id': 'pre1', 
            'wait_time': 1.0
        },  
        '左替身抓取闭合': {
            'task_id': 'stand_left',
            'point_id': 'ready', 
            'wait_time': 1.0
        },  
        '左替身抓取放松': {
            'task_id': 'stand_left',
            'point_id': 'pre1', 
            'wait_time': 1.0
        },  
        '右平板初始化': {
            'task_id': 'plane',
            'point_id': 'init', 
            'wait_time': 1.0
        }, 
        '右平板适当闭合': {
            'task_id': 'plane',
            'point_id': 'pre5', 
            'wait_time': 1.0
        },  
        '右平板抓取闭合': {
            'task_id': 'plane',
            'point_id': 'ready5', 
            'wait_time': 1.0
        },  
        '右平板抓取放松': {
            'task_id': 'plane',
            'point_id': 'init', 
            'wait_time': 1.0
        },
        '左桥初始化': {
            'task_id': 'bridge',
            'point_id': 'init', 
            'wait_time': 1.0
        }, 
        '左桥适当闭合': {
            'task_id': 'bridge',
            'point_id': 'pre', 
            'wait_time': 1.0
        },  
        '左桥抓取闭合': {
            'task_id': 'bridge',
            'point_id': 'ready', 
            'wait_time': 1.0
        },  
        '左桥抓取放松': {
            'task_id': 'bridge',
            'point_id': 'pre', 
            'wait_time': 1.0
        },      
        '左圆柱初始化': {
            'task_id': 'cylinder_left',
            'point_id': 'init', 
            'wait_time': 1.0
        }, 
        '左圆柱适当闭合': {
            'task_id': 'cylinder_left',
            'point_id': 'pre', 
            'wait_time': 1.0
        },  
        '左圆柱抓取闭合': {
            'task_id': 'cylinder_left',
            'point_id': 'ready', 
            'wait_time': 1.0
        },  
        '左圆柱抓取狠狠闭合': {
            'task_id': 'cylinder_left',
            'point_id': 'ready2', 
            'wait_time': 1.0
        },
        '左圆柱抓取放松': {
            'task_id': 'cylinder_left',
            'point_id': 'pre', 
            'wait_time': 1.0
        },           
    },
    
}