import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from papjia_behavior_interface.action import ExecuteTree

from papjia_skill.executor import PapjiaSkillExecutor
from papjia_skill.btree import BehaviorRoot, BehaviorTree, Sequence, Parallel, SubTree
from papjia_skill.action_models import ActionModels
from papjia_skill.atom.remove_escape_character import RemoveEscapeCharacter
from papjia_skill.atom.papjia_delay_async import PapjiaDelayAsync
from papjia_skill.utils import swap_quotes, add_escape_character
from papjia_skill.atom.object_detect import ObjectDetect
from papjia_skill.atom.object_to_json import ObjectTo<PERSON>son
from papjia_skill.atom.remove_escape_character import RemoveEscapeCharacter
from papjia_skill.atom.rh15_cmd_publisher import Rh15CmdPublisher

config_path="/workspace/install/hand_demo_config/share/hand_demo_config/config/task.json"

from hand_demo_task.driver_manager.config_device import device_presets

def gripper_command(config_name):
    config = device_presets['gripper'][config_name]
    cmd = Rh15CmdPublisher(
        config_path=config_path,
        task_id=config['task_id'],
        point_id=config['point_id'],
        wait_time=config['wait_time'],
    )
    return cmd

def tree_test_main(tree_name="TestRh15Gripper"):

    seq1 = gripper_command("测试1")
    seq2 = gripper_command("测试2")
    seq3 = gripper_command("测试1")
    tree = BehaviorTree(tree_name)
    seq = Sequence("SeqRh15CmdPublisher")
    seq.add_child(seq1) 
    seq.add_child(seq2)  
    seq.add_child(seq3)   
    tree.add_child(seq)
    root = BehaviorRoot()
    root.add_child(tree)
    return tree_name, root.to_str()

def main(args=None):
    rclpy.init(args=args)
    executor = PapjiaSkillExecutor()

    models = ActionModels(
        "/workspace/src/papjia_skill/config/action_model.yaml"
    )  # 需要显式加载原子动作模型以检查字段
    tree_id, tree_str = tree_test_main()
    print(tree_str)

    try:
        executor.execute_tree(tree_string=tree_str, tree_name=tree_id)
    except ValueError as e:
        executor.get_logger().error(f"错误: {e}")
    except Exception as e:
        executor.get_logger().error(f"未知错误: {e}")
    finally:
        executor.destroy_node()
        rclpy.shutdown()


if __name__ == "__main__":
    ActionModels("/workspace/src/papjia_skill/config/action_model.yaml")
    main()
    # print(gripper_command("测试"))