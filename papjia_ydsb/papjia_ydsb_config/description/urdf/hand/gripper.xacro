<?xml version="1.0" encoding="UTF-8"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
	<xacro:macro name="gripper" params="parent prefix *origin">
		<!-- 手部主体 - 原点在手部底部中心 -->
		<link name="${prefix}gripper_base">
			<visual>
				<origin xyz="0 0 0.045" rpy="0 0 0"/>
				<geometry>
					<box size="0.065 0.065 0.09"/>
				</geometry>
				<material name="gripper_material">
					<color rgba="0.7 0.7 0.7 1.0"/>
				</material>
			</visual>
			<collision>
				<origin xyz="0 0 0.045" rpy="0 0 0"/>
				<geometry>
					<box size="0.065 0.065 0.09"/>
				</geometry>
			</collision>
			<inertial>
				<mass value="0.1"/>
				<origin xyz="0 0 0.045" rpy="0 0 0"/>
				<inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
			</inertial>
		</link>

		<!-- 左指头 -->
		<link name="${prefix}left_finger">
			<visual>
				<origin xyz="0 0 0" rpy="0 0 0"/>
				<geometry>
					<box size="0.02 0.02 0.04"/>
				</geometry>
				<material name="finger_material">
					<color rgba="0.5 0.5 0.5 1.0"/>
				</material>
			</visual>
			<collision>
				<origin xyz="0 0 0" rpy="0 0 0"/>
				<geometry>
					<box size="0.02 0.02 0.04"/>
				</geometry>
			</collision>
			<inertial>
				<mass value="0.01"/>
				<origin xyz="0 0 0" rpy="0 0 0"/>
				<inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
			</inertial>
		</link>

		<!-- 右指头 -->
		<link name="${prefix}right_finger">
			<visual>
				<origin xyz="0 0 0" rpy="0 0 0"/>
				<geometry>
					<box size="0.02 0.02 0.04"/>
				</geometry>
				<material name="finger_material"/>
			</visual>
			<collision>
				<origin xyz="0 0 0" rpy="0 0 0"/>
				<geometry>
					<box size="0.02 0.02 0.04"/>
				</geometry>
			</collision>
			<inertial>
				<mass value="0.01"/>
				<origin xyz="0 0 0" rpy="0 0 0"/>
				<inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
			</inertial>
		</link>

		<!-- 指尖中心link - 用于表示夹爪工作点 -->
		<link name="${prefix}finger_center">
			<visual>
				<origin xyz="0 0 0" rpy="0 0 0"/>
				<geometry>
					<sphere radius="0.005"/>
				</geometry>
				<material name="center_material">
					<color rgba="1.0 0.0 0.0 1.0"/>
				</material>
			</visual>
			<collision>
				<origin xyz="0 0 0" rpy="0 0 0"/>
				<geometry>
					<sphere radius="0.005"/>
				</geometry>
			</collision>
			<inertial>
				<mass value="0.001"/>
				<origin xyz="0 0 0" rpy="0 0 0"/>
				<inertia ixx="0.000001" ixy="0" ixz="0" iyy="0.000001" iyz="0" izz="0.000001"/>
			</inertial>
		</link>

		<!-- 手部与父链接的连接 -->
		<joint name="${prefix}gripper_base_joint" type="fixed">
			<xacro:insert_block name="origin" />
			<parent link="${parent}" />
			<child link="${prefix}gripper_base" />
		</joint>

		<!-- 左指头与手部的连接 - 插在手部顶部，间隔2.5cm -->
		<joint name="${prefix}left_finger_joint" type="fixed">
			<origin xyz="0.0125 0 0.11" rpy="0 0 0"/>
			<parent link="${prefix}gripper_base" />
			<child link="${prefix}left_finger" />
		</joint>

		<!-- 右指头与手部的连接 - 插在手部顶部，间隔2.5cm -->
		<joint name="${prefix}right_finger_joint" type="fixed">
			<origin xyz="-0.0125 0 0.11" rpy="0 0 0"/>
			<parent link="${prefix}gripper_base" />
			<child link="${prefix}right_finger" />
		</joint>
        
		<!-- 指尖中心与手部的连接 -->
		<link name="${prefix}finger_center_old" />
		<!-- 指尖中心与手部的连接 -->
		<joint name="${prefix}finger_center_joint_old" type="fixed">
			<origin xyz="0 0 0.13" rpy="0 3.1415926 0"/>
			<parent link="${prefix}gripper_base" />
			<child link="${prefix}finger_center_old" />
		</joint>

		<!-- 指尖中心与手部的连接 -->
		<joint name="${prefix}finger_center_joint" type="fixed">
			<origin xyz="0 0 0.13" rpy="0 3.1415926 -1.0471975511965"/>
			<parent link="${prefix}gripper_base" />
			<child link="${prefix}finger_center" />
		</joint>
		<!-- 指尖中心与手部的连接2 -->
		<link name="${prefix}finger_center2" />
		<joint name="${prefix}finger_center_joint2" type="fixed">
			<origin xyz="0 0 0.13" rpy="0 3.1415926 0.52359877"/>
			<parent link="${prefix}gripper_base" />
			<child link="${prefix}finger_center2" />
		</joint>
	</xacro:macro>
</robot> 