<?xml version="1.0" encoding="UTF-8"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
	<xacro:macro name="左手" params="prefix parent *origin">
		<link name="${prefix}hand">
			<visual>
				<origin xyz="0 0 0" rpy="0 0 0" />
				<geometry>
					<mesh filename="package://papjia_ydsb_config/description/meshes/hand_left.STL" />
				</geometry>
				<material name="">
					<color rgba="0.79216 0.81961 0.93333 1" />
				</material>
			</visual>
			<!-- <collision>
				<origin xyz="0 0 0" rpy="0 0 0" />
				<geometry>
					<mesh filename="package://papjia_ydsb_config/description/meshes/hand_left.STL" />
				</geometry>
			</collision> -->
		</link>
		<joint name="${prefix}joint_hand" type="fixed">
			<xacro:insert_block name="origin" />
			<parent link="${parent}" />
			<child link="${prefix}hand" />
		</joint>

		<link name="${prefix}grasp" />
		<link name="${prefix}vacuum_cup" />

		<joint name="${prefix}joint_grasp" type="fixed">
			<origin xyz="0.025 0.0175 -0.2" rpy="0 0 1.5708" />
			<parent link="${prefix}hand" />
			<child link="${prefix}grasp" />
			<axis xyz="0 0 0" />
		</joint>
		<joint name="${prefix}joint_vacuum_cup" type="fixed">
			<origin xyz="0.06 0.06 0" rpy="1.5708 0 0" />
			<parent link="${prefix}hand" />
			<child link="${prefix}vacuum_cup" />
			<axis xyz="0 0 0" />
		</joint>
	</xacro:macro>
</robot>
