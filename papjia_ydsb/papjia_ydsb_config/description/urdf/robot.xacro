<?xml version="1.0"?>
<robot xmlns:xacro="http://ros.org/wiki/xacro">

<xacro:macro name="robot" params="parent *origin use_mock_hardware">
	<joint name="joint_arm_base" type="fixed">
		<xacro:insert_block name="origin" />
		<parent link="${parent}" />
		<child link="arm_base" />
	</joint>
	<link name="arm_base"/>

	<link name="camera_link" />
	<joint name="joint_camera" type="fixed">
		<parent link="arm_base" />
		<child link="camera_link" />
		<origin xyz="0.07 0.060 0.164" 
			    rpy="-0.04 1.004 -0.049" />
	</joint>    

	<!-- robot base start-->
	<xacro:include filename="$(find rm_description)/urdf/rm75.urdf.xacro" />
	<xacro:rm_robot prefix="arm_left_" 
	                parent="arm_base" 
					use_mock_hardware="${use_mock_hardware}"
					mock_sensor_commands="false"
					sim_gazebo_classic="false"
					sim_gazebo="false"
					use_calibration_board="false"
					simulation_controllers=""
					robot_ip="*************"
					kinematic_calibration_yaml_file="$(find papjia_ydsb_config)/description/config/arm_left_kin_cali.yaml"
					initial_values_yaml_file="$(find papjia_ydsb_config)/description/config/arm_left_init_values.yaml"
					>
		<origin xyz="0 0.052 0" 
			    rpy="-1.5708 -1.5708 0" />
	</xacro:rm_robot>
	<xacro:rm_robot prefix="arm_right_" 
	                parent="arm_base" 
					use_mock_hardware="${use_mock_hardware}"
					mock_sensor_commands="false"
					sim_gazebo_classic="false"
					sim_gazebo="false"
					use_calibration_board="false"
					simulation_controllers=""
					robot_ip="*************"
					kinematic_calibration_yaml_file="$(find papjia_ydsb_config)/description/config/arm_right_kin_cali.yaml"
					initial_values_yaml_file="$(find papjia_ydsb_config)/description/config/arm_right_init_values.yaml"
					>
		<origin xyz="0 -0.052 0" 
			    rpy="1.5708 -1.5708 0" />
	</xacro:rm_robot>
    
	<xacro:include filename="$(find papjia_ydsb_config)/description/urdf/hand/right.macro.xacro" />
	<xacro:include filename="$(find papjia_ydsb_config)/description/urdf/hand/gripper.xacro" />
	<xacro:gripper prefix="arm_left_" parent="arm_left_link7">
		<origin xyz="0 0 0" rpy="0 0 0" />
	</xacro:gripper>
	<xacro:右手 prefix="arm_right_" parent="arm_right_link7">
		<origin xyz="0 0 0" rpy="0 3.1415926 -1.5707" />
	</xacro:右手>
</xacro:macro>

</robot>
