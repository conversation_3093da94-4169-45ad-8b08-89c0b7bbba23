<?xml version="1.0"?>
<robot xmlns:xacro="http://ros.org/wiki/xacro" name="robot">

	<xacro:arg name="use_mock_hardware" default="false" />

	<link name="workspace" />
    <link name="world" />

	<joint name="joint_world" type="fixed">
		<parent link="world" />
		<child link="workspace" />
		<origin xyz="0.167 -0.000 -0.412" rpy="0 0 0" />
	</joint>

	<xacro:include filename="$(find papjia_ydsb_config)/description/urdf/workstation.xacro" />
	<xacro:include filename="$(find papjia_ydsb_config)/description/urdf/robot.xacro" />

	<xacro:robot parent="world" use_mock_hardware="$(arg use_mock_hardware)">
		<origin xyz="0 0 0" rpy="0 0 0" />
	</xacro:robot>
</robot>
