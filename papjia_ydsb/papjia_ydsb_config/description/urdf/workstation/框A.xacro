<?xml version="1.0" encoding="UTF-8"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
	<xacro:macro name="框A" params="parent *origin">
		<!-- 合并后的框A -->
		<link name="box_a">
			<!-- 前侧面 -->
			<visual>
				<origin xyz="0 0.2125 0.11" rpy="0 0 0"/>
				<geometry>
					<box size="0.300 0.02 0.200"/>
				</geometry>
				<material name="blue">
					<color rgba="0 0 0.8 1"/>
				</material>
			</visual>
			
			<!-- 后侧面 -->
			<visual>
				<origin xyz="0 -0.2125 0.11" rpy="0 0 0"/>
				<geometry>
					<box size="0.300 0.02 0.200"/>
				</geometry>
				<material name="blue">
					<color rgba="0 0 0.8 1"/>
				</material>
			</visual>
			
			<!-- 左侧面 -->
			<visual>
				<origin xyz="-0.15 0 0.11" rpy="0 0 0"/>
				<geometry>
					<box size="0.02 0.425 0.200"/>
				</geometry>
				<material name="blue">
					<color rgba="0 0 0.8 1"/>
				</material>
			</visual>
			
			<!-- 右侧面 -->
			<visual>
				<origin xyz="0.15 0 0.11" rpy="0 0 0"/>
				<geometry>
					<box size="0.02 0.425 0.200"/>
				</geometry>
				<material name="blue">
					<color rgba="0 0 0.8 1"/>
				</material>
			</visual>
		</link>
		
		<!-- 关节连接 -->
		<joint name="joint_box_a" type="fixed">
			<xacro:insert_block name="origin" />
			<parent link="${parent}" />
			<child link="box_a" />
		</joint>
	</xacro:macro>
</robot> 