<?xml version="1.0" encoding="UTF-8"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
	<xacro:macro name="框B" params="parent *origin">
		<!-- 底面 -->
		<link name="box_b_bottom">
			<visual>
				<origin xyz="0 0 0.01" rpy="0 0 0"/>
				<geometry>
					<box size="0.350 0.255 0.02"/>
				</geometry>
				<material name="green">
					<color rgba="0 0.8 0 1"/>
				</material>
			</visual>
			<collision>
				<origin xyz="0 0 0.01" rpy="0 0 0"/>
				<geometry>
					<box size="0.350 0.255 0.02"/>
				</geometry>
			</collision>
		</link>
		
		<!-- 前侧面 -->
		<link name="box_b_front">
			<visual>
				<origin xyz="0 0.1175 0.0425" rpy="0 0 0"/>
				<geometry>
					<box size="0.350 0.02 0.085"/>
				</geometry>
				<material name="green">
					<color rgba="0 0.8 0 1"/>
				</material>
			</visual>
			<collision>
				<origin xyz="0 0.1175 0.0425" rpy="0 0 0"/>
				<geometry>
					<box size="0.350 0.02 0.085"/>
				</geometry>
			</collision>
		</link>
		
		<!-- 后侧面 -->
		<link name="box_b_back">
			<visual>
				<origin xyz="0 -0.1175 0.0425" rpy="0 0 0"/>
				<geometry>
					<box size="0.350 0.02 0.085"/>
				</geometry>
				<material name="green">
					<color rgba="0 0.8 0 1"/>
				</material>
			</visual>
			<collision>
				<origin xyz="0 -0.1175 0.0425" rpy="0 0 0"/>
				<geometry>
					<box size="0.350 0.02 0.085"/>
				</geometry>
			</collision>
		</link>
		
		<!-- 左侧面 -->
		<link name="box_b_left">
			<visual>
				<origin xyz="-0.165 0 0.0425" rpy="0 0 0"/>
				<geometry>
					<box size="0.02 0.255 0.085"/>
				</geometry>
				<material name="green">
					<color rgba="0 0.8 0 1"/>
				</material>
			</visual>
			<collision>
				<origin xyz="-0.165 0 0.0425" rpy="0 0 0"/>
				<geometry>
					<box size="0.02 0.255 0.085"/>
				</geometry>
			</collision>
		</link>
		
		<!-- 右侧面 -->
		<link name="box_b_right">
			<visual>
				<origin xyz="0.165 0 0.0425" rpy="0 0 0"/>
				<geometry>
					<box size="0.02 0.255 0.085"/>
				</geometry>
				<material name="green">
					<color rgba="0 0.8 0 1"/>
				</material>
			</visual>
			<collision>
				<origin xyz="0.165 0 0.0425" rpy="0 0 0"/>
				<geometry>
					<box size="0.02 0.255 0.085"/>
				</geometry>
			</collision>
		</link>
		
		<!-- 关节连接 -->
		<joint name="joint_box_b_bottom" type="fixed">
			<xacro:insert_block name="origin" />
			<parent link="${parent}" />
			<child link="box_b_bottom" />
		</joint>
		
		<joint name="joint_box_b_front" type="fixed">
			<parent link="box_b_bottom" />
			<child link="box_b_front" />
		</joint>
		
		<joint name="joint_box_b_back" type="fixed">
			<parent link="box_b_bottom" />
			<child link="box_b_back" />
		</joint>
		
		<joint name="joint_box_b_left" type="fixed">
			<parent link="box_b_bottom" />
			<child link="box_b_left" />
		</joint>
		
		<joint name="joint_box_b_right" type="fixed">
			<parent link="box_b_bottom" />
			<child link="box_b_right" />
		</joint>
	</xacro:macro>
</robot> 