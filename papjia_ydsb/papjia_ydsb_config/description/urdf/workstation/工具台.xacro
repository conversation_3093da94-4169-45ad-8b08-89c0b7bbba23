<?xml version="1.0" encoding="UTF-8"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
	<xacro:macro name="工具台" params="parent *origin">
		<link name="plane">
			<visual>
				<origin xyz="0.25 0 -0.005" rpy="0 0 0"/>
				<geometry>
					<box size="0.5 1.0 0.01"/>
				</geometry>
			</visual>
			<collision>
				<origin xyz="0.25 0 -0.005" rpy="0 0 0"/>
				<geometry>
					<box size="0.5 1.0 0.01"/>
				</geometry>
			</collision>
		</link>
		<joint name="joint_plane" type="fixed">
			<xacro:insert_block name="origin" />
			<parent link="${parent}" />
			<child link="plane" />
		</joint>
	</xacro:macro>
</robot>
