from dataclasses import dataclass
from enum import IntEnum, auto
import time
from typing import List, Optional
import logging
from pymodbus.client import ModbusTcpClient
from pymodbus.exceptions import ModbusException

class InitStatus(IntEnum):
    """夹具初始化状态枚举类。"""
    NOT_INITIALIZED = 0  # 未初始化
    FINISHED = 1         # 初始化完成
    INITIALIZING = 2     # 初始化中

class MovingStatus(IntEnum):
    """夹具运动状态枚举类。"""
    MOVING = 0      # 运动中
    ARRIVED = 1     # 到达位置
    GRIPPED = 2     # 夹住物体
    DROPPED = 3     # 物体掉落

class GripperStatus(IntEnum):
    """夹具状态枚举类。"""
    UNKNOWN = 0  # 未知
    OPENED = 1   # 打开
    CLOSED = 2   # 关闭

class ConnectionStatus(IntEnum):
    """连接状态枚举类。"""
    DISCONNECTED = 0  # 断开
    CONNECTING = 1    # 连接中
    CONNECTED = 2     # 已连接
    ERROR = 3         # 错误

class GripperError(IntEnum):
    """夹具错误状态枚举类。"""
    NO_ERROR = 0x00    # 无错误
    OVERHEAT = 0x04    # 过热
    OVERLOAD = 0x08    # 过载
    OVERSPEED = 0x11   # 过速

@dataclass
class GripperState:
    """存储夹具状态的数据类。
    
    Attributes:
        connection_status: 连接状态
        init_status: 初始化状态
        moving_status: 移动状态
        position: 当前位置
        gripper_status: 夹具状态
        error_code: 错误码
        rotate_position: 旋转位置
        rotate_init_status: 旋转初始化状态
        rotate_moving_status: 旋转移动状态
        error_message: 错误信息
    """
    connection_status: ConnectionStatus = ConnectionStatus.DISCONNECTED
    init_status: InitStatus = InitStatus.NOT_INITIALIZED
    moving_status: MovingStatus = MovingStatus.MOVING
    position: int = 0
    gripper_status: GripperStatus = GripperStatus.UNKNOWN
    error_code: GripperError = GripperError.NO_ERROR  # 添加错误码
    rotate_position: int = 0
    rotate_init_status: InitStatus = InitStatus.NOT_INITIALIZED
    rotate_moving_status: MovingStatus = MovingStatus.MOVING
    error_message: str = ""

class GripperDriver:
    """夹具控制驱动类。
    
    该类提供了通过Modbus TCP协议控制夹具的功能。
    
    Attributes:
        _client: ModbusTcp客户端实例
        _status: 夹具状态对象
    """
    
    def __init__(self, ip_address: str, port: int = 502, if_dk: bool = False) -> None:
        """初始化夹具驱动。
        
        Args:
            ip_address: Modbus服务器IP地址
            port: Modbus服务器端口号，默认为502
            
        Raises:
            ConnectionError: 连接失败时抛出
        """
        self.if_dk = if_dk
        self._status = GripperState()
        try:
            self._status.connection_status = ConnectionStatus.CONNECTING
            self._client = ModbusTcpClient(
                host=ip_address,
                port=port,
                timeout=1,
                retries=3
            )
            
            if not self._client.connect():
                self._status.connection_status = ConnectionStatus.ERROR
                self._status.error_message = f"无法连接到夹具 {ip_address}:{port}"
                raise ConnectionError(self._status.error_message)
                
            self._status.connection_status = ConnectionStatus.CONNECTED
                
        except ModbusException as e:
            self._status.connection_status = ConnectionStatus.ERROR
            self._status.error_message = f"连接夹具失败: {str(e)}"
            raise ConnectionError(self._status.error_message)
    
    def get_state(self) -> GripperState:
        """获取夹具状态。
        
        Returns:
            GripperState: 夹具状态对象
        """
        return self._status
    
    def get_error_message(self) -> str:
        """获取错误信息。
        
        Returns:
            str: 错误信息
        """
        return self._status.error_message
    
    def init(self) -> None:
        """初始化夹具。
        
        Raises:
            RuntimeError: 如果初始化超时（超过10秒）则抛出异常
        """
        self.write_register(0x0100, [0x01])
        print ("1")
        time.sleep(1)
        if self.if_dk:
            self.write_register(0x0101, [0x01])
        time.sleep(1)
        for _ in range(10):
            self.update_status()
            if self.is_inited():
                return
            time.sleep(1)
            
        raise RuntimeError("夹具初始化失败")
    
    def write_register(self, address: int, values: List[int]) -> None:
        """写入寄存器。
        
        Args:
            address: 寄存器地址
            values: 要写入的值列表
            
        Raises:
            ModbusException: 通信错误时抛出
        """
        try:
            response = self._client.write_registers(
                address=address,
                values=values,
                slave=1  # 默认从站地址
            )
            if response.isError():
                raise ModbusException(f"写入寄存器失败: {response}")
        except Exception as e:
            raise ModbusException(f"写入寄存器时发生错误: {str(e)}")
    
    def read_register(self, address: int, count: int) -> List[int]:
        """读取寄存器。
        
        Args:
            address: 寄存器地址
            count: 要读取的寄存器数量
            
        Returns:
            包含读取值的列表
            
        Raises:
            ModbusException: 通信错误时抛出
        """
        try:
            response = self._client.read_holding_registers(
                address=address,
                count=count,
                slave=1  # 默认从站地址
            )
            if response.isError():
                raise ModbusException(f"读取寄存器失败: {response}")
            return response.registers
        except Exception as e:
            raise ModbusException(f"读取寄存器时发生错误: {str(e)}")
    
    def move(self, force: int, speed: int, position: int) -> None:
        """控制夹具移动。
        
        Args:
            force: 力度值
            speed: 速度值
            position: 目标位置
        """
        self.write_register(0x0101, [force, 0x00, position, speed])
        
    def rotate(self, force: int, speed: int, angle: int) -> None:
        """控制夹具旋转。
        
        Args:
            force: 力度值
            speed: 速度值
            angle: 旋转角度
        """
        angle_to_send = angle & 0xFFFF  # 转换为无符号16位整数
        self.write_register(0x0105, [angle_to_send, 0x00, speed, force])
        
    def update_status(self) -> None:
        """更新夹具状态。"""
        status = self.read_register(0x0200, 12)
        if len(status) == 12:
            try:
                self._status.init_status = InitStatus(status[0])
                self._status.moving_status = MovingStatus(status[1])
                self._status.position = status[2]
                self._status.gripper_status = GripperStatus(status[5])
                
                # 读取错误码 (0x05地址)
                error_code = status[5]  # 假设错误码在第6个寄存器
                self._status.error_code = GripperError(error_code)
                
                # 根据错误码设置错误信息
                error_messages = {
                    GripperError.NO_ERROR: "正常",
                    GripperError.OVERHEAT: "夹爪过热",
                    GripperError.OVERLOAD: "夹爪过载",
                    GripperError.OVERSPEED: "夹爪过速"
                }
                self._status.error_message = error_messages.get(self._status.error_code, "未知错误")
                
                self._status.rotate_position = status[8]
                self._status.rotate_init_status = InitStatus(status[10])
                self._status.rotate_moving_status = MovingStatus(status[11])
                
            except ValueError as e:
                logging.error(f"无效的状态值: {str(e)}")
                # 设置为未知状态
                if "MovingStatus" in str(e):
                    self._status.moving_status = MovingStatus.MOVING
                if "InitStatus" in str(e):
                    self._status.init_status = InitStatus.NOT_INITIALIZED
                if "GripperError" in str(e):
                    self._status.error_code = GripperError.NO_ERROR
                    self._status.error_message = "错误码解析失败"
            
    def is_connected(self) -> bool:
        """检查是否已连接。
        
        Returns:
            bool: 连接状态
        """
        try:
            if self._client is None:
                return False
            
            if not self._client.connected:
                return self._client.connect()
                
            # 尝试读取一个寄存器来验证连接
            response = self._client.read_holding_registers(
                address=0x0200,
                count=1,
                slave=1
            )
            return not response.isError()
            
        except Exception:
            return False
    
    def is_inited(self) -> bool:
        """检查是否已初始化。
        
        Returns:
            bool: 初始化状态
        """
        return self._status.init_status == InitStatus.FINISHED
        
    def is_moving(self) -> bool:
        """检查是否正在移动。
        
        Returns:
            bool: 移动状态
        """
        return self._status.moving_status == MovingStatus.MOVING
        
    def is_rotate_inited(self) -> bool:
        """检查旋转功能是否已初始化。
        
        Returns:
            bool: 旋转初始化状态
        """
        return self._status.rotate_init_status == InitStatus.FINISHED
        
    def is_rotate_moving(self) -> bool:
        """检查是否正在旋转。
        
        Returns:
            bool: 旋转状态
        """
        return self._status.rotate_moving_status == MovingStatus.MOVING
        
    def get_position(self) -> int:
        """获取当前位置。
        
        Returns:
            int: 当前位置值
        """
        return self._status.position
        
    def get_rotate_position(self) -> int:
        """获取当前旋转位置。
        
        Returns:
            int: 当前旋转位置值
        """
        return self._status.rotate_position 
        
    def close(self) -> None:
        """关闭连接。"""
        if self._client and self._client.connected:
            self._client.close() 
