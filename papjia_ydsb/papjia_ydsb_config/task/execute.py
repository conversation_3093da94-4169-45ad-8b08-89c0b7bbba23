import rclpy
import time
from rclpy.node import Node
from rclpy.action import ActionClient
from geometry_msgs.msg import PoseStamped
from opennav_docking_msgs.action import DockRobot, UndockRobot
from papjia_behavior_interface.action import ExecuteTree
from gripper_control import <PERSON><PERSON>per<PERSON><PERSON>, <PERSON>ripper<PERSON>rror
from dk_gripper_control import MotorDriver
from skills.pipette import <PERSON>petteBehavior
from papjia_skill.bt import *


class PapjiaSkillExecutor(Node):
    def __init__(self, node_name="papjia_task_executor"):
        super().__init__(node_name)
        self.action_client = ActionClient(self, ExecuteTree, "execute_tree")
        self.behaviors = {}  # 存储行为树生成器实例

        # 导航功能相关
        self.nav_goal_publisher = self.create_publisher(PoseStamped, '/move_base_simple/goal', 10)
        
        # 对接功能相关
        self.docking_client = ActionClient(self, DockRobot, "dock_robot")
        self.undocking_client = ActionClient(self, UndockRobot, "undock_robot")
        
        # 夹具控制相关
        self.gripper_driver = None
        self.gripper_ip = "**************"
        self.gripper_port = 5001

        # 左爪控制相关（新增）
        self.left_gripper_driver = None
        self.left_gripper_ip = "**************"  # 左爪IP地址

        self.if_dk = False

    def register_behavior(self, name, behavior_class, *args, **kwargs):
        """注册行为树生成器."""
        self.behaviors[name] = behavior_class(*args, **kwargs)
        self.get_logger().info(f"已注册行为生成器: {name}")

    def generate_tree(self, behavior_name, task_name):
        """生成行为树."""
        if behavior_name not in self.behaviors:
            raise ValueError(f"未找到行为生成器: {behavior_name}")

        behavior = self.behaviors[behavior_name]
        if not hasattr(behavior, task_name):
            raise ValueError(f"未定义的任务: {task_name}")

        tree_method = getattr(behavior, task_name)
        tree = tree_method()
        # self.get_logger().info(f"成功生成行为树: {tree.action_name}")
        return tree

    def execute_tree(self, tree_string, tree_name):
        """执行行为树."""
        if not self.action_client.wait_for_server(timeout_sec=3.0):
            self.get_logger().error("Action server 未启动.")
            return

        goal_msg = ExecuteTree.Goal()
        goal_msg.tree_string = tree_string
        goal_msg.tree_type = ExecuteTree.Goal.STRING  # 树类型为字符串
        goal_msg.tree_name = tree_name

        self.get_logger().info(f"发送行为树任务: {tree_name}")
        future = self.action_client.send_goal_async(goal_msg, self.feedback_callback)
        rclpy.spin_until_future_complete(self, future)

        goal_handle = future.result()
        if not goal_handle.accepted:
            self.get_logger().error("任务被拒绝.")
            return

        result_future = goal_handle.get_result_async()
        rclpy.spin_until_future_complete(self, result_future)

        result = result_future.result().result
        self.get_logger().info(f"任务完成. 成功: {result.success}")

    def feedback_callback(self, feedback_msg):
        feedback = feedback_msg.feedback
        self.get_logger().info(f"反馈 - 当前状态: {feedback.current_status}")
    
    def execute(self, behavior_name, task_name, tree_name):
        tree = self.generate_tree(behavior_name, task_name)
        
        root = BehaviorRoot()
        root.add_child(tree)
        self.execute_tree(tree_string=str(root), tree_name=tree_name)

    # ========== 新增功能函数 ==========
    def execute_nav(self, target_pose):
        """执行导航到目标点"""
        if not isinstance(target_pose, PoseStamped):
            raise ValueError("目标点必须是PoseStamped类型")
        
        self.nav_goal_publisher.publish(target_pose)
        self.get_logger().info(f"已发布导航目标到 {target_pose.pose.position}")

    def execute_docking(self, dock_pose, dock_type):
        """执行对接操作"""
        if not self.docking_client.wait_for_server(timeout_sec=5.0):
            self.get_logger().error("对接服务不可用")
            return False

        goal_msg = DockRobot.Goal()
        goal_msg.use_dock_id = False
        goal_msg.dock_pose = dock_pose
        goal_msg.dock_type = dock_type

        future = self.docking_client.send_goal_async(goal_msg)
        rclpy.spin_until_future_complete(self, future)
        
        if not future.result().accepted:
            self.get_logger().error("对接请求被拒绝")
            return False
        
        result_future = future.result().get_result_async()
        rclpy.spin_until_future_complete(self, result_future)
        return result_future.result().result.success

    def execute_undocking(self, dock_type):
        """执行脱离对接操作"""
        if not self.undocking_client.wait_for_server(timeout_sec=5.0):
            self.get_logger().error("脱离对接服务不可用")
            return False

        goal_msg = UndockRobot.Goal()
        goal_msg.dock_type = dock_type

        future = self.undocking_client.send_goal_async(goal_msg)
        rclpy.spin_until_future_complete(self, future)
        
        if not future.result().accepted:
            self.get_logger().error("脱离请求被拒绝")
            return False
        
        result_future = future.result().get_result_async()
        rclpy.spin_until_future_complete(self, result_future)
        return result_future.result().result.success

    def execute_leftpaw(self, position):
        """控制左爪平移"""
        if position < 0 or position > 100:
            raise ValueError("位置值必须在0-100%之间")

        try:
            if not self.left_gripper_driver:
                self.left_gripper_driver = MotorDriver(ip_address=self.left_gripper_ip)
                
                # 初始化电爪和旋转模块
                if not self.left_gripper_driver.init_gripper():
                    raise Exception("电爪初始化命令发送失败")
                if not self.left_gripper_driver.wait_for_gripper_init():
                    raise Exception("电爪初始化失败")
                
                if not self.left_gripper_driver.init_rotation():
                    raise Exception("旋转初始化命令发送失败")
                if not self.left_gripper_driver.wait_for_rotation_init():
                    raise Exception("旋转初始化失败")
                
                # if not self.left_gripper_driver.set_gripper_speed(90):
                #     raise Exception("点爪设置速度失败")

            # 设置并运行电爪
            if not self.left_gripper_driver.set_gripper_position_and_run(position):
                raise Exception("位置设置失败")
            
            # 等待运动完成
            status = self.left_gripper_driver.wait_for_gripper_completion()
            if status == 1:
                self.get_logger().info(f"左爪已移动到{position}%位置")
                return True
            elif status == 2:
                self.get_logger().error("夹持中（可能物体卡住）")
                return True
            return False
            
        except Exception as e:
            self.get_logger().error(f"左爪控制失败: {str(e)}")
            return False
        

    def execute_leftrotate(self, angle):
        """控制左爪旋转"""
        if not -32768 <= angle <= 32767:
            raise ValueError("角度值超出有效范围")

        try:
            if not self.left_gripper_driver:
                self.left_gripper_driver = MotorDriver(ip_address=self.left_gripper_ip)
                
                # 初始化电爪和旋转模块（同平移）
                if not self.left_gripper_driver.init_gripper():
                    raise Exception("电爪初始化命令发送失败")
                if not self.left_gripper_driver.wait_for_gripper_init():
                    raise Exception("电爪初始化失败")
                
                if not self.left_gripper_driver.init_rotation():
                    raise Exception("旋转初始化命令发送失败")
                if not self.left_gripper_driver.wait_for_rotation_init():
                    raise Exception("旋转初始化失败")
                if not self.left_gripper_driver.set_rotation_speed(30):
                    raise Exception("旋转设置速度失败")

            # 设置并运行旋转
            if not self.left_gripper_driver.set_rotation_angle_and_run(angle):
                raise Exception("角度设置失败")
            
            # 等待运动完成
            status = self.left_gripper_driver.wait_for_rotation_completion()
            if status == 1:
                self.get_logger().info(f"左爪已旋转至{angle}度")
                return True
            elif status == 3:
                self.get_logger().error("旋转堵转")
                return False
            return False
            
        except Exception as e:
            self.get_logger().error(f"左旋转控制失败: {str(e)}")
            return False
        

    def execute_rightpaw(self, position):
        """控制右爪位置"""
        if position < 0 or position > 1000:
            raise ValueError("位置值必须在0-1000之间")

        try:
            if not self.gripper_driver:
                self.gripper_driver = GripperDriver(
                    self.gripper_ip, 
                    self.gripper_port,
                    self.if_dk
                )
                self.gripper_driver.init()
            
            self.gripper_driver.move(50, 100, position)
            
            while True:
                self.gripper_driver.update_status()
                if not self.gripper_driver.is_moving():
                    break
                time.sleep(0.1)
            
            self.get_logger().info(f"右爪已移动到位置: {position}")
            return True
        except Exception as e:
            self.get_logger().error(f"夹具控制失败: {str(e)}")
            return False

    def destroy_node(self):
        if self.gripper_driver:
            self.gripper_driver.close()
        super().destroy_node()

def main(args=None):
    rclpy.init(args=args)
    executor = PapjiaSkillExecutor()

    try:
        executor.execute_leftpaw(0)
        # input()
        executor.execute_leftpaw(100)
        # input()
        executor.execute_leftrotate(100)
        executor.execute_leftrotate(0)
        executor.execute_rightpaw(0)
        executor.register_behavior("pipette", PipetteBehavior, "pipette1")

        # # # executor.execute_rightpaw(240)
        # # input()
        executor.execute("pipette", "fold", "fold_tube")
        input("fold ok")
        # # # # 示例用法
        # # # # 1. 导航到目标点
        # # # # nav_goal = PoseStamped()
        # # # # nav_goal.header.frame_id = "map"
        # # # # nav_goal.pose.position.x = 3.0
        # # # # nav_goal.pose.position.y = 2.5
        # # # # nav_goal.pose.orientation.w = 1.0
        # # # # executor.execute_nav(nav_goal)
        # # # # input()
        # 2. 执行对接
        dock_pose = PoseStamped()
        dock_pose.header.frame_id = "map"
        dock_pose.pose.position.x = 0.617
        dock_pose.pose.position.y = 0.120
        dock_pose.pose.orientation.z = -0.848
        dock_pose.pose.orientation.w = 0.530
        executor.execute_docking(dock_pose, "papjia_car_dock")
        input()

        # # 4. 控制右爪
        # # # input()
        # # # 移液枪\
        # # # executor.execute("pipette", "recover", "recover_tube")

        executor.execute("pipette", "unfold", "unfold_tube")
        executor.execute_rightpaw(0)
        executor.execute("pipette", "pickup_pre", "pickup_pre_tube")
        executor.execute_rightpaw(700)
        executor.execute("pipette", "pickup", "pickup_tube")
        time.sleep(3)
        executor.execute_rightpaw(250)
        executor.execute("pipette", "pickup_after", "pickup_after_tube")
        executor.execute("pipette", "preopen", "preopen_tube")
        executor.execute_leftpaw(0)
        executor.execute("pipette", "open", "open_tube")
        time.sleep(3)
        executor.execute_leftpaw(100)
        time.sleep(1)
        executor.execute_leftrotate(-500)
        executor.execute("pipette", "afteropen", "afteropen_tube")
        executor.execute("pipette", "pull", "pull_tube")
        executor.execute("pipette", "preclose", "preclose_tube")
        executor.execute("pipette", "close", "close_tube")
        time.sleep(3)
        executor.execute_leftpaw(90)

        # executor.execute_leftrotate(-500)        
        executor.execute_leftrotate(800)
        executor.execute_leftpaw(0)
        executor.execute_leftpaw(90)
        executor.execute_leftrotate(1000)
        executor.execute_leftpaw(0)
        executor.execute_leftpaw(90)
        executor.execute_leftrotate(1300)
        executor.execute_rightpaw(700)
        executor.execute("pipette", "preplace", "preplace_tube")
        # input()
        executor.execute_leftpaw(0)
        executor.execute_leftrotate(0)
        # input()
        executor.execute("pipette", "afterplace", "afterplace_tube")
        # input()
        time.sleep(3)
        executor.execute_leftpaw(100)
        executor.execute("pipette", "aftergetshelf", "aftergetshelf_tube")
        # input()

        # # executor.execute("pipette", "fold_left", "fold_left_tube")

        executor.execute_undocking("papjia_car_dock")
        # 2. 执行对接
        dock_pose = PoseStamped()
        dock_pose.header.frame_id = "map"
        dock_pose.pose.position.x = -1.333
        dock_pose.pose.position.y = 0.924
        dock_pose.pose.orientation.z = -0.853
        dock_pose.pose.orientation.w = 0.522
        executor.execute_docking(dock_pose, "papjia_car_dock")

        executor.execute("pipette", "placeshelf", "placeshelf_tube")
        # input()
        time.sleep(2)
        executor.execute_leftpaw(0)
        # input()
        executor.execute("pipette", "afterplaceshelf", "afterplaceshelf_tube")
        # input()
        time.sleep(2)
        executor.execute_leftpaw(100)
        # # executor.execute("pipette", "place_pre", "place_pre_tube")
        # # input()
        # # executor.execute_rightpaw(700)
        # # executor.execute_rightpaw(250)
        # # executor.execute("pipette", "fold_right", "fold_right_tube")
        # # executor.execute_rightpaw(0)

        # # # executor.execute("pipette", "preopen", "preopen_tube")

        # # # executor.execute("pipette", "open", "open_tube")
        
        # # executor.execute("pipette", "recover", "recover_tube")
        # # executor.execute("pipette", "fold", "fold_tube")
    
        # 3. 执行脱离对接
        executor.execute_undocking("papjia_car_dock")
        # # input()
        # # executor.execute("pipette", "preclose", "preclose_tube")
        # # executor.execute("pipette", "close", "close_tube")
        # # executor.execute("pipette", "place", "place_tube")
        # # executor.execute("pipette", "recover", "recover_tube")

    except ValueError as e:
        executor.get_logger().error(f"错误: {e}")
    except Exception as e:
        executor.get_logger().error(f"未知错误: {e}")
    finally:
        executor.destroy_node()
        rclpy.shutdown()


if __name__ == "__main__":
    main()
