from papjia_skill.primitive.arm_move import JointWaypoint, CartWaypoint, arm_move_primitive_bt
from papjia_skill.bt import BehaviorTree, Sequence


class PipetteBehavior(object):

    def __init__(self, pipette_id):
        self.pipette_id = "tube"
        self.pipette_open_id = "tube_up"
        self.align_distance = 0.08
        self.grasp_offset = 0.04
        self.cup_offset = 0
        self.lift_distance = 0.08
        self.ik_frame = 'arm_right_grasp'
        self.arm_group = 'arm_right'
        self.arm_group_l = 'arm_left'
        self.ik_frame_l = 'arm_left_grasp'
        
        self.max_velocity_scaling_factor = 0.1
        self.max_acceleration_scaling_factor = 0.1
        self.waypoints = {}

        self.waypoints['左折叠点'] = JointWaypoint(
            stage_name='左折叠点',
            joint_state_name="fold_left",
            group=self.arm_group_l,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['右折叠点'] = JointWaypoint(
            stage_name='右折叠点',
            joint_state_name="fold_right",
            group=self.arm_group,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['初始点'] = JointWaypoint(
            stage_name='初始点',
            joint_state_name="home_right",
            group=self.arm_group,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['左初始点'] = JointWaypoint(
            stage_name='左初始点',
            joint_state_name="home_left",
            group=self.arm_group_l,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['初始点2'] = JointWaypoint(
            stage_name='初始点2',
            joint_state_name="fetch_cup_init_right",
            group=self.arm_group,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['左初始点2'] = JointWaypoint(
            stage_name='左初始点2',
            joint_state_name="fetch_cup_init_left",
            group=self.arm_group_l,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['预抓点'] = CartWaypoint(
            stage_name="预抓点",
            position=[0.0, 0.0, self.align_distance],
            # position=[0.0, 0.0, 0.0],
            orientation=[0., 0., -0.382, 0.923],
            group=self.arm_group,
            planner='lin',
            ik_frame=self.ik_frame,
            frame_id=self.pipette_id,
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )



        self.waypoints['抓取点'] = CartWaypoint(
            stage_name="抓取点",
            position=[0.0, 0.0, -self.grasp_offset],
            orientation=[0., 0., -0.382, 0.923],
            group=self.arm_group,
            planner='lin',
            ik_frame=self.ik_frame,
            frame_id=self.pipette_id,
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )



        self.waypoints['提起点'] = CartWaypoint(
            stage_name="提起点",
            position=[0.0, 0.0, self.lift_distance - self.grasp_offset],
            orientation=[0., 0., -0.382, 0.923],
            group=self.arm_group,
            planner='lin',
            ik_frame=self.ik_frame,
            frame_id=self.pipette_id,
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        # self.waypoints['开盖点'] = CartWaypoint(
        #     stage_name="提起来",
        #     position=[0, 0.0, 0.0],
        #     orientation=[0., 0., 0, 1],
        #     group=self.arm_group,
        #     planner='ptp',
        #     ik_frame=self.ik_frame,
        #     frame_id=self.pipette_open_id,
        #     max_velocity_scaling_factor=self.max_velocity_scaling_factor,
        #     max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        # )


        self.waypoints['开盖点'] = JointWaypoint(
            stage_name='开盖点',
            joint_state_name="cup_right",
            group=self.arm_group,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        # self.waypoints['左提起点'] = CartWaypoint(
        #     stage_name="提起来",
        #     position=[0.0, 0.0, self.lift_distance + 0.05],
        #     orientation=[0., 0., 0., 1.],
        #     group=self.arm_group_l,
        #     planner='lin',
        #     ik_frame=self.ik_frame_l,
        #     frame_id=self.pipette_id,
        #     max_velocity_scaling_factor=0.1,
        #     max_acceleration_scaling_factor=0.1,
        # ) 

        self.waypoints['左预抓点'] = CartWaypoint(
            stage_name="左预抓点",
            position=[0.05, 0.2, 0.16],
            orientation=[0., 0., 0.707, 0.707],
            group=self.arm_group_l,
            planner='lin',
            ik_frame=self.ik_frame_l,
            frame_id=self.pipette_open_id,
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )
        self.waypoints['左准备点'] = CartWaypoint(
            stage_name="左准备点",
            position=[-0.01, 0.0, 0.06],
            orientation=[0., 0., 0.707, 0.707],
            group=self.arm_group_l,
            planner='ptp',
            ik_frame=self.ik_frame_l,
            frame_id="arm_right_grasp",
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['左抓取点'] = CartWaypoint(
            stage_name="左抓取点",
            position=[-0.01, 0.001, 0.01],
            orientation=[0., 0., 0.707, 0.707],
            group=self.arm_group_l,
            planner='lin',
            ik_frame=self.ik_frame_l,
            frame_id="arm_right_grasp",
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['左放置点'] = CartWaypoint(
            stage_name="左放置点",
            position=[-0.01, 0.001, 0.01],
            orientation=[0., 0., 0.707, 0.707],
            group=self.arm_group_l,
            planner='lin',
            ik_frame=self.ik_frame_l,
            frame_id="arm_right_grasp",
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['左抬起点'] = CartWaypoint(
            stage_name="左抬起点",
            position=[-0.01, 0.001, 0.06],
            orientation=[0., 0., 0.707, 0.707],
            group=self.arm_group_l,
            planner='lin',
            ik_frame=self.ik_frame_l,
            frame_id="arm_right_grasp",
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['左二次准备点'] = CartWaypoint(
            stage_name="左二次准备点",
            position=[0.05, 0.2, 0.16],
            orientation=[0., 0., 0.707, 0.707],
            group=self.arm_group_l,
            planner='ptp',
            ik_frame=self.ik_frame_l,
            frame_id=self.pipette_open_id,
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['预备倾倒点'] = JointWaypoint(
            stage_name='预备倾倒点',
            joint_state_name="plane_pre_right",
            group=self.arm_group,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['倾倒准备'] = JointWaypoint(
            stage_name='倾倒准备',
            joint_state_name="plane_ready_right",
            group=self.arm_group,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['倾倒执行'] = JointWaypoint(
            stage_name='倾倒执行',
            joint_state_name="plane_execute_right",
            group=self.arm_group,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['倾倒收回'] = JointWaypoint(
            stage_name='倾倒收回',
            joint_state_name="plane_ready_right",
            group=self.arm_group,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['倾倒撤退'] = JointWaypoint(
            stage_name='倾倒撤退',
            joint_state_name="plane_pre_right",
            group=self.arm_group,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['右臂撤退'] = CartWaypoint(
            stage_name="右臂撤退",
            position=[0.0, -0.15, 0],
            orientation=[0., 0., 0, 1],
            group=self.arm_group,
            planner='lin',
            ik_frame=self.ik_frame,
            frame_id=self.ik_frame,
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['左放管准备点'] = CartWaypoint(
            stage_name="左放管准备点",
            position=[0.197, 0.71, 0.30],
            orientation=[0., 0., 0.707, 0.707],
            group=self.arm_group_l,
            planner='ptp',
            ik_frame=self.ik_frame_l,
            frame_id="workspace",
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['左放管点'] = CartWaypoint(
            stage_name="左放管点",
            position=[0.197, 0.71, 0.14],
            orientation=[0., 0., 0.707, 0.707],
            group=self.arm_group_l,
            planner='lin',
            ik_frame=self.ik_frame_l,
            frame_id="workspace",
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['左放管抬起点'] = CartWaypoint(
            stage_name="左放管抬起点",
            position=[0.197, 0.71, 0.3],
            orientation=[0., 0., 0.707, 0.707],
            group=self.arm_group_l,
            planner='lin',
            ik_frame=self.ik_frame_l,
            frame_id="workspace",
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['拿架子准备'] = JointWaypoint(
            stage_name='拿架子准备',
            joint_state_name="grasp_shelf_pre_left",
            group=self.arm_group_l,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )
        self.waypoints['拿架子准备2'] = JointWaypoint(
            stage_name='拿架子准备',
            joint_state_name="grasp_shelf_pre2_left",
            group=self.arm_group_l,
            planner='ptp',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['拿架子就绪'] = CartWaypoint(
            stage_name='拿架子就绪',
            position=[-0.292, 0.414, 0.151],
            orientation=[-0.001, -0.013, 1.000, 0.010],
            group=self.arm_group_l,
            planner='lin',
            ik_frame=self.ik_frame_l,
            frame_id="arm_left_base_link",
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['拿架子修正'] = CartWaypoint(
            stage_name='拿架子修正',
            position=[0.004, 0., 0.],
            orientation=[0., 0., 0., 1.],
            group=self.arm_group_l,
            planner='lin',
            ik_frame=self.ik_frame_l,
            frame_id="arm_left_grasp",
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['拿架子抬起'] = JointWaypoint(
            stage_name='拿架子抬起',
            joint_state_name="grasp_shelf_lift_left",
            group=self.arm_group_l,
            planner='lin',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )
        self.waypoints['拿架子折叠'] = JointWaypoint(
            stage_name='拿架子折叠',
            joint_state_name="grasp_shelf_fold_left",
            group=self.arm_group_l,
            planner='lin',
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['放架子修正'] = CartWaypoint(
            stage_name='放架子修正',
            position=[0.02, 0., 0.],
            orientation=[0., 0., 0., 1.],
            group=self.arm_group_l,
            planner='lin',
            ik_frame=self.ik_frame_l,
            frame_id="arm_left_grasp",
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

        self.waypoints['放架子撤退'] = CartWaypoint(
            stage_name='放架子撤退',
            position=[0., 0., 0.08],
            orientation=[0., 0., 0., 1.],
            group=self.arm_group_l,
            planner='lin',
            ik_frame=self.ik_frame_l,
            frame_id="arm_left_grasp",
            max_velocity_scaling_factor=self.max_velocity_scaling_factor,
            max_acceleration_scaling_factor=self.max_acceleration_scaling_factor,
        )

    def unfold(self):
        action_name = "unfold_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)

        sequence.add_child(
            arm_move_primitive_bt("unfold", [
            self.waypoints['左初始点'],
        ])) 
        sequence.add_child(
            arm_move_primitive_bt("unfold", [
            self.waypoints['初始点'],
        ])) 
        return tree

    def fold(self):
        action_name = "fold_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)

        sequence.add_child(
            arm_move_primitive_bt("fold", [
            self.waypoints['左折叠点'],
        ])) 
        sequence.add_child(
            arm_move_primitive_bt("fold", [
            self.waypoints['右折叠点'],
        ])) 
        return tree

    def fold_left(self):
        action_name = "fold_left_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)

        sequence.add_child(
            arm_move_primitive_bt("fold", [
            self.waypoints['左折叠点'],
        ])) 
        return tree

    def fold_right(self):
        action_name = "fold_right_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)

        sequence.add_child(
            arm_move_primitive_bt("fold", [
            self.waypoints['右折叠点'],
        ])) 
        return tree


    def pickup_pre(self):
        """从架子上取移液枪
        
            从初始位置出发，对接移液枪，然后将移液枪提起来，回到初始位置
            要完成相应的判断，比如是否夹紧，移液枪型号是否正确
        """
        action_name = "pickup_pre_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)

        sequence.add_child(
            arm_move_primitive_bt("pickup_pre", [
            self.waypoints['初始点'],
            self.waypoints['初始点2'],
            # self.waypoints['预抓点'],
        ]))

        sequence.add_child(
            arm_move_primitive_bt("pickup_pre", [
            # self.waypoints['初始点'],
            # self.waypoints['初始点2'],
            self.waypoints['预抓点'],
        ]))
        return tree

    def pickup(self):
        action_name = "pickup_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)

        sequence.add_child(
            arm_move_primitive_bt("pickup", [
            self.waypoints['抓取点'],
        ]))
        return tree

    def pickup_after(self):
        action_name = "pickup_after_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)

        sequence.add_child(
            arm_move_primitive_bt("pickup", [
            self.waypoints['提起点'],
        ]))
        sequence.add_child(
            arm_move_primitive_bt("pickup", [
            self.waypoints['左预抓点'],

        ]))  
        sequence.add_child(
            arm_move_primitive_bt("pickup", [
            self.waypoints['开盖点'],

        ]))
       
        return tree

    def preopen(self):
        action_name = "preopen_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)
        
        sequence.add_child(
            arm_move_primitive_bt("open", [
            self.waypoints['左准备点'],
        ]))
        return tree

    def open(self):
        action_name = "open_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)

        sequence.add_child(
            arm_move_primitive_bt("open", [
            self.waypoints['左抓取点'],
        ]))
        return tree

    def afteropen(self):
        action_name = "afteropen_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)

        sequence.add_child(
            arm_move_primitive_bt("afteropen", [
            self.waypoints['左抬起点'],
            self.waypoints['左二次准备点'],

        ]))
        sequence.add_child(
            arm_move_primitive_bt("afteropen", [
            self.waypoints['预备倾倒点'],
        ]))

        return tree
    
    def pull(self):
        action_name = "pull_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)

        sequence.add_child(
            arm_move_primitive_bt("pull", [
            self.waypoints['倾倒准备'],
            self.waypoints['倾倒执行'],

        ]))

        sequence.add_child(
            arm_move_primitive_bt("pull", [
            self.waypoints['倾倒收回'],
            self.waypoints['倾倒撤退'],
        ]))
        return tree

    def preclose(self):
        action_name = "preclose_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)

        sequence.add_child(
            arm_move_primitive_bt("preclose", [
            self.waypoints['开盖点'],

        ]))
        sequence.add_child(
            arm_move_primitive_bt("preclose", [
            self.waypoints['左准备点'],

        ]))


        return tree

    def close(self):
        action_name = "close_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)
    
        sequence.add_child(
            arm_move_primitive_bt("preclose", [
            self.waypoints['左放置点'],

        ]))
        return tree
    
    def preplace(self):
        action_name = "preplace_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)
    
        sequence.add_child(
            arm_move_primitive_bt("preplace", [
            self.waypoints['右臂撤退'],
            self.waypoints['右折叠点'],
        ])) 
        
        sequence.add_child(
            arm_move_primitive_bt("preplace", [
            self.waypoints['左放管准备点'], 
        ]))    
        sequence.add_child(
            arm_move_primitive_bt("preplace", [
            self.waypoints['左放管点'], 
        ]))    
  
        return tree    
    
    def afterplace(self):
        action_name = "afterplace_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)
    
        sequence.add_child(
            arm_move_primitive_bt("afterplace", [
            self.waypoints['左放管抬起点'], 
        ]))        
        sequence.add_child(
            arm_move_primitive_bt("afterplace", [
            self.waypoints['拿架子准备'], 
        ]))        
        sequence.add_child(
            arm_move_primitive_bt("afterplace", [
            self.waypoints['拿架子准备2'], 
        ]))        
        sequence.add_child(
            arm_move_primitive_bt("afterplace", [
            self.waypoints['拿架子就绪'], 
        ]))
        # sequence.add_child(
        #     arm_move_primitive_bt("afterplace", [
        #     self.waypoints['拿架子修正'], 
        # ]))
        
        return tree  

    def aftergetshelf(self):
        action_name = "aftergetshelf_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)
        sequence.add_child(
            arm_move_primitive_bt("aftergetshelf", [
            self.waypoints['拿架子抬起'], 
        ]))        

        sequence.add_child(
            arm_move_primitive_bt("aftergetshelf", [
            self.waypoints['拿架子折叠'], 
        ]))        
   
        return tree 

         
    def placeshelf(self):
        action_name = "placeshelf_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)
        sequence.add_child(
            arm_move_primitive_bt("afterplace", [
            self.waypoints['拿架子抬起'], 
        ]))        
        sequence.add_child(
            arm_move_primitive_bt("afterplace", [
            self.waypoints['拿架子就绪'], 
        ]))    
        # sequence.add_child(
        #     arm_move_primitive_bt("afterplace", [
        #     self.waypoints['拿架子修正'], 
        # ]))   
        sequence.add_child(
            arm_move_primitive_bt("afterplace", [
            self.waypoints['放架子修正'], 
        ]))  
        return tree    
 
    def afterplaceshelf(self):
        action_name = "afterplaceshelf_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)
        sequence.add_child(
            arm_move_primitive_bt("afterplace", [
            self.waypoints['放架子撤退'], 
        ]))        
        sequence.add_child(
            arm_move_primitive_bt("afterplace", [
            self.waypoints['拿架子准备'], 
        ]))    
        sequence.add_child(
            arm_move_primitive_bt("afterplace", [
            self.waypoints['左折叠点'], 
        ]))       
        return tree 

    def recover(self):
        action_name = "recover_" + self.pipette_id
        tree = BehaviorTree(action_name)
        sequence = Sequence(action_name)
        tree.add_child(sequence)
        sequence.add_child(
            arm_move_primitive_bt("recover", [
            self.waypoints['初始点2'],
            self.waypoints['初始点'],
        ])) 
        sequence.add_child(
            arm_move_primitive_bt("recover", [
            self.waypoints['左初始点2'],
            self.waypoints['左初始点'],
        ])) 
        return tree     
   


    # def preclose(self):
    #     action_name = "preclose_" + self.pipette_id
    #     tree = BehaviorTree(action_name)
    #     sequence = Sequence(action_name)
    #     tree.add_child(sequence)

    #     sequence.add_child(
    #         arm_move_primitive_bt("recover", [
    #         self.waypoints['左初始点'],
    #         self.waypoints['左初始点2'],
    #     ])) 
    #     # 先直接提起点，后面需要产生随机误差来测试
    #     sequence.add_child(
    #         arm_move_primitive_bt("preclose", [
    #         self.waypoints['提起点'],
    #     ]))
        
    #     return tree

    # def close(self):
    #     action_name = "close_" + self.pipette_id
    #     tree = BehaviorTree(action_name)
    #     sequence = Sequence(action_name)
    #     tree.add_child(sequence)
    
    #     sequence.add_child(
    #         arm_move_primitive_bt("open", [
    #         self.waypoints['左预抓点'],
            
    #     ]))

    #     # 融差动作
    #     sequence.add_child(
    #         arm_move_primitive_bt("open", [
    #         self.waypoints['左抓取点'],
            
    #     ]))
    #     # 转转转
    #     # 可能伴随右臂动

    #     # 开左夹子
    #     sequence.add_child(
    #         arm_move_primitive_bt("open", [
    #         self.waypoints['左预抓点'],
    #     ]))    
    #     # 关左夹子    
    #     return tree
    
    # def place(self):
    #     action_name = "place_" + self.pipette_id
    #     tree = BehaviorTree(action_name)
    #     sequence = Sequence(action_name)
    #     tree.add_child(sequence)
        
    #     # Step: 对接移液枪
    #     sequence.add_child(
    #         arm_move_primitive_bt("pickup_pre", [
    #         self.waypoints['预抓点'],
    #         self.waypoints['抓取点'],
    #     ]))

    #     # 开右夹子
    #     sequence.add_child(
    #         arm_move_primitive_bt("lift", [
    #         self.waypoints['提起点'],
    #     ]))
    #     # 关右夹子

    #     return tree
    
if __name__ == "__main__":
    behavior = PipetteBehavior('')
    pickup_bt = behavior.pickup()
    print(pickup_bt)
