import time
from gripper_control import Gripper<PERSON>river, GripperError

# 设备配置
IF_DK = False
IP_ADDRESS = "**************"
PORT = 5001
FORCE = 50         # 控制力度
SPEED = 100        # 移动速度（根据实际情况调整）
POSITIONS = [0, 300, 600, 900, 1000]  # 目标位置序列

def main():
    # 创建驱动实例并连接
    try:
        print(f"正在连接夹具 {IP_ADDRESS}:{PORT}...")
        driver = GripperDriver(IP_ADDRESS, PORT, IF_DK)
    except ConnectionError as e:
        print(f"连接失败: {str(e)}")
        return

    # 初始化夹具
    try:
        print("正在初始化夹具...")
        driver.init()
        print("夹具初始化完成")
    except RuntimeError as e:
        print(f"初始化失败: {str(e)}")
        driver.close()
        return

    # 执行位置移动序列
    for idx, target_pos in enumerate(POSITIONS):
        print(f"\n[{idx+1}/{len(POSITIONS)}] 移动到位置 {target_pos}")
        
        try:
            driver.move(FORCE, SPEED, target_pos)
        except Exception as e:
            print(f"移动指令发送失败: {str(e)}")
            break

        # 实时监控移动状态
        try:
            while True:
                driver.update_status()
                state = driver.get_state()
                
                # 错误检查
                if state.error_code != GripperError.NO_ERROR:
                    print(f"! 错误发生: {state.error_message}")
                    break
                
                # 位置反馈
                print(f"当前实际位置: {state.position:4d} | 目标位置: {target_pos:4d}", end='\r')
                
                # 到达判定
                if not driver.is_moving():
                    print("\n到达目标位置")
                    break
                
                time.sleep(0.1)
                
            if state.error_code != GripperError.NO_ERROR:
                break
                
        except Exception as e:
            print(f"状态监控异常: {str(e)}")
            break

        # 等待用户确认
        if idx != len(POSITIONS)-1:
            input("按回车继续下一个位置...")

    # 关闭连接
    driver.close()
    print("\n测试完成，连接已关闭")

if __name__ == "__main__":
    main()
