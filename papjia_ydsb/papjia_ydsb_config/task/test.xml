<root BTCPP_format="4">
    <BehaviorTree ID="TestQucikChange">
		<Sequence>
			<InitMTCTask task_name="TestQucikChange" task="{task}" />
			<CreatePlanner planner_type="lin" planner="{lin_planner}" />
			<CreatePlanner planner_type="ptp" planner="{ptp_planner}" />
			<CreatePlanner planner_type="ompl" planner="{ompl_planner}" />
			<SetupCurrentState task="{task}" />
			<SetupMoveToNamedState task="{task}" planner="{ompl_planner}" planning_group="arm_left" stage_name="MoveToHomePick" named_state="home_left" />

            <GetQuatFromXYZ x="0" y="0" z="0" quaternion="{quaternion}" />
			<GetPoseStampedMsg frame_id="tip10" position="0,0.0,0.1" quaternion="{quaternion}" pose_stamped="{pose1}" />
			<SetupMoveToPose task="{task}" planner="{lin_planner}" planning_group="arm_left" stage_name="stage" ik_frame="arm_left_grasp" pose="{pose1}" />
			<PlanMTCTask task="{task}" />
			<ExecuteMTCTask task="{task}" />
		</Sequence>
	</BehaviorTree>

    <BehaviorTree ID="TestFlask">
		<Sequence>
			<InitMTCTask task_name="TestFlask" task="{task}" />
			<CreatePlanner planner_type="lin" planner="{lin_planner}" />
			<CreatePlanner planner_type="ptp" planner="{ptp_planner}" />
			<SetupCurrentState task="{task}" />
			<SetupMoveToNamedState task="{task}" planner="{ptp_planner}" planning_group="arm_right" stage_name="MoveToHomePick" named_state="home_right" />

			<GetPoseStampedMsg frame_id="flask_top" position="-0.4,0,0.2" quaternion="0,0,0,1" pose_stamped="{pose1}" />
			<SetupMoveToPose task="{task}" planner="{ptp_planner}" planning_group="arm_right" stage_name="stage" ik_frame="arm_right_grasp_out" pose="{pose1}" />
			<GetPoseStampedMsg frame_id="flask_top" position="-0.2,0.4,0.2" quaternion="0,0,0,1" pose_stamped="{pose1}" />
			<SetupMoveToPose task="{task}" planner="{ptp_planner}" planning_group="arm_right" stage_name="stage" ik_frame="arm_right_grasp_out" pose="{pose1}" />
            
			<GetPoseStampedMsg frame_id="flask1" position="0,0,0.3" quaternion="0,0,0,1" pose_stamped="{pose1}" />
			<SetupMoveToPose task="{task}" planner="{ptp_planner}" planning_group="arm_right" stage_name="stage" ik_frame="arm_right_grasp_out" pose="{pose1}" />
			<PlanMTCTask task="{task}" />
			<ExecuteMTCTask task="{task}" />
		</Sequence>
	</BehaviorTree>
    <BehaviorTree ID="TestObjectTestTueb">
		<Sequence>
			<InitMTCTask task_name="TestObjectTestTueb" task="{task}" />
			<CreatePlanner planner_type="lin" planner="{lin_planner}" />
			<CreatePlanner planner_type="ptp" planner="{ptp_planner}" />
			<CreatePlanner planner_type="ompl" planner="{ompl_planner}" />
			<SetupCurrentState task="{task}" />
			<!-- <SetupMoveToNamedState task="{task}" planner="{ompl_planner}" planning_group="arm_right" stage_name="MoveToHomePick" named_state="home_right" />

			<GetPoseStampedMsg frame_id="flask_top" position="-0.4,0,0.2" quaternion="0,0,0,1" pose_stamped="{pose1}" />
			<SetupMoveToPose task="{task}" planner="{ptp_planner}" planning_group="arm_right" stage_name="stage" ik_frame="arm_right_grasp_out" pose="{pose1}" />
			<GetPoseStampedMsg frame_id="flask_top" position="-0.2,0.4,0.2" quaternion="0,0,0,1" pose_stamped="{pose1}" />
			<SetupMoveToPose task="{task}" planner="{ptp_planner}" planning_group="arm_right" stage_name="stage" ik_frame="arm_right_grasp_out" pose="{pose1}" /> -->
            <GetQuatFromXYZ x="0" y="0" z="-90" quaternion="{quaternion}" />
			<GetPoseStampedMsg frame_id="object_test_tube" position="0.1,-0.3,0.4" quaternion="{quaternion}" pose_stamped="{pose1}" />
			<SetupMoveToPose task="{task}" planner="{ptp_planner}" planning_group="arm_right" stage_name="stage" ik_frame="arm_right_grasp_inner" pose="{pose1}" />
			<PlanMTCTask task="{task}" />
			<ExecuteMTCTask task="{task}" />
		</Sequence>
	</BehaviorTree>
</root>