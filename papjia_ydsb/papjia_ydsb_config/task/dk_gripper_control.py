import time
from pymodbus.client import ModbusTcpClient


class MotorDriver:
    """
    大肯RJ28电机驱动，用于控制电爪和旋转模块
    使用ModbusTcpClient进行通信，基于标准ModBus-RTU协议
    """

    def __init__(self, ip_address, port=5001, slave_id=1, timeout=1.0):
        """
        初始化电机驱动
        
        Args:
            ip_address (str): ModbusTCP服务器的IP地址
            port (int): ModbusTCP服务器的端口号，默认为5001
            slave_id (int): 从站ID，默认为1
            timeout (float): 通信超时时间，默认为1.0秒
        """
        self.ip_address = ip_address
        self.port = port
        self.slave_id = slave_id
        self.timeout = timeout
        self.client = None
        self.connect()
        
    def connect(self):
        """
        连接到ModbusTCP服务器
        
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        try:
            self.client = ModbusTcpClient(
                host=self.ip_address,
                port=self.port,
                timeout=self.timeout
            )
            return self.client.connect()
        except Exception as e:
            print(f"连接ModbusTCP服务器失败: {e}")
            return False
            
    def disconnect(self):
        """
        断开与ModbusTCP服务器的连接
        """
        if self.client and self.client.is_socket_open():
            self.client.close()
            
    def __del__(self):
        """
        析构函数，确保断开连接
        """
        self.disconnect()
        
    def _write_register(self, address_high, address_low, value):
        """
        写入寄存器
        
        Args:
            address_high (int): 高字节地址
            address_low (int): 低字节地址
            value (int): 要写入的值
            
        Returns:
            bool: 写入成功返回True，失败返回False
        """
        try:
            # 计算Modbus地址 (高字节*256 + 低字节)
            register_address = (address_high << 8) | address_low
            # 使用功能码06(写单个寄存器)
            response = self.client.write_register(
                address=register_address,
                value=value,
                slave=self.slave_id
            )
            return not response.isError()
        except Exception as e:
            print(f"写入寄存器失败: {e}")
            return False
            
    def _read_register(self, address_high, address_low):
        """
        读取寄存器
        
        Args:
            address_high (int): 高字节地址
            address_low (int): 低字节地址
            
        Returns:
            int: 读取到的值，失败返回None
        """
        try:
            # 计算Modbus地址 (高字节*256 + 低字节)
            register_address = (address_high << 8) | address_low
            # 使用功能码03(读保持寄存器)
            response = self.client.read_holding_registers(
                address=register_address,
                count=1,
                slave=self.slave_id
            )
            if not response.isError():
                return response.registers[0]
            return None
        except Exception as e:
            print(f"读取寄存器失败: {e}")
            return None
    
    # ===== 电爪功能 =====
    
    def init_gripper(self):
        """
        初始化电爪，使电爪寻找原点
        
        Returns:
            bool: 发送命令成功返回True，失败返回False
        """
        return self._write_register(0x01, 0x00, 0x0001)
        
    def query_gripper_init_status(self):
        """
        查询电爪初始化状态
        
        Returns:
            int: 
                0 - 未初始化
                1 - 初始化完成
                2 - 初始化失败
                9 - 初始化中
                None - 查询失败
        """
        return self._read_register(0x02, 0x00)
        
    def set_gripper_torque(self, torque_percent):
        """
        配置电爪的力矩
        
        Args:
            torque_percent (int): 力矩百分比，范围10-100%
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        if not 10 <= torque_percent <= 100:
            print("力矩百分比超出范围，应在10-100%之间")
            return False
        
        return self._write_register(0x01, 0x03, torque_percent)
        
    def get_gripper_torque(self):
        """
        查询电爪当前设定力矩
        
        Returns:
            int: 力矩百分比，范围10-100%，查询失败返回None
        """
        return self._read_register(0x01, 0x03)
        
    def set_gripper_speed(self, speed_percent):
        """
        配置电爪的速度
        
        Args:
            speed_percent (int): 速度百分比，范围5-100%
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        if not 5 <= speed_percent <= 100:
            print("速度百分比超出范围，应在5-100%之间")
            return False
        
        return self._write_register(0x01, 0x04, speed_percent)
        
    def get_gripper_speed(self):
        """
        查询电爪当前设定速度
        
        Returns:
            int: 速度百分比，范围5-100%，查询失败返回None
        """
        return self._read_register(0x01, 0x04)
        
    def set_gripper_position_and_run(self, position_percent):
        """
        配置位置并运行电爪
        
        Args:
            position_percent (int): 位置百分比，范围0-100%
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        if not 0 <= position_percent <= 100:
            print("位置百分比超出范围，应在0-100%之间")
            return False
        
        return self._write_register(0x01, 0x05, position_percent)
        
    def get_gripper_target_position(self):
        """
        查询电爪当前设定位置
        
        Returns:
            int: 位置百分比，范围0-100%，查询失败返回None
            当前模具夹试管闭合到90%左右
        """
        return self._read_register(0x01, 0x05)
        
    def query_gripper_status(self):
        """
        查询电爪运行状态
        
        Returns:
            int: 
                0 - 运动中
                1 - 到达位置
                2 - 正确夹持（张开失败）
                None - 查询失败
        """
        return self._read_register(0x02, 0x02)
        
    def query_gripper_position(self):
        """
        查询电爪实时位置
        
        Returns:
            int: 位置百分比，范围0-100%，查询失败返回None
        """
        return self._read_register(0x02, 0x04)
        
    # ===== 旋转功能 =====
    
    def init_rotation(self, mode=0x0001):
        """
        初始化旋转，使旋转寻找原点
        
        Args:
            mode (int): 
                0x0001 - 寻找原点
                0x0002 - 当前位置为原点
        
        Returns:
            bool: 发送命令成功返回True，失败返回False
        """
        if mode not in [0x0001, 0x0002]:
            print("模式错误，只能是0x0001(寻找原点)或0x0002(当前位置为原点)")
            return False
            
        return self._write_register(0x01, 0x01, mode)
        
    def query_rotation_init_status(self):
        """
        查询旋转初始化状态
        
        Returns:
            int: 
                0 - 未初始化
                1 - 初始化完成
                2 - 初始化失败
                9 - 初始化中
                None - 查询失败
        """
        return self._read_register(0x02, 0x01)
        
    def set_rotation_torque(self, torque_percent):
        """
        配置旋转的转矩
        
        Args:
            torque_percent (int): 转矩百分比，范围10-100%
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        if not 10 <= torque_percent <= 100:
            print("转矩百分比超出范围，应在10-100%之间")
            return False
        
        return self._write_register(0x01, 0x06, torque_percent)
        
    def get_rotation_torque(self):
        """
        查询旋转当前设定转矩
        
        Returns:
            int: 转矩百分比，范围10-100%，查询失败返回None
        """
        return self._read_register(0x01, 0x06)
        
    def set_rotation_speed(self, speed_percent):
        """
        配置旋转的速度
        
        Args:
            speed_percent (int): 速度百分比，范围5-100%
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        if not 5 <= speed_percent <= 100:
            print("速度百分比超出范围，应在5-100%之间")
            return False
        
        return self._write_register(0x01, 0x07, speed_percent)
        
    def get_rotation_speed(self):
        """
        查询旋转当前设定速度
        
        Returns:
            int: 速度百分比，范围5-100%，查询失败返回None
        """
        return self._read_register(0x01, 0x07)
        
    def set_rotation_angle_and_run(self, angle):
        """
        配置角度并运行旋转
        
        Args:
            angle (int): 角度，范围-32768至+32767度
            正数逆时针旋转，负数顺时针旋转
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        if not -32768 <= angle <= 32767:
            print("角度超出范围，应在-32768至+32767度之间")
            return False
        
        # 处理负数
        if angle < 0:
            angle = 65536 + angle  # 转换为2的补码表示
        
        return self._write_register(0x01, 0x08, angle)
        
    def get_rotation_target_angle(self):
        """
        查询旋转当前设定角度
        
        Returns:
            int: 角度，范围-32768至+32767度，查询失败返回None
        """
        value = self._read_register(0x01, 0x08)
        if value is None:
            return None
            
        # 处理2的补码表示转换为有符号整数
        if value > 32767:
            value = value - 65536
            
        return value
        
    def query_rotation_status(self):
        """
        查询旋转运行状态
        
        Returns:
            int: 
                0 - 运动中
                1 - 到达位置
                3 - 旋转过程中堵转
                None - 查询失败
        """
        return self._read_register(0x02, 0x03)
        
    def query_rotation_position(self):
        """
        查询旋转实时位置
        
        Returns:
            int: 角度，范围-32768至+32767度，查询失败返回None
        """
        value = self._read_register(0x02, 0x05)
        if value is None:
            return None
            
        # 处理2的补码表示转换为有符号整数
        if value > 32767:
            value = value - 65536
            
        return value
        
    # ===== 辅助方法 =====
    
    def wait_for_gripper_init(self, timeout=15, check_interval=0.5):
        """
        等待电爪初始化完成
        
        Args:
            timeout (float): 超时时间(秒)
            check_interval (float): 检查间隔(秒)
            
        Returns:
            bool: 初始化成功返回True，失败或超时返回False
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            status = self.query_gripper_init_status()
            if status == 1:  # 初始化完成
                return True
            elif status == 2:  # 初始化失败
                return False
            time.sleep(check_interval)
        return False  # 超时
        
    def wait_for_rotation_init(self, timeout=15, check_interval=0.5):
        """
        等待旋转初始化完成
        
        Args:
            timeout (float): 超时时间(秒)
            check_interval (float): 检查间隔(秒)
            
        Returns:
            bool: 初始化成功返回True，失败或超时返回False
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            status = self.query_rotation_init_status()
            if status == 1:  # 初始化完成
                return True
            elif status == 2:  # 初始化失败
                return False
            time.sleep(check_interval)
        return False  # 超时
        
    def wait_for_gripper_completion(self, timeout=15, check_interval=0.1):
        """
        等待电爪运动完成
        
        Args:
            timeout (float): 超时时间(秒)
            check_interval (float): 检查间隔(秒)
            
        Returns:
            int:
                1 - 到达位置
                2 - 正确夹持（张开失败）
                None - 超时或查询失败
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            status = self.query_gripper_status()
            if status in [1, 2]:  # 运动完成
                return status
            time.sleep(check_interval)
        return None  # 超时
        
    def wait_for_rotation_completion(self, timeout=30, check_interval=0.1):
        """
        等待旋转运动完成
        
        Args:
            timeout (float): 超时时间(秒)
            check_interval (float): 检查间隔(秒)
            
        Returns:
            int:
                1 - 到达位置
                3 - 旋转过程中堵转
                None - 超时或查询失败
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            status = self.query_rotation_status()
            if status in [1, 3]:  # 运动完成
                return status
            time.sleep(check_interval)
        return None  # 超时


# # 使用示例
# if __name__ == "__main__":
#     # 创建MotorDriver实例
#     driver = MotorDriver(ip_address="**************")
    
#     # # 初始化电爪和旋转
#     # print("初始化电爪...")
#     # if driver.init_gripper():
#     #     if driver.wait_for_gripper_init():
#     #         print("电爪初始化成功")
#     #     else:
#     #         print("电爪初始化失败")
    
#     # print("初始化旋转...")
#     # if driver.init_rotation():
#     #     if driver.wait_for_rotation_init():
#     #         print("旋转初始化成功")
#     #     else:
#     #         print("旋转初始化失败")
    
#     # 设置电爪速度和力矩
#     driver.set_gripper_speed(5)
#     driver.set_gripper_torque(20)

#     count = 0
#     while count < 5:
#         count += 1
#         print(f"\n第{count}次")

#         # # 运行电爪到指定位置
#         # if driver.set_gripper_position_and_run(0):
#         #     result = driver.wait_for_gripper_completion()
#         #     if result == 1:
#         #         print("电爪到达指定位置")
#         #     elif result == 2:
#         #         print("电爪正确夹持（张开失败）")
#         #     else:
#         #         print("电爪运动超时或失败")
#         # # 查询当前位置
#         # gripper_pos = driver.query_gripper_position()
#         # print(f"电爪当前位置: {gripper_pos}%")

#         # if driver.set_gripper_position_and_run(100):
#         #     result = driver.wait_for_gripper_completion()
#         #     if result == 1:
#         #         print("电爪到达指定位置")
#         #     elif result == 2:
#         #         print("电爪正确夹持（张开失败）")
#         #     else:
#         #         print("电爪运动超时或失败")
#         # # 查询当前位置
#         # gripper_pos = driver.query_gripper_position()
#         # print(f"电爪当前位置: {gripper_pos}%")
    
    
#         # 设置旋转速度和转矩
#         driver.set_rotation_speed(5)
#         driver.set_rotation_torque(10)
#         # 运行旋转到指定角度
#         if driver.set_rotation_angle_and_run(0):
#             result = driver.wait_for_rotation_completion()
#             if result == 1:
#                 print("旋转到达指定角度")
#             elif result == 3:
#                 print("旋转过程中堵转")
#             else:
#                 print("旋转运动超时或失败")
        
#         rotation_pos = driver.query_rotation_position()
#         print(f"旋转当前角度: {rotation_pos}度")

#         # 运行旋转到指定角度
#         if driver.set_rotation_angle_and_run(360):
#             result = driver.wait_for_rotation_completion()
#             if result == 1:
#                 print("旋转到达指定角度")
#             elif result == 3:
#                 print("旋转过程中堵转")
#             else:
#                 print("旋转运动超时或失败")
        
#         rotation_pos = driver.query_rotation_position()
#         print(f"旋转当前角度: {rotation_pos}度")
        
#     # 断开连接
#     driver.disconnect()