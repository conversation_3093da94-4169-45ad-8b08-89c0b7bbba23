controller_manager:
  ros__parameters:
    update_rate: 100  # Hz

    joint_state_broadcaster:
      type: joint_state_broadcaster/JointStateBroadcaster

    arm_left_controller:
      type: joint_trajectory_controller/JointTrajectoryController

    arm_right_controller:
      type: joint_trajectory_controller/JointTrajectoryController

arm_left_controller:
  ros__parameters:
    joints:
      - arm_left_joint1
      - arm_left_joint2
      - arm_left_joint3
      - arm_left_joint4
      - arm_left_joint5
      - arm_left_joint6
      - arm_left_joint7
    command_interfaces:
      - position
    state_interfaces:
      - position
      - velocity
    
    state_publish_rate: 50.0 # Defaults to 50
    action_monitor_rate: 20.0 # Defaults to 20

    allow_nonzero_velocity_at_trajectory_end: false
    allow_partial_joints_goal: false # Defaults to false
    constraints:
      stopped_velocity_tolerance: 0.01 # Defaults to 0.01
      goal_time: 0.0 # Defaults to 0.0 (start immediately)

arm_right_controller:
  ros__parameters:
    joints:
      - arm_right_joint1
      - arm_right_joint2
      - arm_right_joint3
      - arm_right_joint4
      - arm_right_joint5
      - arm_right_joint6
      - arm_right_joint7
    command_interfaces:
      - position
    state_interfaces:
      - position
      - velocity

    state_publish_rate: 50.0 # Defaults to 50
    action_monitor_rate: 20.0 # Defaults to 20

    allow_nonzero_velocity_at_trajectory_end: false
    allow_partial_joints_goal: false # Defaults to false
    constraints:
      stopped_velocity_tolerance: 0.01 # Defaults to 0.01
      goal_time: 0.0 # Defaults to 0.0 (start immediately)

