                                                                                                  
                                                                                                  visual:
  categories:
    Bridge:
      mesh_path: "package://hand_demo_description/meshes/backup_1_0_item.STL"
      footprint: [[0.10, 0.14], [0.075, 0.15], [0.013, 0.135], [0.09, -0.087], [0.124, -0.087]]  # 长方体 14cm x 7cm
    Cylinder:
      mesh_path: "package://hand_demo_description/meshes/backup_2_1_item.STL"
      footprint: [[0.035, 0.075], [-0.085, 0.055], [-0.085, -0.055], [0.035, -0.07]]  # 近似为5cm x 5cm的正方形
    Plane:
      mesh_path: "package://hand_demo_description/meshes/backup_2_0_item.STL"
      footprint: [[0.04, 0.105], [0.035, -0.12], [0.0, -0.12], [-0.065, 0.105]]  # 长方体 14cm x 7cm
    Stand:
      mesh_path: "package://hand_demo_description/meshes/backup_1_1_item.STL"
      footprint: [[0.035, 0.07], [-0.035, 0.07], [-0.035, -0.07], [0.035, -0.07]]  # 10cm x 10cm的正方形
  instances:
    Cylinder_0:
      footprint: [[0.005, 0.05], [-0.03, 0.05], [-0.03, -0.08], [0.07, -0.08]]
    Cylinder_1:
      footprint: [[0.035, 0.075], [-0.085, 0.055], [-0.085, -0.055], [0.035, -0.07]]

  background:
    backup_1_0:  # 左侧竖直支架
      footprint: [
        [0.025, 0.165],  # 第一个box中心点
        [-0.01, 0.165],
        [-0.01, -0.165],
        [0.025, -0.165],
      ]
      position: [0.24675, 0.29496, 0.0]  # 从右下角(0.24675, 0.12996)调整为中心点坐标
      orientation: [0.0, 0.0, 0.0, 1.0]  # quaternion: x, y, z, w
    backup_1_1:  # 左侧竖直支架
      footprint: [
        [0.145, -0.165],  # 第二个box中心点
        [0.18, -0.165],
        [0.18, 0.165],
        [0.145, 0.165]
      ]
      position: [0.24675, 0.29496, 0.0]  # 从右下角(0.24675, 0.12996)调整为中心点坐标
      orientation: [0.0, 0.0, 0.0, 1.0]  # quaternion: x, y, z, w
    backup_2_0:  # 底部水平支架
      footprint: [
        [0.165, 0.01],  # 第一个box中心点
        [-0.165, 0.01],
        [-0.165, -0.01],
        [0.165, -0.01],
      ]
      position: [0.35675, -0.51504, 0.0]  # 从右下角(0.19175, -0.51504)调整为中心点坐标
      orientation: [0.0, 0.0, 0.0, 1.0]  # quaternion: x, y, z, w
    backup_2_1:  # 底部水平支架
      footprint: [
        [0.165, 0.21],  # 第二个box中心点
        [0.165, 0.23],
        [-0.165, 0.23],
        [-0.165, 0.21]
      ]
      position: [0.35675, -0.51504, 0.0]  # 从右下角(0.19175, -0.51504)调整为中心点坐标
      orientation: [0.0, 0.0, 0.0, 1.0]  # quaternion: x, y, z, w
    setup_1:  # 右上角装置
      footprint: [
        [0.184825, 0.041518],
        [-0.044825, 0.041518],
        [-0.044825, -0.041518],
        [0.184825, -0.041518]
      ]
      position: [0.587556, 0.217926, 0.0]  # 从右下角(0.54934, 0.17641)调整为中心点坐标
      orientation: [0.0, 0.0, 0.0, 1.0]  # quaternion: x, y, z, w
    setup_2:  # 右下角装置
      footprint: [
        [0.107, 0.172143],
        [-0.107, 0.172143],
        [-0.107, -0.172143],
        [0.107, -0.172143]
      ]
      position: [0.61532, -0.10882, 0.0]  # 从右下角(0.72232, 0.06332)调整为中心点坐标
      orientation: [0.0, 0.0, 0.0, 1.0]  # quaternion for PI rotation (180 degrees around Z)
validation:
  required_counts:
    Bridge: 1
    Plane: 1
    Stand: 1
    Cylinder: 2
  pose_limits:
    Bridge_0:  
      x: [0.25, 0.275]
      y: [0.162, 0.423]
      yaw: [-0.2, 0.2] 
      # x: [0.26, 0.288]
      # y: [0.162, 0.423]
      # yaw: [-0.18, 0.18] #最大限度是 0.585，角度变大，xy会变小
      # 长方体距离13, 手看做长14宽7的长方体
    Plane_0:                 # 0.35392798717181817, -0.3922902564564956
      x: [0.240, 0.484]             
      y: [-0.424, -0.390]          # -1.02时y极限大概在-0.405 中间能到-0.396
      yaw: [-2.09, -1.02]
    Stand_0:
      x: [0.50, 0.70]
      y: [-0.5, -0.36] #-0.36对应-0.6
      yaw: [-0.6, 0.785] # -0.3296
    Cylinder_0:
      x: [0.4, 0.8]
      y: [0.36, 0.5]
    Cylinder_1:
      x: [0.6, 0.8]
      y: [-0.5, -0.36]