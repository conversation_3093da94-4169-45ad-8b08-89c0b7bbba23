/**:
    ros__parameters:
        image_transport: compressed    # image format
        family: 36h11           # tag family name
        size: 0.06           # tag edge size in meter
        max_hamming: 0          # maximum allowed hamming distance (corrected bits)

        # see "apriltag.h" 'struct apriltag_detector' for more documentation on these optional parameters
        detector:
            threads: 1          # number of threads
            decimate: 2.0       # decimate resolution for quad detection
            blur: 0.0           # sigma of Gaussian blur for quad detection
            refine: True        # snap to strong gradients
            sharpening: 0.25    # sharpening of decoded images
            debug: False        # write additional debugging images to current working directory

        # optional list of tags
        # tag:
        #     ids: [0, 1]            # tag ID
        #     frames: [base, object]  # optional frame name
        #     sizes: [0.07, 0.07]   # optional tag-specific edge size
