/**:
    ros__parameters:
      video_device: "/dev/video0"
      framerate: 5.0  #30
      io_method: "mmap"
      frame_id: "camera_usb"
      pixel_format: "mjpeg2rgb"  # see usb_cam/supported_formats for list of supported formats  "mjpeg2rgb"  "yuyv"
      av_device_format: "YUV422P"  #"YUV422P"  "RGB"
      image_width: 1920 
      image_height: 1080 
      camera_name: "usb_camera"
      camera_info_url: "package://papjia_ydsb_config/config/calibration.yaml"
      brightness: -1
      contrast: -1
      saturation: -1
      sharpness: -1
      gain: -1
      auto_white_balance: true
      white_balance: 4000
      autoexposure: true
      exposure: 100
      autofocus: false
      focus: -1
