cmake_minimum_required(VERSION 3.8)
project(papjia_ydsb_config)

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(tf2 REQUIRED)

# uncomment the following section in order to fill in
# further dependencies manually.
# find_package(<dependency> REQUIRED)

install(
  DIRECTORY
    config 
    description
    launch
    # task
  DESTINATION 
    share/${PROJECT_NAME}
)

ament_package()
