from launch import LaunchDescription
from launch.substitutions import PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare

from moveit_configs_utils import MoveItConfigsBuilder

def generate_launch_description():
    
    package_name = "papjia_ydsb_config"
    moveit_package_name = "papjia_ydsb_moveit_config"
    robot_name = "robot"
    
    moveit_config = MoveItConfigsBuilder(robot_name, package_name=moveit_package_name).to_moveit_configs()
    
    rviz_config_file = PathJoinSubstitution(
        [FindPackageShare(package_name), "config", "arm.rviz"]
    )
    rviz_node = Node(
        package="rviz2",
        executable="rviz2",
        name="rviz2",
        output="log",
        arguments=["-d", rviz_config_file],
        parameters=[moveit_config.to_dict()],
    )
    
    nodes = [
        rviz_node
    ]

    return LaunchDescription(nodes)
