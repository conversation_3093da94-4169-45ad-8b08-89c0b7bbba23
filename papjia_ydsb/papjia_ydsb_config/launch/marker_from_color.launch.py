import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory
from launch.launch_description_sources import PythonLaunchDescriptionSource

def generate_launch_description():
    # 1. 配置usb_cam的launch参数
    usb_cam_name_arg = DeclareLaunchArgument(
        'usb_cam_name',
        default_value='camera',
        description='Name of the USB camera node'
    )

    usb_cam_param_path_arg = DeclareLaunchArgument(
        'usb_cam_param_path',
        default_value=PathJoinSubstitution([
            get_package_share_directory('papjia_ydsb_config'),
            'config',
            'usbcam_params.yaml'
        ]),
        description='Path to the USB camera parameter file'
    )
    # 2. 配置apriltag_ros的launch参数
    image_rect_arg = DeclareLaunchArgument(
        'image_rect',
        default_value='/camera/image_raw',
        description='Remapped topic for rectified image'
    )

    camera_info_arg = DeclareLaunchArgument(
        'camera_info',
        default_value='/camera/camera_info',
        description='Remapped topic for camera info'
    )

    apriltag_params_file_arg = DeclareLaunchArgument(
        'apriltag_params_file',
        default_value=PathJoinSubstitution([
            get_package_share_directory('papjia_tf_manager'),
            'config',
            'tags_36h11.yaml'
        ]),
        description='Path to the AprilTag parameter file'
    )

    # 3. 配置detect_marker.py的参数
    detect_marker_config_arg = DeclareLaunchArgument(
        'detect_marker_config',
        default_value=PathJoinSubstitution([
            get_package_share_directory('papjia_tf_manager'),
            'config',
            'config_marker.yaml'
        ]),
        description='Path to the marker detection configuration file'
    )

    #4. 启动usb_cam的launch文件
    usb_cam_launch = Node(
        package='usb_cam', executable='usb_cam_node_exe', output='screen',
        name=LaunchConfiguration('usb_cam_name'),
        namespace='camera',
        parameters= [LaunchConfiguration('usb_cam_param_path'),
                    #{'image_transport': "compressed"}
        ],
    )


    # 5. 启动apriltag_ros的launch文件
    apriltag_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                get_package_share_directory('apriltag_ros'),
                'launch',
                'standalone.launch.py'
            ])
        ]),
        launch_arguments={
            'image_rect': LaunchConfiguration('image_rect'),
            'camera_info': LaunchConfiguration('camera_info'),
            'params_file': LaunchConfiguration('apriltag_params_file')
        }.items()
    )

    # 6. 启动detect_marker.py节点
    detect_marker_node = Node(
        package='papjia_tf_manager',
        executable='detect_marker.py',
        name='detect_marker',
        output='screen',
        arguments=[LaunchConfiguration('detect_marker_config')]
    )

    # static_marker_tf = Node(
    #         package="tf2_ros",
    #         executable="static_transform_publisher",
    #         name="hand_left_tf",
    #         arguments=[
    #             "--x", "-0.16",
    #             "--y", "0",
    #             "--z", "0",
    #             "--roll", "0",
    #             "--pitch", "0",
    #             "--yaw", "0",
    #             "--frame-id", "tag",
    #             "--child-frame-id", "root"
    #         ],
    #     )

    # 7. 将所有内容组合到LaunchDescription中
    return LaunchDescription([
        usb_cam_name_arg,
        usb_cam_param_path_arg,
        image_rect_arg,
        camera_info_arg,
        apriltag_params_file_arg,
        detect_marker_config_arg,
        usb_cam_launch,
        apriltag_launch,
        detect_marker_node,
        # static_marker_tf
    ])
