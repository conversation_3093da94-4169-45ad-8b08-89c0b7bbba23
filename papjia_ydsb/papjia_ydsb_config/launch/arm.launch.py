from launch import LaunchDescription
from launch.actions import DeclareLaunch<PERSON><PERSON>ument, RegisterEventHandler, TimerAction, IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.event_handlers import OnProcessExit, OnProcessStart
from launch.substitutions import Command, FindExecutable, LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
import launch_ros.descriptions
from launch.conditions import IfCondition

from moveit_configs_utils import MoveItConfigsBuilder
from moveit_configs_utils.launches import generate_move_group_launch
from moveit_configs_utils import MoveItConfigsBuilder

import os
def generate_launch_description():
    
    package_name = "papjia_ydsb_config"
    moveit_package_name = "papjia_ydsb_moveit_config"
    robot_name = "robot"
    controllers_file = "arm_controllers.yaml"
    robot_description_file = "scene.xacro"
    
    moveit_config = MoveItConfigsBuilder(robot_name, package_name=moveit_package_name).to_moveit_configs()
    
    # Declare arguments
    declared_arguments = []
    declared_arguments.append(
        DeclareLaunchArgument(
            "use_mock_hardware",
            default_value="true",
            description="Start robot with mock hardware mirroring command to its states.",
        )
    )
    declared_arguments.append(
        DeclareLaunchArgument(
            "rviz",
            default_value="true",
            description="Start RViz2 automatically with this launch file.",
        )
    )
    
    #  标定值
    yaml_file_arg = DeclareLaunchArgument(
        'yaml_file',
        default_value=PathJoinSubstitution(
            [FindPackageShare(package_name), 'config', 'tf.yaml']
        ),
        description='Path to the cali.yaml file'
    )
    declared_arguments.append(yaml_file_arg)
    

    yaml_file = LaunchConfiguration("yaml_file")
    use_mock_hardware = LaunchConfiguration("use_mock_hardware")
    rviz = LaunchConfiguration("rviz")
    
    # Get URDF via xacro
    robot_description_content = Command(
        [
            PathJoinSubstitution([FindExecutable(name="xacro")]),
            " ",
            PathJoinSubstitution(
                [FindPackageShare(package_name), "description", "urdf", robot_description_file]
            ),
            " ",
            "use_mock_hardware:=",
            use_mock_hardware,
            " ",
        ]
    )

    robot_description = {"robot_description": launch_ros.descriptions.ParameterValue(robot_description_content, value_type=str)}

    rviz_config_file = PathJoinSubstitution(
        [FindPackageShare(package_name), "config", "arm.rviz"]
    )
    robot_controllers = PathJoinSubstitution(
        [FindPackageShare(package_name), "config", controllers_file]
    )
    
    rviz_node = Node(
        package="rviz2",
        executable="rviz2",
        name="rviz2",
        output="log",
        arguments=["-d", rviz_config_file],
        parameters=[moveit_config.to_dict()],
        condition=IfCondition(rviz),
    )

    robot_state_pub_node = Node(
        package="robot_state_publisher",
        executable="robot_state_publisher",
        output="both",
        parameters=[robot_description],
    )
    
    tf_publisher_node = Node(
        package=package_name,
        executable='tf_publisher.py',
        name='tf_publisher',
        output='screen',
        arguments=[yaml_file]
    )

    ros2_control_node = Node(
        package="controller_manager",
        executable="ros2_control_node",
        output="both",
        parameters=[robot_description, robot_controllers],
    )
    joint_state_broadcaster_spawner = Node(
        package="controller_manager",
        executable="spawner",
        arguments=["joint_state_broadcaster", "--controller-manager", "/controller_manager"],
    )

    robot_controller_names = ["arm_left_controller", "arm_right_controller"]
    robot_controller_spawners = []
    for controller in robot_controller_names:
        robot_controller_spawners += [
            Node(
                package="controller_manager",
                executable="spawner",
                arguments=[controller, "-c", "/controller_manager"],
            )
        ]

    # Delay loading and activation of robot_controller_names after `joint_state_broadcaster`
    delay_robot_controller_spawners_after_joint_state_broadcaster_spawner = []
    for i, controller in enumerate(robot_controller_spawners):
        delay_robot_controller_spawners_after_joint_state_broadcaster_spawner += [
            RegisterEventHandler(
                event_handler=OnProcessExit(
                    target_action=robot_controller_spawners[i - 1]
                    if i > 0
                    else joint_state_broadcaster_spawner,
                    on_exit=[controller],
                )
            )
        ]
    
    move_group_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([FindPackageShare("papjia_ydsb_moveit_config"), '/launch/move_group.launch.py']),
        launch_arguments = {
            'capabilities': 'move_group/ExecuteTaskSolutionCapability'
        }.items(),
    )

    joint_state_manager = Node(
        package="papjia_arm_joint_state_manager",
        executable="joint_state_manager",
        output="both",
        parameters=[{
            'config_path': "/workspace/src/papjia_ydsb/papjia_ydsb_config/config/joint_state.json",
            'service_name': 'handle_joint_state_config',
            'topic_joint_states': '/joint_states',
        }]
    )

    nodes = [
        robot_state_pub_node,
        rviz_node,
        ros2_control_node,
        joint_state_broadcaster_spawner
    ] + delay_robot_controller_spawners_after_joint_state_broadcaster_spawner + [
        move_group_launch,
        joint_state_manager
    ]

    return LaunchDescription(declared_arguments + nodes
    )
