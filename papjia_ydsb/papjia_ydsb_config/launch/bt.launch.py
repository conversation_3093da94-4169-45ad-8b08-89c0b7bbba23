import os

from ament_index_python.packages import get_package_share_directory

from launch import LaunchDescription
from launch_ros.actions import Node

from moveit_configs_utils import MoveItConfigsBuilder

def generate_launch_description():
    
    package_name = "papjia_ydsb_config"
    moveit_package_name = "papjia_ydsb_moveit_config"
    robot_name = "robot"
    
    moveit_config = MoveItConfigsBuilder(robot_name, package_name=moveit_package_name).to_moveit_configs()
    
    bt_plugins = os.path.join(
        get_package_share_directory(package_name),
        'config',
        'bt_plugins.yaml'
        )
    
    return LaunchDescription([
        Node(
            package='papjia_behavior_tree',
            executable='papjia_bt_loader',
            output="screen",
            parameters=[
                bt_plugins,
                moveit_config.to_dict()
            ],
        )
    ])