import os
import yaml
from launch import LaunchDescription
from launch_ros.actions import Node
import launch
import launch_ros
from ament_index_python.packages import get_package_share_directory
from launch_param_builder import ParameterBuilder
from moveit_configs_utils import MoveItConfigsBuilder

def load_yaml(package_name, file_path):
    package_path = get_package_share_directory(package_name)
    absolute_file_path = os.path.join(package_path, file_path)

    try:
        with open(absolute_file_path, "r") as file:
            return yaml.safe_load(file)
    except EnvironmentError:  # parent of IOError, OSError *and* WindowsError where available
        return None

def generate_launch_description():
    
    package_name = "papjia_ydsb_config"
    moveit_package_name = "rm_75_config"
    robot_name = "rm_75_description"
    
    moveit_config = MoveItConfigsBuilder(robot_name, package_name=moveit_package_name).to_moveit_configs()
    
    # Get parameters for the Servo node
    servo_yaml = load_yaml(package_name, "config/servo.yaml")
    servo_params = {"moveit_servo": servo_yaml}

    # This sets the update rate and planning group name for the acceleration limiting filter.
    acceleration_filter_update_period = {"update_period": 0.01}
    planning_group_name = {"planning_group_name": "rm_group"}

    # Launch a standalone Servo node.
    # As opposed to a node component, this may be necessary (for example) if Servo is running on a different PC
    arm_left_servo_node = launch_ros.actions.Node(
        package="moveit_servo",
        executable="servo_node_main",
        name="servo_node",
        parameters=[
            servo_params,
            acceleration_filter_update_period,
            planning_group_name,
            moveit_config.robot_description,
            moveit_config.robot_description_semantic,
            moveit_config.robot_description_kinematics,
            moveit_config.joint_limits,
        ],
        output="screen",
    )
    return launch.LaunchDescription(
        [
            arm_left_servo_node
        ]
    )