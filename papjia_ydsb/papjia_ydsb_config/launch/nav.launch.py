import os

from launch.actions import Declar<PERSON><PERSON>aunch<PERSON><PERSON>ument, RegisterEventHandler, TimerAction, IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.event_handlers import OnProcessExit, OnProcessStart
from launch.substitutions import Command, FindExecutable, LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
import launch_ros.descriptions
from launch.conditions import IfCondition
from ament_index_python.packages import get_package_share_directory

from launch import LaunchDescription
from launch_ros.actions import Node


def generate_launch_description():
    
    package_name = "papjia_ydsb_config"
    moveit_package_name = "papjia_ydsb_moveit_config"
    robot_name = "robot"
    
    bt_plugins = os.path.join(
        get_package_share_directory(package_name),
        'config',
        'bt_plugins.yaml'
        )
    
    nav_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([FindPackageShare("nav2_bringup"), '/launch/bringup_launch.py']),
        launch_arguments = {
            'map': '/workspace/src/papjia_ydsb/map/lab-0210.yaml',
            'params_file': '/workspace/src/papjia_car_driver/navigation/config/nav2_params.yaml'
        }.items(),
    )
    
    rviz_node = Node(
        package="rviz2",
        executable="rviz2",
        name="rviz2",
        output="log",
        arguments=["-d", '/workspace/src/papjia_ydsb/map/nav2_lab_view.rviz',],
    )

    return LaunchDescription([
        nav_launch,
        rviz_node
    ])
