#!/usr/bin/env python3

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import Command, FindExecutable, LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch.actions import ExecuteProcess
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory
import launch_ros.descriptions
import yaml
import os

def load_yaml(package_name, file_path):
    package_path = get_package_share_directory(package_name)
    absolute_file_path = os.path.join(package_path, file_path)

    try:
        with open(absolute_file_path, "r") as file:
            return yaml.safe_load(file)
    except EnvironmentError:  # parent of IOError, OSError *and* WindowsError where available
        return None
    
def generate_launch_description():
    
    # Initialize Arguments
    description_package = "papjia_ydsb_config"
    
    declared_arguments = []
    
    declared_arguments.append(DeclareLaunchArgument("urdf", default_value="scene.xacro"))

    yaml_file_arg = DeclareLaunchArgument(
        'yaml_file',
        default_value=PathJoinSubstitution(
            [FindPackageShare(description_package), 'config', 'cali.yaml']
        ),
        description='Path to the cali.yaml file'
    )
    declared_arguments.append(yaml_file_arg)
    
    urdf = LaunchConfiguration("urdf")
    yaml_file = LaunchConfiguration("yaml_file")

    # Get URDF via xacro
    robot_description_content = Command(
        [
            PathJoinSubstitution([FindExecutable(name="xacro")]),
            " ",
            PathJoinSubstitution(
                [FindPackageShare(description_package), "description", "urdf", urdf]
            ),
            " ",            
        ]
    )

    robot_description = {"robot_description": launch_ros.descriptions.ParameterValue(robot_description_content, value_type=str)}

    rviz_config_file = PathJoinSubstitution(
        [FindPackageShare(description_package), "rvizs", "view_robot.rviz"]
    )

    joint_state_publisher_node = Node(
        package="joint_state_publisher_gui",
        executable="joint_state_publisher_gui",
    )
    robot_state_publisher = Node(
        package="robot_state_publisher",
        executable="robot_state_publisher",
        output="both",
        parameters=[robot_description],
    )

    tf_publisher_node = Node(
        package= description_package,
        executable='tf_publisher.py',
        name='tf_publisher',
        output='screen',
        arguments=[yaml_file]
    )

    rviz = Node(
        package="rviz2",
        executable="rviz2",
        name="rviz2",
        output="log",
        arguments=["-d", rviz_config_file],
    )

    nodes = [
        joint_state_publisher_node,
        robot_state_publisher,
        # tf_publisher_node,
        rviz,
    ]
    
    return LaunchDescription(declared_arguments + nodes)
