<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>papjia_ydsb_moveit_config</name>
  <version>0.3.0</version>
  <description>
     An automatically generated package with all the configuration and launch files for using the robot with the MoveIt Motion Planning Framework
  </description>
  <maintainer email="<EMAIL>">ygao</maintainer>

  <license>BSD</license>

  <url type="website">http://moveit.ros.org/</url>
  <url type="bugtracker">https://github.com/ros-planning/moveit2/issues</url>
  <url type="repository">https://github.com/ros-planning/moveit2</url>

  <author email="<EMAIL>">ygao</author>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <exec_depend>moveit_ros_move_group</exec_depend>
  <exec_depend>moveit_kinematics</exec_depend>
  <exec_depend>moveit_planners</exec_depend>
  <exec_depend>moveit_simple_controller_manager</exec_depend>
  <exec_depend>joint_state_publisher</exec_depend>
  <exec_depend>joint_state_publisher_gui</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>xacro</exec_depend>
  <!-- The next 2 packages are required for the gazebo simulation.
       We don't include them by default to prevent installing gazebo and all its dependencies. -->
  <!-- <exec_depend>joint_trajectory_controller</exec_depend> -->
  <!-- <exec_depend>gazebo_ros_control</exec_depend> -->
  <exec_depend>controller_manager</exec_depend>
  <exec_depend>moveit_configs_utils</exec_depend>
  <exec_depend>moveit_ros_move_group</exec_depend>
  <exec_depend>moveit_ros_visualization</exec_depend>
  <exec_depend>moveit_ros_warehouse</exec_depend>
  <exec_depend>moveit_setup_assistant</exec_depend>
  <exec_depend>papjia_ydsb_config</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>
  <exec_depend>rviz2</exec_depend>
  <exec_depend>rviz_common</exec_depend>
  <exec_depend>rviz_default_plugins</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>warehouse_ros_mongo</exec_depend>
  <exec_depend>xacro</exec_depend>


  <export>
      <build_type>ament_cmake</build_type>
  </export>
</package>
