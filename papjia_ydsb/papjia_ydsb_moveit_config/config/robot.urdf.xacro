<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="robot">
    <xacro:arg name="initial_positions_file" default="initial_positions.yaml" />

    <!-- Import robot urdf file -->
    <xacro:include filename="$(find papjia_ydsb_config)/description/urdf/scene.xacro" />

    <!-- Import control_xacro -->
    <xacro:include filename="robot.ros2_control.xacro" />


    <xacro:robot_ros2_control name="FakeSystem" initial_positions_file="$(arg initial_positions_file)"/>

</robot>
