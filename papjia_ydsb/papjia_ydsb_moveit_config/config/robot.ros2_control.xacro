<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    <xacro:macro name="robot_ros2_control" params="name initial_positions_file">
        <xacro:property name="initial_positions" value="${load_yaml(initial_positions_file)['initial_positions']}"/>

        <ros2_control name="${name}" type="system">
            <hardware>
                <!-- By default, set up controllers for simulation. This won't work on real hardware -->
                <plugin>mock_components/GenericSystem</plugin>
            </hardware>
            <joint name="arm_left_joint1">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_left_joint1']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_left_joint2">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_left_joint2']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_left_joint3">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_left_joint3']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_left_joint4">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_left_joint4']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_left_joint5">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_left_joint5']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_left_joint6">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_left_joint6']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_left_joint7">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_left_joint7']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_right_joint1">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_right_joint1']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_right_joint2">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_right_joint2']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_right_joint3">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_right_joint3']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_right_joint4">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_right_joint4']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_right_joint5">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_right_joint5']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_right_joint6">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_right_joint6']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>
            <joint name="arm_right_joint7">
                <command_interface name="position"/>
                <state_interface name="position">
                  <param name="initial_value">${initial_positions['arm_right_joint7']}</param>
                </state_interface>
                <state_interface name="velocity"/>
            </joint>

        </ros2_control>
    </xacro:macro>
</robot>
