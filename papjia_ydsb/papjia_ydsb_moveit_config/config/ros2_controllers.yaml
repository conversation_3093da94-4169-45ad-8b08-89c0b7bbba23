# This config file is used by ros2_control
controller_manager:
  ros__parameters:
    update_rate: 100  # Hz

    arm_left_controller:
      type: joint_trajectory_controller/JointTrajectoryController


    arm_right_controller:
      type: joint_trajectory_controller/JointTrajectoryController


    joint_state_broadcaster:
      type: joint_state_broadcaster/JointStateBroadcaster

arm_left_controller:
  ros__parameters:
    joints:
      - arm_left_joint1
      - arm_left_joint2
      - arm_left_joint3
      - arm_left_joint4
      - arm_left_joint5
      - arm_left_joint6
      - arm_left_joint7
    command_interfaces:
      - position
    state_interfaces:
      - position
      - velocity
arm_right_controller:
  ros__parameters:
    joints:
      - arm_right_joint1
      - arm_right_joint2
      - arm_right_joint3
      - arm_right_joint4
      - arm_right_joint5
      - arm_right_joint6
      - arm_right_joint7
    command_interfaces:
      - position
    state_interfaces:
      - position
      - velocity