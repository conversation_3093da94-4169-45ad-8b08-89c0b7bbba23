<?xml version="1.0" encoding="UTF-8"?>
<!--This does not replace URDF, and is not an extension of URDF.
    This is a format for representing semantic information about the robot structure.
    A URDF file must exist for this robot as well, where the joints and the links that are referenced are defined
-->
<robot name="robot">
    <!--GROUPS: Representation of a set of joints and links. This can be useful for specifying DOF to plan for, defining arms, end effectors, etc-->
    <!--LINKS: When a link is specified, the parent joint of that link (if it exists) is automatically included-->
    <!--JOINTS: When a joint is specified, the child link of that joint (which will always exist) is automatically included-->
    <!--CHAINS: When a chain is specified, all the links along the chain (including endpoints) are included in the group. Additionally, all the joints that are parents to included links are also included. This means that joints along the chain and the parent joint of the base link are included in the group-->
    <!--SUBGROUPS: Groups can also be formed by referencing to already defined group names-->
    <group name="arm_left">
        <joint name="arm_left_joint1"/>
        <joint name="arm_left_joint2"/>
        <joint name="arm_left_joint3"/>
        <joint name="arm_left_joint4"/>
        <joint name="arm_left_joint5"/>
        <joint name="arm_left_joint6"/>
        <joint name="arm_left_joint7"/>
    </group>
    <group name="arm_right">
        <joint name="arm_right_joint1"/>
        <joint name="arm_right_joint2"/>
        <joint name="arm_right_joint3"/>
        <joint name="arm_right_joint4"/>
        <joint name="arm_right_joint5"/>
        <joint name="arm_right_joint6"/>
        <joint name="arm_right_joint7"/>
    </group>
    <!--GROUP STATES: Purpose: Define a named state for a particular group, in terms of joint values. This is useful to define states like 'folded arms'-->
    <group_state name="start_left" group="arm_left">
        <joint name="arm_left_joint1" value="0"/>
        <joint name="arm_left_joint2" value="0.7"/>
        <joint name="arm_left_joint3" value="0"/>
        <joint name="arm_left_joint4" value="-1.5708"/>
        <joint name="arm_left_joint5" value="0"/>
        <joint name="arm_left_joint6" value="-0.7"/>
        <joint name="arm_left_joint7" value="0"/>
    </group_state>
    <group_state name="start_right" group="arm_right">
        <joint name="arm_right_joint1" value="0"/>
        <joint name="arm_right_joint2" value="0.7"/>
        <joint name="arm_right_joint3" value="0"/>
        <joint name="arm_right_joint4" value="-1.5708"/>
        <joint name="arm_right_joint5" value="0"/>
        <joint name="arm_right_joint6" value="-0.7"/>
        <joint name="arm_right_joint7" value="0"/>
    </group_state>
    <!--DISABLE COLLISIONS: By default it is assumed that any link of the robot could potentially come into collision with any other link in the robot. This tag disables collision checking between a specified pair of links. -->
    <disable_collisions link1="arm_left_base_link" link2="arm_left_link1" reason="Adjacent"/>
    <disable_collisions link1="arm_left_base_link" link2="arm_left_link2" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="arm_left_link3" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="arm_left_link4" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="arm_right_base_link" reason="Adjacent"/>
    <disable_collisions link1="arm_left_base_link" link2="arm_right_link1" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="arm_right_link2" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="arm_right_link3" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="arm_right_link4" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="box_a_back" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="box_a_bottom" reason="Adjacent"/>
    <disable_collisions link1="arm_left_base_link" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="box_a_left" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="box_b_bottom" reason="Adjacent"/>
    <disable_collisions link1="arm_left_base_link" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_left_base_link" link2="plane" reason="Adjacent"/>
    <disable_collisions link1="arm_left_finger_center" link2="arm_left_gripper_base" reason="Adjacent"/>
    <disable_collisions link1="arm_left_finger_center" link2="arm_left_left_finger" reason="Default"/>
    <disable_collisions link1="arm_left_finger_center" link2="arm_left_link3" reason="Never"/>
    <disable_collisions link1="arm_left_finger_center" link2="arm_left_link4" reason="Never"/>
    <disable_collisions link1="arm_left_finger_center" link2="arm_left_link5" reason="Never"/>
    <disable_collisions link1="arm_left_finger_center" link2="arm_left_link6" reason="Never"/>
    <disable_collisions link1="arm_left_finger_center" link2="arm_left_link7" reason="Never"/>
    <disable_collisions link1="arm_left_finger_center" link2="arm_left_right_finger" reason="Default"/>
    <disable_collisions link1="arm_left_finger_center" link2="arm_right_link7" reason="Never"/>
    <disable_collisions link1="arm_left_finger_center" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_left_finger_center" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_finger_center" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_left_finger_center" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_left_finger_center" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_left_gripper_base" link2="arm_left_left_finger" reason="Adjacent"/>
    <disable_collisions link1="arm_left_gripper_base" link2="arm_left_link3" reason="Never"/>
    <disable_collisions link1="arm_left_gripper_base" link2="arm_left_link4" reason="Never"/>
    <disable_collisions link1="arm_left_gripper_base" link2="arm_left_link5" reason="Never"/>
    <disable_collisions link1="arm_left_gripper_base" link2="arm_left_link6" reason="Never"/>
    <disable_collisions link1="arm_left_gripper_base" link2="arm_left_link7" reason="Adjacent"/>
    <disable_collisions link1="arm_left_gripper_base" link2="arm_left_right_finger" reason="Adjacent"/>
    <disable_collisions link1="arm_left_gripper_base" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_left_gripper_base" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_gripper_base" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_left_gripper_base" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_left_gripper_base" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_left_left_finger" link2="arm_left_link3" reason="Never"/>
    <disable_collisions link1="arm_left_left_finger" link2="arm_left_link4" reason="Never"/>
    <disable_collisions link1="arm_left_left_finger" link2="arm_left_link5" reason="Never"/>
    <disable_collisions link1="arm_left_left_finger" link2="arm_left_link6" reason="Never"/>
    <disable_collisions link1="arm_left_left_finger" link2="arm_left_link7" reason="Never"/>
    <disable_collisions link1="arm_left_left_finger" link2="arm_left_right_finger" reason="Never"/>
    <disable_collisions link1="arm_left_left_finger" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_left_left_finger" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_left_finger" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_left_left_finger" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_left_left_finger" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="arm_left_link2" reason="Adjacent"/>
    <disable_collisions link1="arm_left_link1" link2="arm_left_link3" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="arm_left_link4" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="arm_left_link5" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="arm_right_base_link" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="arm_right_link1" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="arm_right_link2" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="arm_right_link3" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="arm_right_link4" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="arm_right_link5" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="arm_right_link6" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="arm_right_link7" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="box_a_back" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="box_a_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="box_a_left" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_left_link1" link2="plane" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="arm_left_link3" reason="Adjacent"/>
    <disable_collisions link1="arm_left_link2" link2="arm_left_link4" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="arm_left_link5" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="arm_right_base_link" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="arm_right_link1" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="arm_right_link2" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="arm_right_link3" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="arm_right_link4" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="arm_right_link5" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="arm_right_link6" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="arm_right_link7" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="box_a_back" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="box_a_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="box_a_left" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_left_link2" link2="plane" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="arm_left_link4" reason="Adjacent"/>
    <disable_collisions link1="arm_left_link3" link2="arm_left_link5" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="arm_left_link6" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="arm_left_link7" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="arm_left_right_finger" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="arm_right_base_link" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="arm_right_link1" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="arm_right_link2" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="arm_right_link3" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="arm_right_link4" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="box_a_back" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="box_a_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="box_a_left" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_left_link3" link2="plane" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="arm_left_link5" reason="Adjacent"/>
    <disable_collisions link1="arm_left_link4" link2="arm_left_link6" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="arm_left_link7" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="arm_left_right_finger" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="arm_right_base_link" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="arm_right_link1" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="arm_right_link2" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="arm_right_link3" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="arm_right_link4" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="box_a_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_left_link4" link2="plane" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="arm_left_link6" reason="Adjacent"/>
    <disable_collisions link1="arm_left_link5" link2="arm_left_link7" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="arm_left_right_finger" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="arm_right_link1" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="arm_right_link2" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="arm_right_link3" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="box_a_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_left_link5" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_left_link6" link2="arm_left_link7" reason="Adjacent"/>
    <disable_collisions link1="arm_left_link6" link2="arm_left_right_finger" reason="Never"/>
    <disable_collisions link1="arm_left_link6" link2="arm_right_link1" reason="Never"/>
    <disable_collisions link1="arm_left_link6" link2="arm_right_link2" reason="Never"/>
    <disable_collisions link1="arm_left_link6" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_left_link6" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="arm_left_link6" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_left_link6" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_link6" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_left_link6" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_left_link6" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_left_link7" link2="arm_left_right_finger" reason="Never"/>
    <disable_collisions link1="arm_left_link7" link2="arm_right_link1" reason="Never"/>
    <disable_collisions link1="arm_left_link7" link2="arm_right_link2" reason="Never"/>
    <disable_collisions link1="arm_left_link7" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_left_link7" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_left_link7" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_link7" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_left_link7" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_left_link7" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_left_right_finger" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_left_right_finger" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_left_right_finger" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_left_right_finger" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_left_right_finger" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_right_base_link" link2="arm_right_link1" reason="Adjacent"/>
    <disable_collisions link1="arm_right_base_link" link2="arm_right_link2" reason="Never"/>
    <disable_collisions link1="arm_right_base_link" link2="arm_right_link3" reason="Never"/>
    <disable_collisions link1="arm_right_base_link" link2="arm_right_link4" reason="Never"/>
    <disable_collisions link1="arm_right_base_link" link2="box_a_back" reason="Never"/>
    <disable_collisions link1="arm_right_base_link" link2="box_a_bottom" reason="Adjacent"/>
    <disable_collisions link1="arm_right_base_link" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_right_base_link" link2="box_a_left" reason="Never"/>
    <disable_collisions link1="arm_right_base_link" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="arm_right_base_link" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_right_base_link" link2="box_b_bottom" reason="Adjacent"/>
    <disable_collisions link1="arm_right_base_link" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_right_base_link" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_right_base_link" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_right_base_link" link2="plane" reason="Adjacent"/>
    <disable_collisions link1="arm_right_hand" link2="arm_right_link4" reason="Never"/>
    <disable_collisions link1="arm_right_hand" link2="arm_right_link5" reason="Never"/>
    <disable_collisions link1="arm_right_hand" link2="arm_right_link6" reason="Never"/>
    <disable_collisions link1="arm_right_hand" link2="arm_right_link7" reason="Adjacent"/>
    <disable_collisions link1="arm_right_hand" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_right_hand" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_hand" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_right_hand" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_right_hand" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="arm_right_link2" reason="Adjacent"/>
    <disable_collisions link1="arm_right_link1" link2="arm_right_link3" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="arm_right_link4" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="arm_right_link5" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="box_a_back" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="box_a_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="box_a_left" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_right_link1" link2="plane" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="arm_right_link3" reason="Adjacent"/>
    <disable_collisions link1="arm_right_link2" link2="arm_right_link4" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="arm_right_link5" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="box_a_back" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="box_a_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="box_a_left" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_right_link2" link2="plane" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="arm_right_link4" reason="Adjacent"/>
    <disable_collisions link1="arm_right_link3" link2="arm_right_link5" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="arm_right_link6" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="arm_right_link7" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="box_a_back" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="box_a_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="box_a_left" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_right_link3" link2="plane" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="arm_right_link5" reason="Adjacent"/>
    <disable_collisions link1="arm_right_link4" link2="arm_right_link6" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="arm_right_link7" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="box_a_back" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="box_a_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="box_a_left" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_right_link4" link2="plane" reason="Never"/>
    <disable_collisions link1="arm_right_link5" link2="arm_right_link6" reason="Adjacent"/>
    <disable_collisions link1="arm_right_link5" link2="arm_right_link7" reason="Never"/>
    <disable_collisions link1="arm_right_link5" link2="box_a_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_link5" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_right_link5" link2="box_a_left" reason="Never"/>
    <disable_collisions link1="arm_right_link5" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_right_link5" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_link5" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_right_link5" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_right_link5" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_right_link6" link2="arm_right_link7" reason="Adjacent"/>
    <disable_collisions link1="arm_right_link6" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_right_link6" link2="box_a_left" reason="Never"/>
    <disable_collisions link1="arm_right_link6" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_right_link6" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_link6" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_right_link6" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_right_link6" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="arm_right_link7" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="arm_right_link7" link2="box_a_left" reason="Never"/>
    <disable_collisions link1="arm_right_link7" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="arm_right_link7" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="arm_right_link7" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="arm_right_link7" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="arm_right_link7" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="box_a_back" link2="box_a_bottom" reason="Adjacent"/>
    <disable_collisions link1="box_a_back" link2="box_a_front" reason="Never"/>
    <disable_collisions link1="box_a_back" link2="box_a_left" reason="Default"/>
    <disable_collisions link1="box_a_back" link2="box_a_right" reason="Default"/>
    <disable_collisions link1="box_a_back" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="box_a_back" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="box_a_back" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="box_a_back" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="box_a_back" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="box_a_back" link2="plane" reason="Never"/>
    <disable_collisions link1="box_a_bottom" link2="box_a_front" reason="Adjacent"/>
    <disable_collisions link1="box_a_bottom" link2="box_a_left" reason="Adjacent"/>
    <disable_collisions link1="box_a_bottom" link2="box_a_right" reason="Adjacent"/>
    <disable_collisions link1="box_a_bottom" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="box_a_bottom" link2="box_b_bottom" reason="Adjacent"/>
    <disable_collisions link1="box_a_bottom" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="box_a_bottom" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="box_a_bottom" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="box_a_bottom" link2="plane" reason="Adjacent"/>
    <disable_collisions link1="box_a_front" link2="box_a_left" reason="Default"/>
    <disable_collisions link1="box_a_front" link2="box_a_right" reason="Default"/>
    <disable_collisions link1="box_a_front" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="box_a_front" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="box_a_front" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="box_a_front" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="box_a_front" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="box_a_front" link2="plane" reason="Never"/>
    <disable_collisions link1="box_a_left" link2="box_a_right" reason="Never"/>
    <disable_collisions link1="box_a_left" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="box_a_left" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="box_a_left" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="box_a_left" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="box_a_left" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="box_a_left" link2="plane" reason="Never"/>
    <disable_collisions link1="box_a_right" link2="box_b_back" reason="Never"/>
    <disable_collisions link1="box_a_right" link2="box_b_bottom" reason="Never"/>
    <disable_collisions link1="box_a_right" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="box_a_right" link2="box_b_left" reason="Never"/>
    <disable_collisions link1="box_a_right" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="box_a_right" link2="plane" reason="Never"/>
    <disable_collisions link1="box_b_back" link2="box_b_bottom" reason="Adjacent"/>
    <disable_collisions link1="box_b_back" link2="box_b_front" reason="Never"/>
    <disable_collisions link1="box_b_back" link2="box_b_left" reason="Default"/>
    <disable_collisions link1="box_b_back" link2="box_b_right" reason="Default"/>
    <disable_collisions link1="box_b_back" link2="plane" reason="Default"/>
    <disable_collisions link1="box_b_bottom" link2="box_b_front" reason="Adjacent"/>
    <disable_collisions link1="box_b_bottom" link2="box_b_left" reason="Adjacent"/>
    <disable_collisions link1="box_b_bottom" link2="box_b_right" reason="Adjacent"/>
    <disable_collisions link1="box_b_bottom" link2="plane" reason="Adjacent"/>
    <disable_collisions link1="box_b_front" link2="box_b_left" reason="Default"/>
    <disable_collisions link1="box_b_front" link2="box_b_right" reason="Default"/>
    <disable_collisions link1="box_b_front" link2="plane" reason="Default"/>
    <disable_collisions link1="box_b_left" link2="box_b_right" reason="Never"/>
    <disable_collisions link1="box_b_left" link2="plane" reason="Default"/>
    <disable_collisions link1="box_b_right" link2="plane" reason="Default"/>
</robot>