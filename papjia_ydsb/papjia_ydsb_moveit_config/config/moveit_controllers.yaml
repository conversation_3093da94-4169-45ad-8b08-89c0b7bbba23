# MoveIt uses this configuration for controller management

moveit_controller_manager: moveit_simple_controller_manager/MoveItSimpleControllerManager

moveit_simple_controller_manager:
  controller_names:
    - arm_left_controller
    - arm_right_controller

  arm_left_controller:
    type: FollowJointTrajectory
    action_ns: follow_joint_trajectory
    default: true
    joints:
      - arm_left_joint1
      - arm_left_joint2
      - arm_left_joint3
      - arm_left_joint4
      - arm_left_joint5
      - arm_left_joint6
      - arm_left_joint7
    action_ns: follow_joint_trajectory
    default: true
  arm_right_controller:
    type: FollowJointTrajectory
    action_ns: follow_joint_trajectory
    default: true
    joints:
      - arm_right_joint1
      - arm_right_joint2
      - arm_right_joint3
      - arm_right_joint4
      - arm_right_joint5
      - arm_right_joint6
      - arm_right_joint7
    action_ns: follow_joint_trajectory
    default: true