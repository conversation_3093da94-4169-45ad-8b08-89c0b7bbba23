# 路径配置：指定工作空间和docker compose目录
paths:
  # 项目工作空间的根目录，用于容器内的路径映射
  workspace: "/home/<USER>/work/src/papjia_ydsb/papjia_ydsb/"
  # docker-compose.yml 文件所在的目录
  docker_compose: "/home/<USER>/work/src/papjia_ydsb/papjia_ydsb/docker"

# 命令模板：定义如何执行不同类型的命令
command_templates:
  # Docker Compose相关的命令模板
  compose:
    # 基础命令模板，用于执行docker compose命令
    base: "{command}"
    # 在容器中执行命令的模板（不带交互）
    exec: "docker compose exec {service} {command}"
    # 在容器中执行命令的模板（带交互终端）
    exec_it: "docker compose exec -it {service} {command}"
  # 容器内命令的模板
  container:
    # 设置ROS2环境并执行命令
    base: "bash -ic 'export ROS_DOMAIN_ID=17 && source /workspace/install/setup.bash && {command}'"

# 终端配置：指定使用的终端模拟器
terminal:
  preferred: "gnome-terminal"  # 首选终端
  fallback: "xterm"  # 备选终端

# Docker Compose 命令集合：定义可执行的compose命令
compose_commands:
  up:  # 启动compose服务
    command: "docker compose up"  # 要执行的命令
    auto_start: true  # 是否在start-all时自动启动
    terminal: true  # 是否在新终端窗口中运行
    terminal_title: "Docker Compose - Up"  # 终端窗口标题

# 容器配置：定义所有需要管理的容器
containers:
  # hand_demo: 服务名称，用于docker compose命令
  papjia_ydsb:
    container_name: "papjia_ydsb_container"  # 运行时容器名称
    programs:  # 容器内可执行的程序列表
      simulation:  
        command: "ros2 launch papjia_ydsb_config arm.launch.py"  # 要执行的命令
        auto_start: true 
        terminal: true  
        terminal_title: "YDSB - Simulation"
      real:  
        command: "ros2 launch papjia_ydsb_config arm.launch.py use_mock_hardware:=false"  # 要执行的命令
        auto_start: false  
        terminal: true  
        terminal_title: "YDSB - Real"  
      bt:  
        command: "ros2 launch papjia_ydsb_config bt.launch.py"  # 要执行的命令
        auto_start: true 
        terminal: true  
        terminal_title: "YDSB - BT"
      camera:  
        command: "camera_orbbec"  # 要执行的命令
        auto_start: false 
        terminal: true  
        terminal_title: "YDSB - Camera"
      vision:  
        command: "ros2 launch papjia_ydsb_config pose.launch.py"  # 要执行的命令
        auto_start: false 
        terminal: true  
        terminal_title: "YDSB - Vision"
      mock_vision:  # 应用程序
        command: "ros2 run hand_demo_task mock_vision_server.py"
        auto_start: false
        terminal: true
        terminal_title: "YDSB - Mock Vision"
      pause:
        command: "ros2 run rqt_service_caller rqt_service_caller"
        auto_start: false
        terminal: true
        terminal_title: "YDSB - Pause"
      camera_usb:  
        command: "ros2 launch papjia_tf_manager marker.launch.py"  # 要执行的命令
        auto_start: false 
        terminal: true  
        terminal_title: "YDSB - Camera_usb"
      shell:  # 交互式shell
        command: "bash"
        auto_start: true
        terminal: true
        terminal_title: "YDSB - Shell"

